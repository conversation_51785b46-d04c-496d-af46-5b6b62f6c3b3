<?php
/**
 * Direct API Test Script
 * Test mobile search API directly without browser
 */

echo "<h2>🔍 Mobile Search API Test</h2>";

// Test URLs
$testUrls = [
    'Debug API' => 'http://localhost/thuenhadanang/api/mobile-search-debug.php?keyword=test',
    'Main API' => 'http://localhost/thuenhadanang/api/mobile-search.php?keyword=test',
    'Desktop API' => 'http://localhost/thuenhadanang/api/search.php?keyword=test'
];

foreach ($testUrls as $name => $url) {
    echo "<h3>Testing: $name</h3>";
    echo "<p><strong>URL:</strong> $url</p>";
    
    // Initialize cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'X-Requested-With: XMLHttpRequest'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
    echo "<p><strong>Content Type:</strong> $contentType</p>";
    
    if ($error) {
        echo "<p style='color: red;'><strong>cURL Error:</strong> $error</p>";
    }
    
    if ($response) {
        echo "<p><strong>Response Length:</strong> " . strlen($response) . " bytes</p>";
        
        // Check if it's JSON
        $json = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<p style='color: green;'><strong>✅ Valid JSON Response</strong></p>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
            echo htmlspecialchars(json_encode($json, JSON_PRETTY_PRINT));
            echo "</pre>";
        } else {
            echo "<p style='color: red;'><strong>❌ Invalid JSON Response</strong></p>";
            echo "<p><strong>JSON Error:</strong> " . json_last_error_msg() . "</p>";
            echo "<pre style='background: #ffe6e6; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
            echo htmlspecialchars(substr($response, 0, 1000));
            if (strlen($response) > 1000) {
                echo "\n... (truncated)";
            }
            echo "</pre>";
        }
    } else {
        echo "<p style='color: red;'><strong>❌ No Response</strong></p>";
    }
    
    echo "<hr>";
}

// Test file existence
echo "<h3>File Existence Check</h3>";
$files = [
    'Debug API' => __DIR__ . '/api/mobile-search-debug.php',
    'Main API' => __DIR__ . '/api/mobile-search.php',
    'Desktop API' => __DIR__ . '/api/search.php',
    'SearchApiController' => __DIR__ . '/app/controllers/SearchApiController.php',
    'MobileSearchController' => __DIR__ . '/app/controllers/MobileSearchController.php',
    'Property Model' => __DIR__ . '/app/models/Property.php'
];

foreach ($files as $name => $file) {
    $exists = file_exists($file);
    $readable = $exists ? is_readable($file) : false;
    $status = $exists ? ($readable ? '✅ OK' : '⚠️ Not Readable') : '❌ Not Found';
    
    echo "<p><strong>$name:</strong> $status</p>";
    if ($exists) {
        echo "<p style='margin-left: 20px; color: #666; font-size: 0.9em;'>$file</p>";
    }
}

echo "<hr>";

// Test .htaccess rules
echo "<h3>.htaccess Rules Check</h3>";
$htaccessFile = __DIR__ . '/.htaccess';
if (file_exists($htaccessFile)) {
    $content = file_get_contents($htaccessFile);
    $rules = [
        'mobile-search-debug' => 'api/mobile-search-debug',
        'mobile-search' => 'api/mobile-search',
        'search' => 'api/search'
    ];
    
    foreach ($rules as $name => $pattern) {
        $found = strpos($content, $pattern) !== false;
        $status = $found ? '✅ Found' : '❌ Missing';
        echo "<p><strong>$name rule:</strong> $status</p>";
    }
} else {
    echo "<p style='color: red;'>❌ .htaccess file not found</p>";
}

echo "<hr>";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
