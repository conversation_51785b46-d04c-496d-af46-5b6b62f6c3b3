<?php
/**
 * Mobile Search Controller
 * Handles mobile-specific search functionality
 * Extends SearchApiController to reuse existing logic
 */

require_once __DIR__ . '/SearchApiController.php';

class MobileSearchController extends SearchApiController {
    
    /**
     * Handle mobile AJAX search requests
     */
    public function mobileAjaxSearch() {
        // Set mobile-specific headers
        header('Content-Type: application/json');
        header('X-Mobile-Search: true');
        
        try {
            // Log mobile search request
            error_log('Mobile AJAX Search Request: ' . json_encode($_GET));
            
            // Use parent's AJAX search logic
            $this->ajaxSearch();
            
        } catch (Exception $e) {
            // Mobile-specific error handling
            $this->handleMobileError($e);
        }
    }
    
    /**
     * Get mobile-optimized search parameters
     */
    protected function getMobileSearchParams() {
        $params = $this->getSearchParams();
        
        // Add mobile-specific parameters or modifications
        $params['mobile'] = true;
        $params['limit'] = 20; // Fewer results for mobile
        
        return $params;
    }
    
    /**
     * Format properties for mobile response
     */
    protected function formatMobileProperties($properties) {
        $formattedProperties = [];
        
        foreach ($properties as $property) {
            // Mobile-optimized property data
            $formattedProperty = [
                'id' => $property->id,
                'title' => $property->title,
                'slug' => $property->slug,
                'price' => $property->price,
                'price_period' => $property->price_period,
                'area' => $property->area,
                'bedrooms' => $property->bedrooms,
                'bathrooms' => $property->bathrooms,
                'ward_name' => $property->ward_name,
                'type_name' => $property->type_name,
                'featured' => $property->featured,
                'created_at' => $property->created_at,
                
                // Mobile-optimized image handling
                'image' => $this->getMobileOptimizedImage($property->images),
                
                // Mobile-specific formatted data
                'formatted_price' => $this->formatMobilePrice($property->price, $property->price_period),
                'is_new' => (strtotime($property->created_at) > strtotime('-7 days')),
                'url' => '/thuenhadanang/' . $property->slug . '-' . $property->id
            ];
            
            $formattedProperties[] = $formattedProperty;
        }
        
        return $formattedProperties;
    }
    
    /**
     * Get mobile-optimized image
     */
    private function getMobileOptimizedImage($images) {
        $imageArray = json_decode($images, true);
        
        if (!empty($imageArray)) {
            return '/thuenhadanang/public/uploads/properties/' . $imageArray[0];
        }
        
        return '/thuenhadanang/public/images/no-image.jpg';
    }
    
    /**
     * Format price for mobile display
     */
    private function formatMobilePrice($price, $period) {
        $pricePeriod = '';
        switch ($period) {
            case 'month':
                $pricePeriod = 'tháng';
                break;
            case 'quarter':
                $pricePeriod = 'quý';
                break;
            case 'year':
                $pricePeriod = 'năm';
                break;
            default:
                $pricePeriod = 'tháng';
        }
        
        // Format price in millions
        $formattedPrice = number_format($price / 1000000, 1);
        $formattedPrice = rtrim(rtrim($formattedPrice, '0'), '.');
        
        return $formattedPrice . ' triệu/' . $pricePeriod;
    }
    
    /**
     * Handle mobile-specific errors
     */
    private function handleMobileError($e) {
        error_log('Mobile Search Error: ' . $e->getMessage());
        
        $response = [
            'success' => false,
            'error' => [
                'message' => 'Lỗi tìm kiếm trên mobile',
                'details' => $e->getMessage(),
                'mobile' => true
            ],
            'timestamp' => time()
        ];
        
        http_response_code(500);
        echo json_encode($response);
        exit;
    }
    
    /**
     * Get mobile filter options
     */
    public function getMobileFilterOptions() {
        header('Content-Type: application/json');
        
        try {
            // Get filter options from parent
            $this->getFilterOptions();
            
        } catch (Exception $e) {
            $this->handleMobileError($e);
        }
    }
    
    /**
     * Mobile search suggestions (for autocomplete)
     */
    public function getMobileSearchSuggestions() {
        header('Content-Type: application/json');
        
        try {
            $query = isset($_GET['q']) ? trim($_GET['q']) : '';
            
            if (strlen($query) < 2) {
                echo json_encode([
                    'success' => true,
                    'suggestions' => []
                ]);
                return;
            }
            
            // Get suggestions from database
            $suggestions = $this->propertyModel->getSearchSuggestions($query, 5);
            
            $response = [
                'success' => true,
                'suggestions' => $suggestions,
                'query' => $query
            ];
            
            echo json_encode($response);
            
        } catch (Exception $e) {
            $this->handleMobileError($e);
        }
    }
    
    /**
     * Mobile quick search (simplified search)
     */
    public function mobileQuickSearch() {
        header('Content-Type: application/json');
        
        try {
            $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
            
            if (empty($keyword)) {
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'properties' => [],
                        'count' => 0
                    ]
                ]);
                return;
            }
            
            // Simplified search for mobile
            $properties = $this->propertyModel->searchProperties($keyword, '', '', '', 'default', '', '', '', '');
            
            // Limit results for mobile
            $properties = array_slice($properties, 0, 10);
            
            $response = [
                'success' => true,
                'data' => [
                    'properties' => $this->formatMobileProperties($properties),
                    'count' => count($properties),
                    'keyword' => $keyword,
                    'mobile' => true
                ],
                'timestamp' => time()
            ];
            
            echo json_encode($response);
            
        } catch (Exception $e) {
            $this->handleMobileError($e);
        }
    }
    
    /**
     * Check if request is from mobile device
     */
    protected function isMobileRequest() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $mobileKeywords = [
            'Mobile', 'Android', 'iPhone', 'iPad', 'iPod', 
            'BlackBerry', 'Windows Phone', 'Opera Mini'
        ];
        
        foreach ($mobileKeywords as $keyword) {
            if (stripos($userAgent, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Log mobile search analytics
     */
    protected function logMobileSearchAnalytics($params, $resultCount) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'type' => 'mobile_search',
            'params' => $params,
            'result_count' => $resultCount,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
        ];
        
        error_log('Mobile Search Analytics: ' . json_encode($logData));
    }
}
?>
