<?php

// Test database data for the specific search criteria
define('BASE_PATH', __DIR__);
define('APP_PATH', BASE_PATH . '/app');

// Load required files
require_once APP_PATH . '/libraries/Database.php';
require_once APP_PATH . '/models/Property.php';
require_once APP_PATH . '/models/PropertyType.php';
require_once APP_PATH . '/models/Ward.php';

echo "<h1>Database Data Analysis</h1>\n";

try {
    $db = new Database();
    
    // Get property type and ward IDs
    $propertyTypeModel = new PropertyType();
    $wardModel = new Ward();
    
    $typeObj = $propertyTypeModel->getPropertyTypeBySlug('can-ho');
    $wardObj = $wardModel->getWardBySlug('an-hai-bac');
    
    echo "<h2>1. Basic Info</h2>\n";
    echo "<p><strong>Property Type:</strong> " . ($typeObj ? "ID: {$typeObj->id}, Name: {$typeObj->name}" : "Not found") . "</p>\n";
    echo "<p><strong>Ward:</strong> " . ($wardObj ? "ID: {$wardObj->id}, Name: {$wardObj->name}" : "Not found") . "</p>\n";
    
    if (!$typeObj || !$wardObj) {
        echo "<p style='color: red;'>Cannot proceed without valid type and ward.</p>\n";
        exit;
    }
    
    // Check all properties in this type and ward
    echo "<h2>2. All Properties in Type + Ward</h2>\n";
    $db->query('SELECT p.*, pt.name as type_name, w.name as ward_name
                FROM properties p
                LEFT JOIN property_types pt ON p.type_id = pt.id
                LEFT JOIN wards w ON p.ward_id = w.id
                WHERE p.type_id = :type_id AND p.ward_id = :ward_id
                AND p.status = "display" AND p.active = 1
                AND (p.expiration_date IS NULL OR p.expiration_date > NOW())
                ORDER BY p.created_at DESC');
    $db->bind(':type_id', $typeObj->id);
    $db->bind(':ward_id', $wardObj->id);
    $allProperties = $db->resultSet();
    
    echo "<p><strong>Total properties in Căn hộ + An Hải Bắc:</strong> " . count($allProperties) . "</p>\n";
    
    if (count($allProperties) > 0) {
        echo "<h3>Properties Details:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>ID</th><th>Title</th><th>Price (VND)</th><th>Area (m²)</th><th>Bedrooms</th><th>Bathrooms</th><th>Status</th></tr>\n";
        
        foreach ($allProperties as $prop) {
            $priceInMillions = $prop->price / 1000000;
            $meetsCriteria = ($prop->price >= 15000000 && $prop->area >= 50 && $prop->area <= 70 && $prop->bedrooms == 1);
            $rowColor = $meetsCriteria ? '#e8f5e8' : '#ffe8e8';
            
            echo "<tr style='background-color: $rowColor;'>\n";
            echo "<td>{$prop->id}</td>\n";
            echo "<td>" . htmlspecialchars($prop->title) . "</td>\n";
            echo "<td>" . number_format($prop->price) . " ({$priceInMillions}M)</td>\n";
            echo "<td>{$prop->area}</td>\n";
            echo "<td>{$prop->bedrooms}</td>\n";
            echo "<td>{$prop->bathrooms}</td>\n";
            echo "<td>{$prop->status}</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
        echo "<p><em>Green rows meet all criteria (Price ≥ 15M, Area 50-70m², 1 bedroom)</em></p>\n";
    }
    
    // Test specific search criteria
    echo "<h2>3. Search with Specific Criteria</h2>\n";
    echo "<p><strong>Criteria:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Type: can-ho (ID: {$typeObj->id})</li>\n";
    echo "<li>Ward: an-hai-bac (ID: {$wardObj->id})</li>\n";
    echo "<li>Price: 15+ (≥ 15,000,000 VND)</li>\n";
    echo "<li>Area: 50-70 (50-70 m²)</li>\n";
    echo "<li>Bedrooms: 1</li>\n";
    echo "</ul>\n";
    
    // Manual query to debug
    $sql = 'SELECT p.*, pt.name as type_name, w.name as ward_name
            FROM properties p
            LEFT JOIN property_types pt ON p.type_id = pt.id
            LEFT JOIN wards w ON p.ward_id = w.id
            WHERE p.status = "display" AND p.active = 1
            AND (p.expiration_date IS NULL OR p.expiration_date > NOW())
            AND LOWER(pt.slug) = LOWER(:type_slug)
            AND LOWER(w.slug) = LOWER(:ward_slug)
            AND p.price >= :min_price
            AND p.area BETWEEN :min_area AND :max_area
            AND p.bedrooms = :bedrooms
            ORDER BY p.created_at DESC';
    
    echo "<h3>SQL Query:</h3>\n";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>\n";
    
    $db->query($sql);
    $db->bind(':type_slug', 'can-ho');
    $db->bind(':ward_slug', 'an-hai-bac');
    $db->bind(':min_price', 15000000);
    $db->bind(':min_area', 50);
    $db->bind(':max_area', 70);
    $db->bind(':bedrooms', 1);
    
    $searchResults = $db->resultSet();
    
    echo "<p><strong>Search Results:</strong> " . count($searchResults) . " properties found</p>\n";
    
    if (count($searchResults) > 0) {
        echo "<h3>Matching Properties:</h3>\n";
        foreach ($searchResults as $prop) {
            echo "<div style='border: 1px solid #green; padding: 10px; margin: 5px; background-color: #e8f5e8;'>\n";
            echo "<strong>ID:</strong> {$prop->id}<br>\n";
            echo "<strong>Title:</strong> " . htmlspecialchars($prop->title) . "<br>\n";
            echo "<strong>Price:</strong> " . number_format($prop->price) . " VND<br>\n";
            echo "<strong>Area:</strong> {$prop->area} m²<br>\n";
            echo "<strong>Bedrooms:</strong> {$prop->bedrooms}<br>\n";
            echo "<strong>Bathrooms:</strong> {$prop->bathrooms}<br>\n";
            echo "</div>\n";
        }
    } else {
        echo "<p style='color: red;'>No properties match the search criteria.</p>\n";
        
        // Let's check what's available with relaxed criteria
        echo "<h3>Debugging - Relaxed Criteria:</h3>\n";
        
        // Check without price filter
        echo "<h4>Without price filter (≥15M):</h4>\n";
        $db->query('SELECT COUNT(*) as count FROM properties p
                    LEFT JOIN property_types pt ON p.type_id = pt.id
                    LEFT JOIN wards w ON p.ward_id = w.id
                    WHERE p.status = "display" AND p.active = 1
                    AND (p.expiration_date IS NULL OR p.expiration_date > NOW())
                    AND LOWER(pt.slug) = LOWER(:type_slug)
                    AND LOWER(w.slug) = LOWER(:ward_slug)
                    AND p.area BETWEEN :min_area AND :max_area
                    AND p.bedrooms = :bedrooms');
        $db->bind(':type_slug', 'can-ho');
        $db->bind(':ward_slug', 'an-hai-bac');
        $db->bind(':min_area', 50);
        $db->bind(':max_area', 70);
        $db->bind(':bedrooms', 1);
        $result = $db->single();
        echo "<p>Count: {$result->count}</p>\n";
        
        // Check without area filter
        echo "<h4>Without area filter (50-70m²):</h4>\n";
        $db->query('SELECT COUNT(*) as count FROM properties p
                    LEFT JOIN property_types pt ON p.type_id = pt.id
                    LEFT JOIN wards w ON p.ward_id = w.id
                    WHERE p.status = "display" AND p.active = 1
                    AND (p.expiration_date IS NULL OR p.expiration_date > NOW())
                    AND LOWER(pt.slug) = LOWER(:type_slug)
                    AND LOWER(w.slug) = LOWER(:ward_slug)
                    AND p.price >= :min_price
                    AND p.bedrooms = :bedrooms');
        $db->bind(':type_slug', 'can-ho');
        $db->bind(':ward_slug', 'an-hai-bac');
        $db->bind(':min_price', 15000000);
        $db->bind(':bedrooms', 1);
        $result = $db->single();
        echo "<p>Count: {$result->count}</p>\n";
        
        // Check without bedrooms filter
        echo "<h4>Without bedrooms filter (1 bedroom):</h4>\n";
        $db->query('SELECT COUNT(*) as count FROM properties p
                    LEFT JOIN property_types pt ON p.type_id = pt.id
                    LEFT JOIN wards w ON p.ward_id = w.id
                    WHERE p.status = "display" AND p.active = 1
                    AND (p.expiration_date IS NULL OR p.expiration_date > NOW())
                    AND LOWER(pt.slug) = LOWER(:type_slug)
                    AND LOWER(w.slug) = LOWER(:ward_slug)
                    AND p.price >= :min_price
                    AND p.area BETWEEN :min_area AND :max_area');
        $db->bind(':type_slug', 'can-ho');
        $db->bind(':ward_slug', 'an-hai-bac');
        $db->bind(':min_price', 15000000);
        $db->bind(':min_area', 50);
        $db->bind(':max_area', 70);
        $result = $db->single();
        echo "<p>Count: {$result->count}</p>\n";
    }
    
    // Test Property model method
    echo "<h2>4. Property Model Method Test</h2>\n";
    $propertyModel = new Property();
    $modelResults = $propertyModel->getPropertiesByTypeAndWard('can-ho', 'an-hai-bac', '15+', '50-70', '1');
    echo "<p><strong>Property Model Results:</strong> " . count($modelResults) . " properties</p>\n";
    
    if (count($modelResults) > 0) {
        echo "<h3>Model Results:</h3>\n";
        foreach ($modelResults as $prop) {
            echo "<div style='border: 1px solid #blue; padding: 10px; margin: 5px; background-color: #e8f0ff;'>\n";
            echo "<strong>Title:</strong> " . htmlspecialchars($prop->title) . "<br>\n";
            echo "<strong>Price:</strong> " . number_format($prop->price) . " VND<br>\n";
            echo "<strong>Area:</strong> {$prop->area} m²<br>\n";
            echo "<strong>Bedrooms:</strong> {$prop->bedrooms}<br>\n";
            echo "</div>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

?>
