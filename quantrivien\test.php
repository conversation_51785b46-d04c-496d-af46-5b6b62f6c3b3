<?php
// B<PERSON>t hiển thị lỗi
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Kiểm tra kết nối cơ sở dữ liệu
require_once __DIR__ . '/../app/libraries/Database.php';

try {
    $db = new Database();
    echo "<h2>Kết nối cơ sở dữ liệu thành công!</h2>";
    
    // Kiểm tra bảng users
    $db->query("SELECT * FROM users LIMIT 1");
    $user = $db->single();
    
    if ($user) {
        echo "<h3>Thông tin người dùng:</h3>";
        echo "<pre>";
        print_r($user);
        echo "</pre>";
    } else {
        echo "<p>Không tìm thấy người dùng nào.</p>";
    }
    
    // Kiểm tra bảng properties
    $db->query("SELECT * FROM properties LIMIT 1");
    $property = $db->single();
    
    if ($property) {
        echo "<h3>Thông tin bất động sản:</h3>";
        echo "<pre>";
        print_r($property);
        echo "</pre>";
    } else {
        echo "<p>Không tìm thấy bất động sản nào.</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>Lỗi:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . " on line " . $e->getLine() . "</p>";
}

// Kiểm tra các file template
echo "<h2>Kiểm tra file template:</h2>";
echo "<p>File header.php: " . (file_exists(__DIR__ . '/templates/header.php') ? 'Tồn tại' : 'Không tồn tại') . "</p>";
echo "<p>File footer.php: " . (file_exists(__DIR__ . '/templates/footer.php') ? 'Tồn tại' : 'Không tồn tại') . "</p>";

// Kiểm tra nội dung file
echo "<h3>Nội dung file header.php:</h3>";
echo "<pre>";
echo htmlspecialchars(file_get_contents(__DIR__ . '/templates/header.php'));
echo "</pre>";

echo "<h3>Nội dung file footer.php:</h3>";
echo "<pre>";
echo htmlspecialchars(file_get_contents(__DIR__ . '/templates/footer.php'));
echo "</pre>";
?>
