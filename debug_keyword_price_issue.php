<?php

// Debug keyword + price issue
define('BASE_PATH', __DIR__);
define('APP_PATH', BASE_PATH . '/app');

// Load required files
require_once APP_PATH . '/libraries/Database.php';
require_once APP_PATH . '/libraries/UrlHandler.php';
require_once APP_PATH . '/models/PropertyType.php';
require_once APP_PATH . '/models/Ward.php';
require_once APP_PATH . '/models/Property.php';

echo "<h1>Debug Keyword + Price Issue</h1>\n";

try {
    // Test the exact URL that's causing issues
    $testUrl = 'cho-thue-nha-dat/gia-tren-15-trieu';
    $keyword = '<PERSON><PERSON>ền';
    
    echo "<h2>1. URL Analysis</h2>\n";
    echo "<p><strong>URL:</strong> $testUrl</p>\n";
    echo "<p><strong>Keyword:</strong> $keyword</p>\n";
    echo "<p><strong>Expected:</strong> Properties ≥ 15 million VND containing '<PERSON><PERSON>'</p>\n";
    
    // Parse URL
    $urlHandler = new UrlHandler();
    $parsedParams = $urlHandler->parseUrl($testUrl);
    
    echo "<h2>2. URL Parsing Result</h2>\n";
    echo "<ul>\n";
    echo "<li>Type: '{$parsedParams['type']}'</li>\n";
    echo "<li>Ward: '{$parsedParams['ward']}'</li>\n";
    echo "<li>Price: '{$parsedParams['price']}'</li>\n";
    echo "<li>Area: '{$parsedParams['area']}'</li>\n";
    echo "<li>Bedrooms: '{$parsedParams['bedrooms']}'</li>\n";
    echo "</ul>\n";
    
    // Test Property model methods
    $propertyModel = new Property();
    
    echo "<h2>3. Testing Property Model Methods</h2>\n";
    
    // Test without keyword (should work correctly)
    echo "<h3>A. Without Keyword (getPropertiesByTypeAndWard)</h3>\n";
    $propertiesWithoutKeyword = $propertyModel->getPropertiesByTypeAndWard(
        '', // type
        '', // ward
        '15+', // price
        '', // area
        '', // bedrooms
        'default', // sort
        '', // bathrooms
        '' // direction
    );
    
    echo "<p><strong>Results:</strong> " . count($propertiesWithoutKeyword) . " properties</p>\n";
    
    if (count($propertiesWithoutKeyword) > 0) {
        echo "<h4>Properties found (without keyword):</h4>\n";
        foreach ($propertiesWithoutKeyword as $prop) {
            $priceInMillions = $prop->price / 1000000;
            $meetsPrice = ($prop->price >= 15000000);
            $priceColor = $meetsPrice ? 'green' : 'red';
            
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>\n";
            echo "<strong>ID:</strong> {$prop->id}<br>\n";
            echo "<strong>Title:</strong> " . htmlspecialchars($prop->title) . "<br>\n";
            echo "<strong>Price:</strong> <span style='color: $priceColor;'>" . number_format($prop->price) . " VND ({$priceInMillions}M)</span><br>\n";
            echo "<strong>Meets ≥15M:</strong> " . ($meetsPrice ? '✅ Yes' : '❌ No') . "<br>\n";
            echo "</div>\n";
        }
    }
    
    // Test with keyword (the problematic case)
    echo "<h3>B. With Keyword (searchProperties)</h3>\n";
    $propertiesWithKeyword = $propertyModel->searchProperties(
        $keyword, // keyword
        '', // typeSlug
        '', // wardSlug
        '15+', // price
        'default', // sort
        '', // bedrooms
        '', // bathrooms
        '', // direction
        '' // area
    );
    
    echo "<p><strong>Results:</strong> " . count($propertiesWithKeyword) . " properties</p>\n";
    
    if (count($propertiesWithKeyword) > 0) {
        echo "<h4>Properties found (with keyword):</h4>\n";
        foreach ($propertiesWithKeyword as $prop) {
            $priceInMillions = $prop->price / 1000000;
            $meetsPrice = ($prop->price >= 15000000);
            $priceColor = $meetsPrice ? 'green' : 'red';
            $containsKeyword = (stripos($prop->title, $keyword) !== false || stripos($prop->street, $keyword) !== false);
            
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>\n";
            echo "<strong>ID:</strong> {$prop->id}<br>\n";
            echo "<strong>Title:</strong> " . htmlspecialchars($prop->title) . "<br>\n";
            echo "<strong>Street:</strong> " . htmlspecialchars($prop->street) . "<br>\n";
            echo "<strong>Price:</strong> <span style='color: $priceColor;'>" . number_format($prop->price) . " VND ({$priceInMillions}M)</span><br>\n";
            echo "<strong>Meets ≥15M:</strong> " . ($meetsPrice ? '✅ Yes' : '❌ No') . "<br>\n";
            echo "<strong>Contains keyword:</strong> " . ($containsKeyword ? '✅ Yes' : '❌ No') . "<br>\n";
            echo "</div>\n";
        }
    }
    
    // Compare results
    echo "<h2>4. Comparison Analysis</h2>\n";
    
    $withoutKeywordCount = count($propertiesWithoutKeyword);
    $withKeywordCount = count($propertiesWithKeyword);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Method</th><th>Count</th><th>Notes</th></tr>\n";
    echo "<tr>\n";
    echo "<td>getPropertiesByTypeAndWard (no keyword)</td>\n";
    echo "<td>$withoutKeywordCount</td>\n";
    echo "<td>Should show only properties ≥ 15M</td>\n";
    echo "</tr>\n";
    echo "<tr>\n";
    echo "<td>searchProperties (with keyword)</td>\n";
    echo "<td>$withKeywordCount</td>\n";
    echo "<td>Should show only properties ≥ 15M containing keyword</td>\n";
    echo "</tr>\n";
    echo "</table>\n";
    
    // Check if there are properties < 15M in the keyword search results
    $invalidPriceCount = 0;
    if (count($propertiesWithKeyword) > 0) {
        foreach ($propertiesWithKeyword as $prop) {
            if ($prop->price < 15000000) {
                $invalidPriceCount++;
            }
        }
    }
    
    if ($invalidPriceCount > 0) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<h4 style='color: #721c24;'>❌ PROBLEM IDENTIFIED</h4>\n";
        echo "<p style='color: #721c24;'><strong>Issue:</strong> searchProperties() is returning $invalidPriceCount properties with price < 15M</p>\n";
        echo "<p style='color: #721c24;'>This indicates a bug in the price filtering logic within the searchProperties() method.</p>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<h4 style='color: #155724;'>✅ NO PRICE FILTERING ISSUE DETECTED</h4>\n";
        echo "<p style='color: #155724;'>All properties returned by searchProperties() meet the price criteria.</p>\n";
        echo "</div>\n";
    }
    
    // Test manual SQL query to see what should be returned
    echo "<h2>5. Manual SQL Query Test</h2>\n";
    
    $db = new Database();
    
    $sql = 'SELECT p.*, pt.name as type_name, w.name as ward_name
            FROM properties p
            LEFT JOIN property_types pt ON p.type_id = pt.id
            LEFT JOIN wards w ON p.ward_id = w.id
            WHERE p.status = "display" AND p.active = 1
            AND (p.expiration_date IS NULL OR p.expiration_date > NOW())
            AND p.price >= :min_price
            AND (p.title LIKE :keyword OR p.street LIKE :keyword)
            ORDER BY p.created_at DESC';
    
    echo "<h3>Expected SQL Query:</h3>\n";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>\n";
    
    echo "<h3>Parameters:</h3>\n";
    echo "<ul>\n";
    echo "<li>min_price: 15000000</li>\n";
    echo "<li>keyword: %Ngô Quyền%</li>\n";
    echo "</ul>\n";
    
    $db->query($sql);
    $db->bind(':min_price', 15000000);
    $db->bind(':keyword', '%' . $keyword . '%');
    
    $manualResults = $db->resultSet();
    
    echo "<p><strong>Manual SQL Results:</strong> " . count($manualResults) . " properties</p>\n";
    
    if (count($manualResults) > 0) {
        echo "<h4>Manual SQL Results:</h4>\n";
        foreach ($manualResults as $prop) {
            $priceInMillions = $prop->price / 1000000;
            
            echo "<div style='border: 1px solid #green; padding: 10px; margin: 5px; background-color: #e8f5e8;'>\n";
            echo "<strong>ID:</strong> {$prop->id}<br>\n";
            echo "<strong>Title:</strong> " . htmlspecialchars($prop->title) . "<br>\n";
            echo "<strong>Street:</strong> " . htmlspecialchars($prop->street) . "<br>\n";
            echo "<strong>Price:</strong> " . number_format($prop->price) . " VND ({$priceInMillions}M)<br>\n";
            echo "</div>\n";
        }
    }
    
    // Test different price values
    echo "<h2>6. Testing Different Price Values</h2>\n";
    
    $priceTests = ['5+', '10+', '15+', '20+'];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Price Filter</th><th>Without Keyword</th><th>With Keyword</th><th>Match</th></tr>\n";
    
    foreach ($priceTests as $testPrice) {
        $withoutKw = $propertyModel->getPropertiesByTypeAndWard('', '', $testPrice, '', '', 'default', '', '');
        $withKw = $propertyModel->searchProperties($keyword, '', '', $testPrice, 'default', '', '', '', '');
        
        $match = (count($withKw) <= count($withoutKw)) ? '✅' : '❌';
        
        echo "<tr>\n";
        echo "<td>{$testPrice} (≥" . str_replace('+', '', $testPrice) . "M)</td>\n";
        echo "<td>" . count($withoutKw) . "</td>\n";
        echo "<td>" . count($withKw) . "</td>\n";
        echo "<td>$match</td>\n";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    echo "<h2>7. Recommendations</h2>\n";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h4 style='color: #0066cc;'>🔧 NEXT STEPS</h4>\n";
    echo "<ol style='color: #0066cc;'>\n";
    echo "<li><strong>Check searchProperties() method</strong> - Look for price filtering logic</li>\n";
    echo "<li><strong>Compare SQL queries</strong> - Ensure both methods use same price conditions</li>\n";
    echo "<li><strong>Check parameter passing</strong> - Verify price parameter is passed correctly</li>\n";
    echo "<li><strong>Test with different keywords</strong> - See if issue is keyword-specific</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

?>
