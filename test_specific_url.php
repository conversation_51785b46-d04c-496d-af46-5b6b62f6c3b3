<?php

// Test specific URL: cho-thue-can-ho-tai-an-hai-bac/gia-tren-15-trieu-dt-50-70m2-1pn
define('BASE_PATH', __DIR__);
define('APP_PATH', BASE_PATH . '/app');

// Load required files
require_once APP_PATH . '/libraries/Database.php';
require_once APP_PATH . '/libraries/UrlHandler.php';
require_once APP_PATH . '/models/PropertyType.php';
require_once APP_PATH . '/models/Ward.php';

echo "<h1>Test Specific URL: cho-thue-can-ho-tai-an-hai-bac/gia-tren-15-trieu-dt-50-70m2-1pn</h1>\n";

try {
    $urlHandler = new UrlHandler();
    $testUrl = 'cho-thue-can-ho-tai-an-hai-bac/gia-tren-15-trieu-dt-50-70m2-1pn';
    
    echo "<h2>1. URL Parsing Test</h2>\n";
    echo "<p><strong>Testing URL:</strong> $testUrl</p>\n";
    
    $result = $urlHandler->parseUrl($testUrl);
    
    echo "<h3>Parsed Result:</h3>\n";
    echo "<pre>" . print_r($result, true) . "</pre>\n";
    
    // Check specific values
    echo "<h3>Specific Values Check:</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Type:</strong> '" . $result['type'] . "' (Expected: 'can-ho')</li>\n";
    echo "<li><strong>Ward:</strong> '" . $result['ward'] . "' (Expected: 'an-hai-bac')</li>\n";
    echo "<li><strong>Price:</strong> '" . $result['price'] . "' (Expected: '15+')</li>\n";
    echo "<li><strong>Area:</strong> '" . $result['area'] . "' (Expected: '50-70')</li>\n";
    echo "<li><strong>Bedrooms:</strong> '" . $result['bedrooms'] . "' (Expected: '1')</li>\n";
    echo "<li><strong>Matched:</strong> " . ($result['matched'] ? 'Yes' : 'No') . "</li>\n";
    echo "</ul>\n";
    
    // Test URL building back
    echo "<h2>2. URL Building Test</h2>\n";
    if ($result['matched']) {
        $params = [
            'type' => $result['type'],
            'ward' => $result['ward'],
            'price' => $result['price'],
            'area' => $result['area'],
            'bedrooms' => $result['bedrooms']
        ];
        
        echo "<p><strong>Building URL from parsed params:</strong></p>\n";
        echo "<pre>" . print_r($params, true) . "</pre>\n";
        
        $rebuiltUrl = $urlHandler->buildUrl($params);
        echo "<p><strong>Rebuilt URL:</strong> $rebuiltUrl</p>\n";
        
        $normalizedOriginal = str_replace('/thuenhadanang/', '', $rebuiltUrl);
        echo "<p><strong>Normalized:</strong> $normalizedOriginal</p>\n";
        echo "<p><strong>Match:</strong> " . ($testUrl === $normalizedOriginal ? 'Yes' : 'No') . "</p>\n";
    }
    
    // Test filter segments parsing
    echo "<h2>3. Filter Segments Analysis</h2>\n";
    $filterPart = 'gia-tren-15-trieu-dt-50-70m2-1pn';
    $filterSegments = explode('-', $filterPart);
    
    echo "<p><strong>Filter part:</strong> $filterPart</p>\n";
    echo "<p><strong>Filter segments:</strong></p>\n";
    echo "<pre>" . print_r($filterSegments, true) . "</pre>\n";
    
    // Manual parsing test
    echo "<h3>Manual Parsing Test:</h3>\n";
    $manualParams = [
        'type' => '',
        'ward' => '',
        'price' => '',
        'area' => '',
        'bedrooms' => ''
    ];
    
    for ($i = 0; $i < count($filterSegments); $i++) {
        $segment = $filterSegments[$i];
        echo "<p>Processing segment $i: '$segment'</p>\n";
        
        if ($segment == 'gia') {
            echo "  - Found 'gia' at index $i<br>\n";
            if (isset($filterSegments[$i+1]) && $filterSegments[$i+1] == 'tren' &&
                isset($filterSegments[$i+2]) && isset($filterSegments[$i+3]) &&
                $filterSegments[$i+3] == 'trieu') {
                $manualParams['price'] = $filterSegments[$i+2] . '+';
                echo "  - Parsed price: " . $manualParams['price'] . "<br>\n";
                $i += 3;
            }
        }
        else if ($segment == 'dt') {
            echo "  - Found 'dt' at index $i<br>\n";
            if (isset($filterSegments[$i+1]) && isset($filterSegments[$i+2]) &&
                preg_match('/^([0-9]+)m2$/', $filterSegments[$i+2], $matches)) {
                $manualParams['area'] = $filterSegments[$i+1] . '-' . $matches[1];
                echo "  - Parsed area: " . $manualParams['area'] . "<br>\n";
                $i += 2;
            }
        }
        else if (preg_match('/^([0-9]+)pn$/', $segment, $bedroomMatches)) {
            $manualParams['bedrooms'] = $bedroomMatches[1];
            echo "  - Parsed bedrooms: " . $manualParams['bedrooms'] . "<br>\n";
        }
    }
    
    echo "<h3>Manual Parsing Result:</h3>\n";
    echo "<pre>" . print_r($manualParams, true) . "</pre>\n";
    
    // Test database queries
    echo "<h2>4. Database Validation</h2>\n";
    
    // Check if type exists
    $propertyTypeModel = new PropertyType();
    $typeObj = $propertyTypeModel->getPropertyTypeBySlug('can-ho');
    echo "<p><strong>Property Type 'can-ho':</strong> " . ($typeObj ? "Found (ID: {$typeObj->id}, Name: {$typeObj->name})" : "Not found") . "</p>\n";
    
    // Check if ward exists
    $wardModel = new Ward();
    $wardObj = $wardModel->getWardBySlug('an-hai-bac');
    echo "<p><strong>Ward 'an-hai-bac':</strong> " . ($wardObj ? "Found (ID: {$wardObj->id}, Name: {$wardObj->name})" : "Not found") . "</p>\n";
    
    // Test SearchController parameters
    echo "<h2>5. SearchController Parameters Test</h2>\n";
    if ($result['matched']) {
        echo "<p>Parameters that would be passed to SearchController:</p>\n";
        echo "<ul>\n";
        echo "<li><strong>typeSlug:</strong> '" . $result['type'] . "'</li>\n";
        echo "<li><strong>wardSlug:</strong> '" . $result['ward'] . "'</li>\n";
        echo "<li><strong>additionalParams:</strong></li>\n";
        echo "<ul>\n";
        echo "<li>price: '" . $result['price'] . "'</li>\n";
        echo "<li>area: '" . $result['area'] . "'</li>\n";
        echo "<li>bedrooms: '" . $result['bedrooms'] . "'</li>\n";
        echo "<li>bathrooms: '" . $result['bathrooms'] . "'</li>\n";
        echo "<li>direction: '" . $result['direction'] . "'</li>\n";
        echo "</ul>\n";
        echo "</ul>\n";
        
        // Simulate what SearchController would receive
        echo "<h3>Simulated SearchController Call:</h3>\n";
        echo "<code>filterByTypeAndWard('{$result['type']}', '{$result['ward']}', [<br>\n";
        echo "&nbsp;&nbsp;'price' => '{$result['price']}',<br>\n";
        echo "&nbsp;&nbsp;'area' => '{$result['area']}',<br>\n";
        echo "&nbsp;&nbsp;'bedrooms' => '{$result['bedrooms']}',<br>\n";
        echo "&nbsp;&nbsp;'bathrooms' => '{$result['bathrooms']}',<br>\n";
        echo "&nbsp;&nbsp;'direction' => '{$result['direction']}'<br>\n";
        echo "])</code>\n";
    }
    
    // Test Property model query
    echo "<h2>6. Property Model Query Test</h2>\n";
    if ($result['matched']) {
        require_once APP_PATH . '/models/Property.php';
        $propertyModel = new Property();
        
        echo "<p>Testing Property model query with parsed parameters...</p>\n";
        
        // Test the exact query that would be used
        $properties = $propertyModel->getPropertiesByTypeAndWard(
            $result['type'],
            $result['ward'],
            $result['price'],
            $result['area'],
            $result['bedrooms'],
            'default',
            '',
            ''
        );
        
        echo "<p><strong>Query Result:</strong> " . count($properties) . " properties found</p>\n";
        
        if (count($properties) > 0) {
            echo "<h3>Sample Properties (first 3):</h3>\n";
            for ($i = 0; $i < min(3, count($properties)); $i++) {
                $prop = $properties[$i];
                echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>\n";
                echo "<strong>Title:</strong> " . htmlspecialchars($prop->title) . "<br>\n";
                echo "<strong>Type:</strong> " . htmlspecialchars($prop->type_name) . "<br>\n";
                echo "<strong>Ward:</strong> " . htmlspecialchars($prop->ward_name) . "<br>\n";
                echo "<strong>Price:</strong> " . number_format($prop->price) . " VND<br>\n";
                echo "<strong>Area:</strong> " . $prop->area . " m²<br>\n";
                echo "<strong>Bedrooms:</strong> " . $prop->bedrooms . "<br>\n";
                echo "<strong>Bathrooms:</strong> " . $prop->bathrooms . "<br>\n";
                echo "</div>\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

?>
