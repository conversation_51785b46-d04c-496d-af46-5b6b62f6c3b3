<?php

// Test complete flow for 90+ area filtering
define('BASE_PATH', __DIR__);
define('APP_PATH', BASE_PATH . '/app');

// Load required files
require_once APP_PATH . '/libraries/Database.php';
require_once APP_PATH . '/libraries/UrlHandler.php';
require_once APP_PATH . '/models/PropertyType.php';
require_once APP_PATH . '/models/Ward.php';
require_once APP_PATH . '/models/Property.php';
require_once APP_PATH . '/controllers/BaseController.php';

echo "<h1>Complete Flow Test for 90+ Area Filtering</h1>\n";

try {
    // Test the exact URL that's causing issues
    $testUrl = 'cho-thue-nha-dat-tai-an-hai-bac/gia-tren-15-trieu-dt-tren-90m2-2pn';
    
    echo "<h2>1. URL Handler Processing</h2>\n";
    $urlHandler = new UrlHandler();
    $parsedParams = $urlHandler->parseUrl($testUrl);
    
    echo "<p><strong>URL:</strong> $testUrl</p>\n";
    echo "<p><strong>Parsed Parameters:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Type: '{$parsedParams['type']}'</li>\n";
    echo "<li>Ward: '{$parsedParams['ward']}'</li>\n";
    echo "<li>Price: '{$parsedParams['price']}'</li>\n";
    echo "<li>Area: '{$parsedParams['area']}'</li>\n";
    echo "<li>Bedrooms: '{$parsedParams['bedrooms']}'</li>\n";
    echo "<li>Bathrooms: '{$parsedParams['bathrooms']}'</li>\n";
    echo "</ul>\n";
    
    // Test Property model with exact parameters
    echo "<h2>2. Property Model Query</h2>\n";
    
    $propertyModel = new Property();
    
    // Simulate SearchController call
    $properties = $propertyModel->getPropertiesByTypeAndWard(
        $parsedParams['type'],
        $parsedParams['ward'],
        $parsedParams['price'],
        $parsedParams['area'],
        $parsedParams['bedrooms'],
        'default',
        '1', // bathrooms from query string
        $parsedParams['direction']
    );
    
    echo "<p><strong>Properties found:</strong> " . count($properties) . "</p>\n";
    
    if (count($properties) > 0) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4 style='color: #721c24;'>❌ UNEXPECTED RESULTS</h4>\n";
        echo "<p style='color: #721c24;'>The system found properties when it should find none (area ≥ 90m²):</p>\n";
        
        foreach ($properties as $prop) {
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>\n";
            echo "<strong>ID:</strong> {$prop->id}<br>\n";
            echo "<strong>Title:</strong> " . htmlspecialchars($prop->title) . "<br>\n";
            echo "<strong>Area:</strong> {$prop->area} m² (Should be ≥ 90m²)<br>\n";
            echo "<strong>Price:</strong> " . number_format($prop->price) . " VND<br>\n";
            echo "<strong>Bedrooms:</strong> {$prop->bedrooms}<br>\n";
            echo "</div>\n";
        }
        echo "</div>\n";
    } else {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4 style='color: #155724;'>✅ CORRECT BEHAVIOR</h4>\n";
        echo "<p style='color: #155724;'>No properties found, which is correct since no properties in An Hải Bắc have area ≥ 90m².</p>\n";
        echo "</div>\n";
    }
    
    // Test different area values to ensure they work
    echo "<h2>3. Testing Different Area Values</h2>\n";
    
    $areaTests = [
        ['area' => '50-70', 'description' => '50-70m²'],
        ['area' => '70-90', 'description' => '70-90m²'],
        ['area' => '90+', 'description' => '90+ m²'],
        ['area' => '', 'description' => 'No area filter']
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Area Filter</th><th>Properties Found</th><th>Property Details</th></tr>\n";
    
    foreach ($areaTests as $test) {
        $testProperties = $propertyModel->getPropertiesByTypeAndWard(
            '',
            'an-hai-bac',
            '',
            $test['area'],
            '',
            'default',
            '',
            ''
        );
        
        $details = '';
        if (count($testProperties) > 0) {
            $details = '<ul>';
            foreach ($testProperties as $prop) {
                $details .= "<li>ID {$prop->id}: {$prop->area}m²</li>";
            }
            $details .= '</ul>';
        } else {
            $details = '<em>None</em>';
        }
        
        echo "<tr>\n";
        echo "<td>{$test['description']}</td>\n";
        echo "<td>" . count($testProperties) . "</td>\n";
        echo "<td>$details</td>\n";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    // Test URL building to ensure consistency
    echo "<h2>4. URL Building Test</h2>\n";
    
    $buildParams = [
        'type' => '',
        'ward' => 'an-hai-bac',
        'price' => '15+',
        'area' => '90+',
        'bedrooms' => '2'
    ];
    
    $builtUrl = $urlHandler->buildUrl($buildParams);
    echo "<p><strong>Built URL:</strong> $builtUrl</p>\n";
    echo "<p><strong>Original URL:</strong> /thuenhadanang/$testUrl</p>\n";
    
    if ($builtUrl === "/thuenhadanang/$testUrl") {
        echo "<p style='color: green;'>✅ URL building is consistent</p>\n";
    } else {
        echo "<p style='color: red;'>❌ URL building inconsistency detected</p>\n";
    }
    
    // Test round-trip
    echo "<h2>5. Round-trip Test</h2>\n";
    
    $roundTripParsed = $urlHandler->parseUrl(str_replace('/thuenhadanang/', '', $builtUrl));
    
    echo "<p><strong>Round-trip comparison:</strong></p>\n";
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Parameter</th><th>Original</th><th>Round-trip</th><th>Match</th></tr>\n";
    
    $compareParams = ['type', 'ward', 'price', 'area', 'bedrooms'];
    foreach ($compareParams as $param) {
        $original = $parsedParams[$param];
        $roundTrip = $roundTripParsed[$param];
        $match = ($original === $roundTrip) ? '✅' : '❌';
        
        echo "<tr>\n";
        echo "<td>$param</td>\n";
        echo "<td>'$original'</td>\n";
        echo "<td>'$roundTrip'</td>\n";
        echo "<td>$match</td>\n";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<h2>6. Recommendations</h2>\n";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h4 style='color: #0066cc;'>🔧 NEXT STEPS</h4>\n";
    echo "<p style='color: #0066cc;'>The URL parsing and database queries are now working correctly.</p>\n";
    echo "<p style='color: #0066cc;'><strong>If you're still seeing the 76m² property, please:</strong></p>\n";
    echo "<ol style='color: #0066cc;'>\n";
    echo "<li><strong>Clear browser cache</strong> - Hard refresh with Ctrl+F5</li>\n";
    echo "<li><strong>Check the exact URL</strong> - Make sure it contains 'dt-tren-90m2'</li>\n";
    echo "<li><strong>Check browser developer tools</strong> - Look for AJAX requests loading additional properties</li>\n";
    echo "<li><strong>Try incognito mode</strong> - To rule out cache/session issues</li>\n";
    echo "<li><strong>Check view source</strong> - See if properties are in the initial HTML or loaded via JavaScript</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
    // Test with actual query string
    echo "<h2>7. Test with Query String (bathrooms=1)</h2>\n";
    
    // Simulate $_GET['bathrooms'] = '1'
    $_GET['bathrooms'] = '1';
    
    $propertiesWithBathrooms = $propertyModel->getPropertiesByTypeAndWard(
        $parsedParams['type'],
        $parsedParams['ward'],
        $parsedParams['price'],
        $parsedParams['area'],
        $parsedParams['bedrooms'],
        'default',
        '1', // bathrooms
        $parsedParams['direction']
    );
    
    echo "<p><strong>Properties with bathrooms=1 filter:</strong> " . count($propertiesWithBathrooms) . "</p>\n";
    
    if (count($propertiesWithBathrooms) > 0) {
        echo "<p style='color: red;'>❌ Found properties when none should match all criteria</p>\n";
    } else {
        echo "<p style='color: green;'>✅ Correctly found no properties matching all criteria</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

?>
