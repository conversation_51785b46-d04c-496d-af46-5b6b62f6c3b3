<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="container mt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/thuenhadanang" class="text-decoration-none">Trang chủ</a></li>
        <li class="breadcrumb-item"><a href="/thuenhadanang/dashboard" class="text-decoration-none">Bảng điều khiển</a></li>
        <li class="breadcrumb-item active" aria-current="page">Thông tin tài khoản</li>
    </ol>
</nav>

<div class="container py-4">
    <div class="row">
        <!-- Include Sidebar -->
        <?php require_once 'app/views/partials/dashboard-sidebar.php'; ?>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Thông báo -->
            <?php if(isset($_SESSION['success_message'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>
                    <?php
                    echo $_SESSION['success_message'];
                    unset($_SESSION['success_message']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if(isset($_SESSION['error_message'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-circle me-2"></i>
                    <?php
                    echo $_SESSION['error_message'];
                    unset($_SESSION['error_message']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Card quản lý avatar -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <div class="d-flex align-items-center">
                        <div>
                            <h5 class="card-title mb-0">Ảnh đại diện</h5>
                            <small class="text-muted">Cập nhật ảnh đại diện của bạn</small>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <h6 class="mb-3">Avatar hiện tại</h6>
                            <?php if (!empty($data['user']->avatar) && $data['user']->avatar != 'default-avatar.jpg'): ?>
                                <img id="currentAvatar" src="<?php echo BASE_URL; ?>/public/uploads/users/<?php echo htmlspecialchars($data['user']->avatar); ?>"
                                     alt="Current Avatar" class="img-thumbnail rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                            <?php else: ?>
                                <img id="currentAvatar" src="<?php echo BASE_URL; ?>/public/uploads/users/default-avatar.jpg"
                                     alt="Current Avatar" class="img-thumbnail rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                            <?php endif; ?>
                        </div>
                        <div class="col-md-8">
                            <h6 class="mb-3">Cập nhật avatar mới</h6>
                            <form id="avatarForm" action="/thuenhadanang/dashboard/update-avatar" method="POST" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <div class="custom-file-upload">
                                        <div class="file-upload-wrapper">
                                            <button type="button" class="file-upload-btn">Chọn tệp</button>
                                            <div class="file-upload-info">Không có tệp nào được chọn</div>
                                            <input type="file" class="file-upload-input" id="avatarInput" name="avatar" accept="image/*">
                                        </div>
                                        <div class="form-text mt-2">Chỉ chấp nhận file ảnh (JPG, JPEG, PNG, GIF)</div>
                                    </div>
                                </div>

                                <div id="cropContainer" style="display: none;" class="mb-3">
                                    <h6>Cắt ảnh đại diện (bắt buộc)</h6>
                                    <div class="img-container mb-2" style="max-width: 100%; max-height: 400px;">
                                        <img id="cropperImage" src="" alt="Ảnh cần crop" style="max-width: 100%;">
                                    </div>
                                    <button type="button" id="cropButton" class="btn btn-primary">
                                        <i class="bi bi-crop me-1"></i> Cắt ảnh
                                    </button>
                                </div>

                                <div id="previewContainer" style="display: none;" class="mb-3 text-center">
                                    <h6>Xem trước</h6>
                                    <img id="avatarPreview" src="" alt="Avatar Preview" class="img-thumbnail rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                                    <input type="hidden" name="avatar_data" id="avatarData">
                                </div>

                                <button type="submit" id="saveAvatarBtn" name="update_avatar" class="btn btn-success" style="display: none;">
                                    <i class="bi bi-save me-1"></i> Lưu avatar
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card thông tin cá nhân -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <div class="d-flex align-items-center">
                        <div>
                            <h5 class="card-title mb-0">Thông tin tài khoản</h5>
                            <small class="text-muted">Cập nhật thông tin cá nhân của bạn</small>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form action="/thuenhadanang/dashboard/profile/update" method="POST" class="needs-validation" novalidate>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control border-2" id="fullname" name="fullname"
                                           value="<?php echo htmlspecialchars($data['user']->fullname ?? ''); ?>"
                                           placeholder="Nhập họ và tên" required>
                                    <label for="fullname">Họ và tên</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="email" class="form-control border-2" id="email" name="email"
                                           value="<?php echo htmlspecialchars($data['user']->email ?? ''); ?>"
                                           placeholder="Nhập địa chỉ email" required>
                                    <label for="email">Email</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="tel" class="form-control border-2" id="phone" name="phone"
                                           value="<?php echo htmlspecialchars($data['user']->phone ?? ''); ?>"
                                           placeholder="Nhập số điện thoại">
                                    <label for="phone">Số điện thoại</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control border-2" id="zalo" name="zalo"
                                           value="<?php echo htmlspecialchars($data['user']->zalo ?? ''); ?>"
                                           placeholder="Nhập số Zalo">
                                    <label for="zalo">Zalo</label>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-floating">
                                    <input type="text" class="form-control border-2" id="address" name="address"
                                           value="<?php echo htmlspecialchars($data['user']->address ?? ''); ?>"
                                           placeholder="Nhập địa chỉ">
                                    <label for="address">Địa chỉ</label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <button type="reset" class="btn btn-light border-2">
                                <i class="bi bi-arrow-counterclockwise me-2"></i>Đặt lại
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>Cập nhật thông tin
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control {
    border-color: #dee2e6;
}

/* CSS cho nút chọn tệp */
.custom-file-upload {
    width: 100%;
}

.file-upload-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.375rem;
    background-color: #fff;
    overflow: hidden;
}

.file-upload-btn {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #212529;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.875rem;
    white-space: nowrap;
}

.file-upload-btn:hover {
    background-color: #e9ecef;
}

.file-upload-info {
    margin-left: 10px;
    color: #6c757d;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
}

.file-upload-input {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}
.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    color: #0d6efd;
    opacity: 0.65;
}
.btn-primary {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
}
.btn-light {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}
.btn-light:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
}
</style>

<!-- Thêm CSS cho Cropper.js -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.css">

<script>
// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()
</script>

<!-- Thêm JS cho Cropper.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Các biến toàn cục
    let cropper;
    const avatarInput = document.getElementById('avatarInput');
    const cropperImage = document.getElementById('cropperImage');
    const cropContainer = document.getElementById('cropContainer');
    const previewContainer = document.getElementById('previewContainer');
    const avatarPreview = document.getElementById('avatarPreview');
    const cropButton = document.getElementById('cropButton');
    const avatarData = document.getElementById('avatarData');
    const saveAvatarBtn = document.getElementById('saveAvatarBtn');
    const avatarForm = document.getElementById('avatarForm');

    // Xử lý nút chọn tệp
    const fileUploadBtn = document.querySelector('.file-upload-btn');
    const fileUploadInfo = document.querySelector('.file-upload-info');

    if (fileUploadBtn) {
        fileUploadBtn.addEventListener('click', function() {
            if (avatarInput) {
                avatarInput.click();
            }
        });
    }

    // Xử lý khi người dùng chọn file
    if (avatarInput) {
        avatarInput.addEventListener('change', function(e) {
            if (e.target.files.length) {
                // Lấy file đã chọn
                const file = e.target.files[0];

                // Hiển thị tên file
                if (fileUploadInfo) {
                    fileUploadInfo.textContent = file.name;
                }

                // Kiểm tra loại file
                if (!file.type.match('image.*')) {
                    alert('Vui lòng chọn file hình ảnh');
                    return;
                }

                // Đọc file và hiển thị trong cropper
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Hiển thị ảnh trong cropper
                    cropperImage.src = e.target.result;

                    // Hiển thị container crop
                    cropContainer.style.display = 'block';

                    // Ẩn container preview và nút lưu
                    previewContainer.style.display = 'none';
                    saveAvatarBtn.style.display = 'none';

                    // Hủy cropper cũ nếu có
                    if (cropper) {
                        cropper.destroy();
                    }

                    // Khởi tạo cropper mới
                    cropper = new Cropper(cropperImage, {
                        aspectRatio: 1, // Tỷ lệ 1:1 cho avatar
                        viewMode: 1,    // Giới hạn khung nhìn trong canvas
                        dragMode: 'move',
                        guides: true,
                        center: true,
                        highlight: true,
                        cropBoxMovable: true,
                        cropBoxResizable: true,
                        toggleDragModeOnDblclick: false
                    });
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Xử lý khi người dùng nhấn nút Crop
    if (cropButton) {
        cropButton.addEventListener('click', function() {
            if (!cropper) return;

            // Lấy dữ liệu ảnh đã crop
            const canvas = cropper.getCroppedCanvas({
                width: 300,
                height: 300,
                minWidth: 100,
                minHeight: 100,
                maxWidth: 1000,
                maxHeight: 1000,
                fillColor: '#fff',
                imageSmoothingEnabled: true,
                imageSmoothingQuality: 'high',
            });

            // Chuyển đổi canvas thành dữ liệu base64
            const croppedImageData = canvas.toDataURL('image/jpeg', 0.8);

            // Hiển thị ảnh đã crop
            avatarPreview.src = croppedImageData;
            previewContainer.style.display = 'block';

            // Lưu dữ liệu ảnh vào input hidden
            avatarData.value = croppedImageData;

            // Hiển thị nút lưu
            saveAvatarBtn.style.display = 'block';
        });
    }

    // Xử lý form submit
    if (avatarForm) {
        avatarForm.addEventListener('submit', function(e) {
            // Kiểm tra xem đã crop ảnh chưa
            if (avatarInput.files.length > 0 && !avatarData.value) {
                e.preventDefault();
                alert('Vui lòng cắt ảnh trước khi lưu');
                return;
            }
        });
    }
});
</script>