<?php
// Load configuration
require_once 'config.php';

// Load database library
require_once APP_PATH . '/libraries/Database.php';

// Load Ward model
require_once APP_PATH . '/models/Ward.php';

// Create Ward model instance
$wardModel = new Ward();

// Get all wards
$wards = $wardModel->getAllWards();

echo '<h1>Ward Test</h1>';
echo '<h2>All Wards in Database</h2>';
echo '<table border="1">';
echo '<tr><th>ID</th><th>Name</th><th>Slug</th><th>Test Link</th></tr>';

foreach ($wards as $ward) {
    echo '<tr>';
    echo '<td>' . $ward->id . '</td>';
    echo '<td>' . htmlspecialchars($ward->name) . '</td>';
    echo '<td>' . htmlspecialchars($ward->slug) . '</td>';
    echo '<td><a href="/thuenhadanang/thue-nha-dat-' . urlencode($ward->slug) . '" target="_blank">Test Ward Search</a></td>';
    echo '</tr>';
}

echo '</table>';

// Test direct property query for each ward
echo '<h2>Property Count by Ward</h2>';
echo '<table border="1">';
echo '<tr><th>Ward ID</th><th>Ward Name</th><th>Ward Slug</th><th>Property Count</th></tr>';

$db = new Database();

foreach ($wards as $ward) {
    $db->query('SELECT COUNT(*) as count FROM properties p
                JOIN wards w ON p.ward_id = w.id
                WHERE w.id = :ward_id');
    $db->bind(':ward_id', $ward->id);
    $result = $db->single();

    echo '<tr>';
    echo '<td>' . $ward->id . '</td>';
    echo '<td>' . htmlspecialchars($ward->name) . '</td>';
    echo '<td>' . htmlspecialchars($ward->slug) . '</td>';
    echo '<td>' . $result->count . '</td>';
    echo '</tr>';
}

echo '</table>';

// Specific test for An Hai Bac ward
echo '<h2>Specific Test for An Hai Bac Ward</h2>';

// Find the An Hai Bac ward
$anHaiBacWard = null;
foreach ($wards as $ward) {
    if (strtolower($ward->slug) === 'an-hai-bac') {
        $anHaiBacWard = $ward;
        break;
    }
}

if ($anHaiBacWard) {
    echo '<p>Found An Hai Bac ward in database:</p>';
    echo '<ul>';
    echo '<li>ID: ' . $anHaiBacWard->id . '</li>';
    echo '<li>Name: ' . htmlspecialchars($anHaiBacWard->name) . '</li>';
    echo '<li>Slug: ' . htmlspecialchars($anHaiBacWard->slug) . '</li>';
    echo '</ul>';

    // Check if there are any properties with this ward
    $db->query('SELECT COUNT(*) as count FROM properties p WHERE p.ward_id = :ward_id');
    $db->bind(':ward_id', $anHaiBacWard->id);
    $result = $db->single();

    echo '<p>Number of properties in An Hai Bac ward: ' . $result->count . '</p>';

    if ($result->count > 0) {
        // List the properties
        $db->query('SELECT p.*, pt.name as type_name FROM properties p
                    JOIN property_types pt ON p.type_id = pt.id
                    WHERE p.ward_id = :ward_id');
        $db->bind(':ward_id', $anHaiBacWard->id);
        $properties = $db->resultSet();

        echo '<h3>Properties in An Hai Bac ward:</h3>';
        echo '<table border="1">';
        echo '<tr><th>ID</th><th>Title</th><th>Type</th><th>Status</th></tr>';

        foreach ($properties as $property) {
            echo '<tr>';
            echo '<td>' . $property->id . '</td>';
            echo '<td>' . htmlspecialchars($property->title) . '</td>';
            echo '<td>' . htmlspecialchars($property->type_name) . '</td>';
            echo '<td>' . htmlspecialchars($property->status) . '</td>';
            echo '</tr>';
        }

        echo '</table>';
    }
} else {
    echo '<p>An Hai Bac ward not found in database!</p>';

    // Check if there's a similar ward
    echo '<p>Checking for similar wards:</p>';
    echo '<ul>';
    foreach ($wards as $ward) {
        if (stripos($ward->name, 'an hai') !== false || stripos($ward->slug, 'an-hai') !== false) {
            echo '<li>' . htmlspecialchars($ward->name) . ' (Slug: ' . htmlspecialchars($ward->slug) . ')</li>';
        }
    }
    echo '</ul>';
}

// Check the URL slug format
echo '<h2>URL Slug Test</h2>';
echo '<p>Testing URL: /thuenhadanang/thue-nha-dat-an-hai-bac</p>';

// Simulate URL processing
$testSlug = 'an-hai-bac';
$db->query('SELECT id, name, slug FROM wards WHERE LOWER(slug) = LOWER(:slug)');
$db->bind(':slug', $testSlug);
$wardResult = $db->single();

if ($wardResult) {
    echo '<p>Ward found with slug "' . $testSlug . '":</p>';
    echo '<ul>';
    echo '<li>ID: ' . $wardResult->id . '</li>';
    echo '<li>Name: ' . htmlspecialchars($wardResult->name) . '</li>';
    echo '<li>Slug: ' . htmlspecialchars($wardResult->slug) . '</li>';
    echo '</ul>';
} else {
    echo '<p>No ward found with slug "' . $testSlug . '"</p>';

    // Check for similar slugs
    $db->query('SELECT id, name, slug FROM wards WHERE slug LIKE :pattern');
    $db->bind(':pattern', '%an-hai%');
    $similarWards = $db->resultSet();

    if (!empty($similarWards)) {
        echo '<p>Similar wards found:</p>';
        echo '<ul>';
        foreach ($similarWards as $ward) {
            echo '<li>' . htmlspecialchars($ward->name) . ' (Slug: ' . htmlspecialchars($ward->slug) . ')</li>';
        }
        echo '</ul>';
    } else {
        echo '<p>No similar wards found</p>';
    }
}
