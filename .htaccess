RewriteEngine On
RewriteBase /thuenhadanang/

# API Routes (must be before other rules)
RewriteRule ^api/search/?$ api/search.php [L,QSA]
RewriteRule ^api/search/filters/?$ api/search.php?action=filters [L,QSA]
RewriteRule ^api/mobile-search-debug/?$ api/mobile-search-debug.php [L,QSA]
RewriteRule ^api/mobile-search-simple/?$ api/mobile-search-simple.php [L,QSA]
RewriteRule ^api/mobile-search/?$ api/mobile-search.php [L,QSA]
RewriteRule ^api/mobile-search/filters/?$ api/mobile-search.php?action=filters [L,QSA]
RewriteRule ^api/mobile-search/suggestions/?$ api/mobile-search.php?action=suggestions [L,QSA]
RewriteRule ^api/mobile-search/quick/?$ api/mobile-search.php?action=quick [L,QSA]

# Cho phép truy cập trực tiếp các file trong thư mục public
RewriteRule ^public/(.*)$ public/$1 [L]

# Xử lý URL thân thiện cho trang tìm kiếm
# Trường hợp chỉ có type
RewriteRule ^thue-([a-z0-9-]+)/?$ index.php?url=thue-$1 [QSA,L]

# Trường hợp chỉ có ward
RewriteRule ^thue-nha-dat-([a-z0-9-]+)/?$ index.php?url=thue-nha-dat-$1 [QSA,L]

# Trường hợp chỉ có price
RewriteRule ^thue-nha-dat-gia-(tu|tren)-([0-9]+)-?([0-9]+)?-trieu/?$ index.php?url=thue-nha-dat-gia-$1-$2-$3-trieu [QSA,L]

# Định dạng URL mới với dấu gạch chéo - định dạng mới cho-thue-{type}-tai-{ward}
RewriteRule ^cho-thue-([a-z0-9-]+)-tai-([a-z0-9-]+)/?$ index.php?url=cho-thue-$1-tai-$2 [QSA,L]
RewriteRule ^cho-thue-([a-z0-9-]+)-tai-([a-z0-9-]+)/([0-9]+)pn/?$ index.php?url=cho-thue-$1-tai-$2/$3pn [QSA,L]

# Định dạng URL mới với filter SEO-friendly
RewriteRule ^cho-thue-([a-z0-9-]+)-tai-([a-z0-9-]+)/(.+)$ index.php?url=cho-thue-$1-tai-$2/$3 [QSA,L]
RewriteRule ^cho-thue-([a-z0-9-]+)/(.+)$ index.php?url=cho-thue-$1/$2 [QSA,L]

# Định dạng URL mới với dấu gạch chéo - các trường hợp đơn giản (tương thích ngược)
RewriteRule ^cho-thue-([a-z0-9-]+)/?$ index.php?url=cho-thue-$1 [QSA,L]
RewriteRule ^cho-thue-([a-z0-9-]+)/([0-9]+)pn/?$ index.php?url=cho-thue-$1/$2pn [QSA,L]

# Chuyển hướng từ định dạng cũ sang định dạng mới
RewriteRule ^cho-thue-([a-z0-9-]+)-([a-z0-9-]+)/?$ /thuenhadanang/cho-thue-$1-tai-$2 [R=301,L]
RewriteRule ^cho-thue-([a-z0-9-]+)-([a-z0-9-]+)/([0-9]+)pn/?$ /thuenhadanang/cho-thue-$1-tai-$2/$3pn [R=301,L]

# Chuyển hướng từ định dạng URL cũ sang định dạng mới (các trường hợp phức tạp)
RewriteRule ^cho-thue-([a-z0-9-]+)-([a-z0-9-]+)/gia-([0-9]+)-([0-9]+)-trieu/?$ /thuenhadanang/cho-thue-$1-tai-$2/gia-$3-$4-trieu [R=301,L]
RewriteRule ^cho-thue-([a-z0-9-]+)-([a-z0-9-]+)/gia-([0-9]+)-([0-9]+)-trieu-dt-([0-9]+)-([0-9]+)m2/?$ /thuenhadanang/cho-thue-$1-tai-$2/gia-$3-$4-trieu-dt-$5-$6m2 [R=301,L]
RewriteRule ^cho-thue-([a-z0-9-]+)-([a-z0-9-]+)/gia-([0-9]+)-([0-9]+)-trieu-dt-([0-9]+)-([0-9]+)m2-([0-9]+)pn/?$ /thuenhadanang/cho-thue-$1-tai-$2/gia-$3-$4-trieu-dt-$5-$6m2-$7pn [R=301,L]

# Trường hợp có nhiều tham số (định dạng cũ)
RewriteRule ^cho-thue-([a-z0-9-]+)(-tai-([a-z0-9-]+))?/?$ index.php?url=cho-thue-$1$2 [QSA,L]
RewriteRule ^cho-thue-nha-dat(-tai-([a-z0-9-]+))?/?$ index.php?url=cho-thue-nha-dat$1 [QSA,L]
RewriteRule ^cho-thue-([a-z0-9-]+)(-tai-([a-z0-9-]+))?/gia-(tu|tren)-([0-9]+)-?([0-9]+)?-trieu/?$ index.php?url=cho-thue-$1$2/gia-$4-$5-$6-trieu [QSA,L]

# Xử lý các tham số bổ sung (định dạng cũ)
RewriteRule ^cho-thue-([a-z0-9-]+)(-tai-([a-z0-9-]+))?/([^/]+)/?$ index.php?url=cho-thue-$1$2/$4 [QSA,L]
RewriteRule ^cho-thue-([a-z0-9-]+)(-tai-([a-z0-9-]+))?/([^/]+)/([^/]+)/?$ index.php?url=cho-thue-$1$2/$4/$5 [QSA,L]
RewriteRule ^cho-thue-([a-z0-9-]+)(-tai-([a-z0-9-]+))?/([^/]+)/([^/]+)/([^/]+)/?$ index.php?url=cho-thue-$1$2/$4/$5/$6 [QSA,L]

# Nếu file/directory không tồn tại, chuyển hướng về index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]
