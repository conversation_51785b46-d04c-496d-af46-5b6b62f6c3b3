<!-- Include custom CSS for property listing -->
<link rel="stylesheet" href="/thuenhadanang/public/css/property-listing.css">
<!-- Include Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<!-- Include Dropzone.js -->
<link rel="stylesheet" href="https://unpkg.com/dropzone@5/dist/min/dropzone.min.css" type="text/css" />
<script src="https://unpkg.com/dropzone@5/dist/min/dropzone.min.js"></script>

<!-- Include Compressor.js for image optimization -->
<script src="https://unpkg.com/compressorjs/dist/compressor.min.js"></script>

<!-- Include Sortable.js for drag and drop functionality -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<!-- Custom CSS for image upload and drag-drop functionality -->
<style>
    .dropzone {
        border: 2px dashed #0d6efd;
        border-radius: 8px;
        background: #f8f9fa;
        min-height: 180px;
        padding: 30px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .dropzone:hover {
        background: #e9ecef;
        border-color: #0b5ed7;
        box-shadow: 0 4px 15px rgba(13, 110, 253, 0.15);
    }

    .dropzone:active {
        transform: translateY(0);
        box-shadow: 0 2px 5px rgba(13, 110, 253, 0.1);
    }

    .dropzone .dz-message {
        text-align: center;
        width: 100%;
    }

    .dropzone .dz-message i {
        display: block;
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: #0d6efd;
        transition: transform 0.3s ease;
    }

    .dropzone:hover .dz-message i {
        transform: scale(1.1);
        color: #0b5ed7;
    }

    .dropzone .dz-message p {
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #212529;
    }

    /* Hide default file input button */
    .dropzone .fallback {
        display: none;
    }

    /* Ensure the dropzone is clickable */
    .dropzone.dz-clickable {
        cursor: pointer;
    }

    /* Hide default button styles */
    .dropzone.dz-clickable .dz-message,
    .dropzone.dz-clickable .dz-message * {
        cursor: pointer;
    }

    /* Hide the default file input */
    .dz-hidden-input {
        position: absolute;
        top: -9999px;
        left: -9999px;
        visibility: hidden;
    }

    /* Style for dz-preview elements */
    .dz-preview {
        margin: 10px;
        display: inline-block;
    }

    .dz-processing-indicator {
        margin-top: 15px;
        padding: 10px 15px;
        background: rgba(13, 110, 253, 0.1);
        border-radius: 6px;
        display: flex;
        align-items: center;
        font-size: 0.875rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.4);
        }
        70% {
            box-shadow: 0 0 0 6px rgba(13, 110, 253, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
        }
    }

    .dz-preview {
        position: relative;
        margin: 10px;
        transition: all 0.3s ease;
    }

    .dz-preview:hover {
        transform: translateY(-3px);
    }

    .dz-image {
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    }

    .dz-details {
        padding: 10px;
        text-align: center;
    }

    .bg-success-light {
        background-color: rgba(25, 135, 84, 0.15);
    }

    .bg-danger-light {
        background-color: rgba(220, 53, 69, 0.15);
    }

    /* CSS cho nút xóa hình ảnh */
    .remove-image {
        padding: 0.25rem 0.5rem;
        min-width: 32px;
        min-height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        opacity: 0.9;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
    }

    .remove-image:hover {
        opacity: 1;
        transform: scale(1.1);
        box-shadow: 0 3px 6px rgba(220, 53, 69, 0.2);
    }

    .remove-image:active {
        transform: scale(0.95);
    }

    /* CSS cho chức năng kéo thả */
    .image-preview-item {
        cursor: grab;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        margin-bottom: 1rem;
    }

    .image-preview-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        z-index: 1;
    }

    .image-preview-item:active {
        cursor: grabbing;
    }

    .image-preview-item.sortable-ghost {
        opacity: 0.4;
        transform: scale(0.95);
    }

    .image-preview-item.sortable-chosen {
        background-color: #f8f9fa;
        box-shadow: 0 0 20px rgba(13, 110, 253, 0.4);
        transform: scale(1.05);
        z-index: 10;
    }

    .image-preview-item.sortable-drag {
        opacity: 0.9;
        transform: rotate(3deg) scale(1.08);
        box-shadow: 0 15px 25px rgba(0, 0, 0, 0.2);
        z-index: 100;
    }

    .image-preview-item .card {
        border: 2px solid transparent;
        transition: all 0.3s ease;
        border-radius: 8px;
        overflow: hidden;
    }

    .image-preview-item:hover .card {
        border-color: #0d6efd;
    }

    .image-preview-item .preview-image {
        transition: all 0.3s ease;
        height: 150px;
        object-fit: cover;
    }

    .image-preview-item:hover .preview-image {
        transform: scale(1.05);
    }

    .image-preview-item .form-check {
        transition: all 0.2s ease;
    }

    .image-preview-item:hover .form-check {
        transform: translateY(2px);
    }

    .drag-hint {
        background-color: rgba(13, 110, 253, 0.1);
        padding: 5px 10px;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .drag-hint:hover {
        background-color: rgba(13, 110, 253, 0.2);
    }

    /* Toast notifications */
    .toast-container {
        z-index: 1060;
    }

    .toast {
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .toast.show {
        opacity: 1;
    }
</style>

<!-- Disable Dropzone auto-discover to prevent double initialization -->
<script>
    // Disable Dropzone auto-discover before any Dropzone elements are rendered
    window.Dropzone = window.Dropzone || {};
    window.Dropzone.autoDiscover = false;
</script>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="container mt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/thuenhadanang" class="text-decoration-none">Trang chủ</a></li>
        <li class="breadcrumb-item"><a href="/thuenhadanang/dashboard" class="text-decoration-none">Bảng điều khiển</a></li>
        <li class="breadcrumb-item"><a href="/thuenhadanang/dashboard/properties" class="text-decoration-none">Quản lý tin đăng</a></li>
        <li class="breadcrumb-item active" aria-current="page">Chỉnh sửa tin đăng</li>
    </ol>
</nav>

<?php
// Hiển thị thông báo lỗi/thành công nếu có
if (!empty($data['error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . $data['error'] . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

if (!empty($data['success'])) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . $data['success'] . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

// Lấy dữ liệu bất động sản
$property = $data['property'];
$propertyTypes = $data['propertyTypes'];
$wards = $data['wards'];
$directions = $data['directions'];
$propertyImages = $data['propertyImages'];
?>

<div class="container py-4">
    <div class="row">
        <!-- Include Sidebar -->
        <?php require_once 'app/views/partials/dashboard-sidebar.php'; ?>

        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card border-0 shadow-sm property-listing-form">
                <div class="card-header bg-white py-3 border-0">
                    <h5 class="mb-0">Chỉnh sửa tin đăng</h5>
                </div>
                <div class="card-body p-4">
                    <p class="text-muted">Chỉnh sửa thông tin tin đăng của bạn. Tin đăng sau khi chỉnh sửa sẽ được chuyển sang trạng thái chờ duyệt.</p>

                    <form id="propertyEditForm" action="/thuenhadanang/property/update/<?php echo $property->id; ?>" method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="type_id" class="form-label">Loại bất động sản <span class="required">*</span></label>
                                    <select class="form-select" id="type_id" name="type_id" required>
                                        <option value="">-- Chọn loại bất động sản --</option>
                                        <?php foreach ($propertyTypes as $type): ?>
                                            <option value="<?php echo $type->id; ?>" <?php echo ($property->type_id == $type->id) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($type->name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="ward_id" class="form-label">Phường/Xã <span class="required">*</span></label>
                                    <select class="form-select ward-select" id="ward_id" name="ward_id" required>
                                        <option value="">-- Chọn phường/xã --</option>
                                        <?php foreach ($wards as $ward): ?>
                                            <option value="<?php echo $ward->id; ?>" <?php echo ($property->ward_id == $ward->id) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($ward->name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="street" class="form-label">Đường <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="street" name="street" placeholder="Chỉ nhập tên đường. Ví dụ: Ngô Quyền" required value="<?php echo htmlspecialchars($property->street); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="address" class="form-label">Địa chỉ đầy đủ <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="address" name="address" required value="<?php echo htmlspecialchars($property->address); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="price" class="form-label">Giá thuê (VND) <span class="required">*</span></label>
                                    <input type="number" class="form-control" id="price" name="price" min="0" required value="<?php echo $property->price; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="price_period" class="form-label">Thời hạn thuê <span class="required">*</span></label>
                                    <select class="form-select" id="price_period" name="price_period" required>
                                        <option value="day" <?php echo ($property->price_period == 'day') ? 'selected' : ''; ?>>Ngày</option>
                                        <option value="week" <?php echo ($property->price_period == 'week') ? 'selected' : ''; ?>>Tuần</option>
                                        <option value="month" <?php echo ($property->price_period == 'month') ? 'selected' : ''; ?>>Tháng</option>
                                        <option value="quarter" <?php echo ($property->price_period == 'quarter') ? 'selected' : ''; ?>>Quý</option>
                                        <option value="year" <?php echo ($property->price_period == 'year') ? 'selected' : ''; ?>>Năm</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="area" class="form-label">Diện tích (m²) <span class="required">*</span></label>
                                    <input type="number" class="form-control" id="area" name="area" min="0" step="0.1" required value="<?php echo $property->area; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="direction" class="form-label">Hướng chính</label>
                                    <select class="form-select" id="direction" name="direction">
                                        <option value="">-- Chọn hướng --</option>
                                        <?php foreach ($directions as $direction): ?>
                                            <option value="<?php echo $direction->slug; ?>" <?php echo ($property->direction == $direction->slug) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($direction->name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="bedrooms" class="form-label">Số phòng ngủ</label>
                                    <select class="form-select" id="bedrooms" name="bedrooms">
                                        <option value="">-- Chọn số phòng ngủ --</option>
                                        <?php for ($i = 0; $i <= 10; $i++): ?>
                                            <option value="<?php echo $i; ?>" <?php echo ($property->bedrooms == $i) ? 'selected' : ''; ?>>
                                                <?php echo $i; ?>
                                            </option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="bathrooms" class="form-label">Số phòng tắm</label>
                                    <select class="form-select" id="bathrooms" name="bathrooms">
                                        <option value="">-- Chọn số phòng tắm --</option>
                                        <?php for ($i = 0; $i <= 10; $i++): ?>
                                            <option value="<?php echo $i; ?>" <?php echo ($property->bathrooms == $i) ? 'selected' : ''; ?>>
                                                <?php echo $i; ?>
                                            </option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="video_url" class="form-label">URL video YouTube</label>
                            <input type="url" class="form-control" id="video_url" name="video_url" placeholder="https://www.youtube.com/watch?v=..." value="<?php echo htmlspecialchars($property->video_url ?? ''); ?>">
                            <div class="form-text">Nhập URL video YouTube (nếu có)</div>
                        </div>

                        <div class="form-group">
                            <label for="title" class="form-label">Tiêu đề <span class="required">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required value="<?php echo htmlspecialchars($property->title); ?>">
                        </div>

                        <div class="form-group">
                            <label for="description" class="form-label">Mô tả <span class="required">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="5" required><?php echo htmlspecialchars($property->description); ?></textarea>
                        </div>

                        <!-- Thêm trường slug ẩn để giữ giá trị -->
                        <input type="hidden" name="slug" value="<?php echo htmlspecialchars($property->slug); ?>">

                        <!-- Hình ảnh -->
                        <div class="form-group">
                            <h5 class="card-title border-bottom pb-2 mt-4">Hình ảnh</h5>

                            <div class="alert alert-info bg-light-info border-info">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-info-circle-fill text-primary me-2"></i>
                                    <strong>Yêu cầu hình ảnh:</strong>
                                </div>
                                <ul class="mb-0 ps-4">
                                    <li>Tối đa 10 hình ảnh</li>
                                    <li>Kích thước tối thiểu 300x300 pixels</li>
                                    <li>Định dạng: JPG, JPEG, PNG</li>
                                    <li>Hình ảnh sẽ được tự động tối ưu hóa (nén và thay đổi kích thước)</li>
                                    <li><strong>Mới:</strong> Bạn có thể kéo và thả để sắp xếp lại thứ tự hình ảnh</li>
                                </ul>
                            </div>

                            <!-- Dropzone Upload Area -->
                            <div class="dropzone-container mb-4">
                                <div id="property-images-upload" class="dropzone">
                                    <div class="fallback">
                                        <input name="file" type="file" multiple />
                                    </div>
                                    <div class="dz-message needsclick">
                                        <i class="bi bi-cloud-arrow-up text-primary fs-1 mb-3"></i>
                                        <p class="mb-2">Kéo và thả hình ảnh vào đây hoặc nhấp để chọn</p>
                                        <span class="text-muted small">Hình ảnh sẽ được tự động tối ưu hóa</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Preview Area -->
                            <div class="image-preview-container mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="form-label fw-bold mb-0">Hình ảnh hiện tại</h6>
                                    <div class="drag-hint small text-muted <?php echo (empty($propertyImages) || count($propertyImages) <= 1) ? 'd-none' : ''; ?>" id="drag-hint">
                                        <i class="bi bi-arrows-move me-1"></i> Kéo để sắp xếp lại thứ tự
                                    </div>
                                </div>
                                <div class="row g-3" id="image-preview-row">
                                    <?php if (empty($propertyImages)): ?>
                                    <div class="col-12 text-center text-muted py-4 border rounded bg-light" id="no-images-message">
                                        <i class="bi bi-images d-block mb-2 fs-3"></i>
                                        Chưa có hình ảnh nào được tải lên
                                    </div>
                                    <?php else: ?>
                                        <?php foreach ($propertyImages as $image): ?>
                                        <div class="col-md-3 col-sm-6 image-preview-item" data-image="<?php echo $image->image_path; ?>">
                                            <div class="card h-100">
                                                <div class="position-relative">
                                                    <img src="/thuenhadanang/public/uploads/properties/<?php echo $image->image_path; ?>"
                                                         class="card-img-top preview-image" alt="Property Image"
                                                         style="height: 150px; object-fit: cover;">
                                                    <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 m-2 remove-image"
                                                            data-image="<?php echo $image->image_path; ?>" title="Xóa ảnh">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                                <div class="card-body p-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input main-image-radio" type="radio"
                                                               name="main_image" value="<?php echo $image->image_path; ?>"
                                                               id="main_image_<?php echo $image->id; ?>"
                                                               <?php echo ($image->is_main) ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="main_image_<?php echo $image->id; ?>">
                                                            Đặt làm ảnh chính
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Hidden input for image order -->
                            <input type="hidden" name="image_order" id="image_order" value="">
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <a href="/thuenhadanang/dashboard/properties" class="btn btn-outline-secondary me-2">Hủy</a>
                            <button type="submit" class="btn btn-primary">
                                Cập nhật tin đăng <i class="bi bi-check-circle ms-1"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Template for image preview -->
<template id="image-preview-template">
    <div class="col-md-3 col-sm-6 image-preview-item">
        <div class="card h-100">
            <div class="position-relative">
                <img src="" class="card-img-top preview-image" style="height: 150px; object-fit: cover;">
                <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 m-2 remove-image"
                        data-filename="" title="Xóa ảnh">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
            <div class="card-body p-2">
                <div class="form-check">
                    <input class="form-check-input main-image-radio" type="radio" name="main_image" value="">
                    <label class="form-check-label">Đặt làm ảnh chính</label>
                </div>
            </div>
        </div>
    </div>
</template>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-5">
                <div class="spinner-border text-primary mb-4" style="width: 3rem; height: 3rem;" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5 class="mb-2">Đang xử lý...</h5>
                <p class="text-muted mb-0">Vui lòng đợi trong giây lát</p>
            </div>
        </div>
    </div>
</div>

<!-- Include jQuery first -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Include Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<!-- Include Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<!-- JavaScript for property edit form -->
<script>
$(document).ready(function() {
    // Initialize Select2 for dropdowns
    $('.ward-select').select2({
        theme: 'bootstrap-5',
        placeholder: '-- Chọn phường/xã --',
        width: '100%',
        language: {
            noResults: function() {
                return "Không tìm thấy kết quả";
            },
            searching: function() {
                return "Đang tìm kiếm...";
            }
        }
    });

    // Initialize Sortable for drag and drop functionality
    const imagePreviewRow = document.getElementById('image-preview-row');
    if (imagePreviewRow) {
        const sortable = new Sortable(imagePreviewRow, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            handle: '.card',
            filter: '#no-images-message',
            onEnd: function() {
                updateImageOrder();

                // Show drag hint if more than one image
                if ($('.image-preview-item').length > 1) {
                    $('#drag-hint').removeClass('d-none');
                } else {
                    $('#drag-hint').addClass('d-none');
                }
            }
        });
    }

    // Function to update image order
    function updateImageOrder() {
        const imageOrder = [];
        const processedImages = new Set(); // Sử dụng Set để tránh trùng lặp

        $('.image-preview-item').each(function() {
            const filename = $(this).data('image');
            if (filename && !processedImages.has(filename)) {
                imageOrder.push(filename);
                processedImages.add(filename);
            }
        });

        $('#image_order').val(JSON.stringify(imageOrder));
        console.log('Image order updated:', imageOrder);
    }

    // Initialize image order on page load
    updateImageOrder();

    // Đảm bảo Dropzone.autoDiscover = false đã được đặt ở đầu trang
    try {
        // Initialize Dropzone for image uploads
        const myDropzone = new Dropzone("#property-images-upload", {
        url: "/thuenhadanang/property/upload-image/<?php echo $property->id; ?>",
        method: "post",
        paramName: "file",
        maxFilesize: 10, // MB
        maxFiles: 10,
        acceptedFiles: "image/jpeg,image/jpg,image/png",
        addRemoveLinks: false,
        autoProcessQueue: true,
        uploadMultiple: false,
        parallelUploads: 1,
        createImageThumbnails: true,
        clickable: true,
        previewsContainer: document.createElement('div'),
        dictDefaultMessage: "Kéo và thả hình ảnh vào đây hoặc nhấp để chọn",
        dictFallbackMessage: "Trình duyệt của bạn không hỗ trợ kéo và thả tệp.",
        dictFileTooBig: "Tệp quá lớn ({{filesize}}MB). Kích thước tối đa: {{maxFilesize}}MB.",
        dictInvalidFileType: "Định dạng tệp không hợp lệ. Chỉ chấp nhận JPG, JPEG, PNG.",
        dictResponseError: "Lỗi máy chủ: {{statusCode}}.",
        dictMaxFilesExceeded: "Bạn chỉ có thể tải lên tối đa {{maxFiles}} hình ảnh.",
        init: function() {
            const dz = this;

            // Count existing images
            const existingImageCount = $('.image-preview-item').length;

            this.on("maxfilesexceeded", function(file) {
                this.removeFile(file);
                alert("Bạn chỉ có thể tải lên tối đa 10 hình ảnh.");
            });

            this.on("addedfile", function(file) {
                // Check if we've reached the maximum number of files
                if (existingImageCount + dz.files.length > 10) {
                    dz.removeFile(file);
                    alert("Bạn chỉ có thể tải lên tối đa 10 hình ảnh.");
                    return;
                }

                // Show loading indicator in the dropzone
                const dropzoneElement = document.querySelector('#property-images-upload');
                const messageElement = dropzoneElement.querySelector('.dz-message');
                if (messageElement) {
                    messageElement.innerHTML = `
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-2" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mb-0">Đang xử lý hình ảnh...</p>
                        </div>
                    `;
                }

                // Optimize the image before uploading
                if (file.type.match(/image.*/)) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = new Image();
                        img.onload = function() {
                            // Check image dimensions
                            if (img.width < 300 || img.height < 300) {
                                dz.removeFile(file);
                                alert("Hình ảnh phải có kích thước tối thiểu 300x300 pixels.");
                                return;
                            }

                            // Compress the image
                            new Compressor(file, {
                                quality: 0.8,
                                maxWidth: 1000,
                                maxHeight: 1000,
                                convertSize: 1000000,
                                convertTypes: ['image/png', 'image/jpeg'],
                                mimeType: 'image/jpeg',
                                success(result) {
                                    // Replace the original file with the compressed one
                                    const compressedFile = new File([result], file.name, {
                                        type: result.type,
                                        lastModified: Date.now()
                                    });

                                    // Hiển thị thông tin về file đã nén
                                    console.log('Compressed file size:', (compressedFile.size / 1024 / 1024).toFixed(2) + 'MB',
                                                'Compression ratio:', ((1 - compressedFile.size / file.size) * 100).toFixed(0) + '%');

                                    // Tính toán tỷ lệ nén
                                    const compressionRatio = ((1 - compressedFile.size / file.size) * 100).toFixed(0);

                                    // Update loading indicator in the dropzone
                                    const dropzoneElement = document.querySelector('#property-images-upload');
                                    const messageElement = dropzoneElement.querySelector('.dz-message');
                                    if (messageElement) {
                                        messageElement.innerHTML = `
                                            <div class="text-center">
                                                <div class="spinner-border text-primary mb-2" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                                <p class="mb-0">Đang tải lên hình ảnh (đã giảm ${compressionRatio}%)...</p>
                                            </div>
                                        `;
                                    }

                                    // Create FormData and append the compressed file
                                    const formData = new FormData();
                                    formData.append('file', compressedFile);

                                    // Upload the compressed file via fetch API
                                    fetch(dz.options.url, {
                                        method: 'POST',
                                        body: formData
                                    })
                                    .then(response => response.json())
                                    .then(data => {
                                        // Remove the file from Dropzone
                                        dz.removeFile(file);

                                        if (data.success) {
                                            // Add the image to the preview area
                                            addImageToPreview(data.filename, data.filepath);

                                            // Show success message
                                            showToast('success', 'Tải lên thành công', 'Hình ảnh đã được tải lên thành công.');

                                            // Reset dropzone message
                                            resetDropzoneMessage();
                                        } else {
                                            showToast('error', 'Lỗi tải lên', data.message || 'Có lỗi xảy ra khi tải lên hình ảnh.');
                                            resetDropzoneMessage();
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Upload error:', error);
                                        dz.removeFile(file);
                                        showToast('error', 'Lỗi tải lên', 'Có lỗi xảy ra khi tải lên hình ảnh.');
                                        resetDropzoneMessage();
                                    });
                                },
                                error(err) {
                                    console.error('Compression error:', err);
                                    dz.removeFile(file);
                                    showToast('error', 'Lỗi tối ưu hóa', 'Có lỗi xảy ra khi tối ưu hóa hình ảnh.');
                                    resetDropzoneMessage();
                                }
                            });
                        };
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            });
        }
    });
    } catch (error) {
        console.error("Dropzone initialization error:", error);
        // Fallback for file upload if Dropzone fails
        const fileInput = document.querySelector('.fallback input[type="file"]');
        if (fileInput) {
            fileInput.style.display = 'block';
            document.querySelector('.dz-message').innerHTML = '<p class="text-danger">Không thể khởi tạo trình tải lên. Vui lòng sử dụng nút tải lên thông thường.</p>';
        }
    }

    // Function to add image to preview area
    function addImageToPreview(filename, filepath) {
        // Remove no images message if it exists
        $('#no-images-message').remove();

        // Kiểm tra xem hình ảnh đã tồn tại chưa để tránh trùng lặp
        const existingImage = $(`.image-preview-item[data-image="${filename}"]`);
        if (existingImage.length > 0) {
            console.log('Image already exists in preview, skipping:', filename);
            return;
        }

        // Clone the template
        const template = document.getElementById('image-preview-template');
        const clone = document.importNode(template.content, true);

        // Set image source and data attributes
        const imagePreviewItem = clone.querySelector('.image-preview-item');
        imagePreviewItem.dataset.image = filename;

        const img = clone.querySelector('img');
        img.src = filepath;
        img.alt = 'Property Image';

        // Add loading effect and fade-in
        img.style.opacity = '0';
        img.onload = function() {
            img.style.transition = 'opacity 0.3s ease';
            img.style.opacity = '1';
        };

        const removeButton = clone.querySelector('.remove-image');
        removeButton.dataset.image = filename;

        const radio = clone.querySelector('.main-image-radio');
        radio.value = filename;
        radio.id = 'main_image_' + Date.now();

        const label = clone.querySelector('.form-check-label');
        label.setAttribute('for', radio.id);

        // If this is the first image, set it as main
        if ($('.image-preview-item').length === 0) {
            radio.checked = true;
        }

        // Append to preview row
        $('#image-preview-row').append(imagePreviewItem);

        // Update image order
        updateImageOrder();

        // Show drag hint if more than one image
        if ($('.image-preview-item').length > 1) {
            $('#drag-hint').removeClass('d-none');
        }

        // Attach event handlers
        $(removeButton).on('click', handleImageRemoval);

        // Log for debugging
        console.log('Added image to preview:', filename);
    }

    // Handle image deletion
    function handleImageRemoval() {
        if (confirm('Bạn có chắc chắn muốn xóa hình ảnh này?')) {
            const filename = $(this).data('image');
            const imageItem = $(this).closest('.image-preview-item');

            // Create or update hidden input for deleted images
            let deletedImagesInput = $('#deleted_images');
            if (deletedImagesInput.length === 0) {
                deletedImagesInput = $('<input type="hidden" id="deleted_images" name="deleted_images" value="[]">');
                $('#propertyEditForm').append(deletedImagesInput);
            }

            const deletedImages = JSON.parse(deletedImagesInput.val());
            deletedImages.push(filename);
            deletedImagesInput.val(JSON.stringify(deletedImages));

            // Log for debugging
            console.log('Marked image for deletion:', filename);

            // If the image is a temp image, also send a request to delete it from the server
            if (filename.startsWith('temp_')) {
                // Send request to remove file from server
                fetch('/thuenhadanang/property/delete-image/<?php echo $property->id; ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `filename=${filename}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('Temp file deleted from server:', filename);
                    } else {
                        console.error('Failed to delete temp file from server:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error deleting temp file:', error);
                });
            }

            // Remove the image item from DOM with animation
            imageItem.fadeOut(300, function() {
                $(this).remove();

                // If the removed image was the main image, select another one
                if ($('.main-image-radio:checked').length === 0 && $('.image-preview-item').length > 0) {
                    $('.main-image-radio:first').prop('checked', true);
                }

                // If no images left, show message
                if ($('.image-preview-item').length === 0) {
                    $('#image-preview-row').html(
                        '<div class="col-12 text-center text-muted py-4 border rounded bg-light" id="no-images-message">' +
                        '<i class="bi bi-images d-block mb-2 fs-3"></i>' +
                        'Chưa có hình ảnh nào được tải lên' +
                        '</div>'
                    );
                    $('#drag-hint').addClass('d-none');
                }

                // Update image order
                updateImageOrder();
            });
        }
    }

    // Attach event handler to existing remove buttons
    $('.remove-image').on('click', handleImageRemoval);

    // Function to reset dropzone message
    function resetDropzoneMessage() {
        const dropzoneElement = document.querySelector('#property-images-upload');
        if (dropzoneElement) {
            const messageElement = dropzoneElement.querySelector('.dz-message');
            if (messageElement) {
                messageElement.innerHTML = `
                    <i class="bi bi-cloud-arrow-up text-primary fs-1 mb-3"></i>
                    <p class="mb-2">Kéo và thả hình ảnh vào đây hoặc nhấp để chọn</p>
                    <span class="text-muted small">Hình ảnh sẽ được tự động tối ưu hóa</span>
                `;
            }
        }
    }

    // Function to show toast notifications
    function showToast(type, title, message) {
        // Create toast container if it doesn't exist
        if ($('.toast-container').length === 0) {
            $('body').append('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
        }

        // Create toast
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="5000">
                <div class="toast-header ${type === 'success' ? 'bg-success-light' : 'bg-danger-light'}">
                    <i class="bi ${type === 'success' ? 'bi-check-circle' : 'bi-exclamation-triangle'} me-2"></i>
                    <strong class="me-auto">${title}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        // Append and show toast
        $('.toast-container').append(toastHtml);
        const toastElement = new bootstrap.Toast(document.getElementById(toastId));
        toastElement.show();

        // Remove toast after it's hidden
        $(`#${toastId}`).on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }

    // Form validation
    $('#propertyEditForm').on('submit', function(e) {
        let isValid = true;
        const requiredFields = $(this).find('[required]');

        requiredFields.each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;

                // Add validation feedback if not exists
                if ($(this).next('.invalid-feedback').length === 0) {
                    $(this).after('<div class="invalid-feedback">Trường này là bắt buộc</div>');
                }
            } else {
                $(this).removeClass('is-invalid').addClass('is-valid');
                $(this).next('.invalid-feedback').remove();
            }
        });

        if (!isValid) {
            e.preventDefault();

            // Scroll to first invalid field
            $('html, body').animate({
                scrollTop: $('.is-invalid:first').offset().top - 100
            }, 200);

            // Show validation message
            const alertHtml =
                '<div class="alert alert-danger alert-dismissible fade show mb-4">' +
                '<i class="bi bi-exclamation-triangle-fill me-2"></i> Vui lòng điền đầy đủ thông tin bắt buộc.' +
                '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                '</div>';

            if ($('.alert-danger').length === 0) {
                $(this).prepend(alertHtml);
            }
        } else {
            // Show loading modal
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            loadingModal.show();
        }
    });

    // Live validation on blur
    $('#propertyEditForm [required]').on('blur', function() {
        if (!$(this).val().trim()) {
            $(this).addClass('is-invalid');

            // Add validation feedback if not exists
            if ($(this).next('.invalid-feedback').length === 0) {
                $(this).after('<div class="invalid-feedback">Trường này là bắt buộc</div>');
            }
        } else {
            $(this).removeClass('is-invalid').addClass('is-valid');
            $(this).next('.invalid-feedback').remove();
        }
    });
});
</script>
