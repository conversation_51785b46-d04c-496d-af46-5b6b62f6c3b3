<?php
// Trang đăng nhập cho admin
require_once __DIR__ . '/../config.php'; 
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../app/libraries/Database.php';
require_once __DIR__ . '/../app/models/User.php';

// Khởi tạo session
session_start();

// Redirect nếu đã đăng nhập
if(isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit();
}

// Khởi tạo đối tượng User
$userModel = new User();

// Xử lý đăng nhập
$error = '';
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Lấy dữ liệu từ form
    $email = trim($_POST['email']);
    $password = trim($_POST['password']);
    
    // Kiểm tra form
    if(empty($email) || empty($password)) {
        $error = '<PERSON><PERSON> lòng nhập đầy đủ thông tin';
    } else {
        // <PERSON><PERSON>c thực người dùng
        $user = $userModel->login($email, $password);
        
        // Kiểm tra quyền admin
        if($user && $user->role == 'admin') {
            // Lưu thông tin vào session
            $_SESSION['admin_id'] = $user->id;
            $_SESSION['admin_name'] = $user->fullname;
            $_SESSION['admin_email'] = $user->email;
            
            // Chuyển hướng đến trang quản trị
            header('Location: index.php');
            exit();
        } else {
            $error = 'Email hoặc mật khẩu không đúng, hoặc bạn không có quyền truy cập';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đăng nhập - Quản trị website</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 2rem;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .login-logo {
            text-align: center;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-logo">
            <h2>Quản trị website</h2>
            <p>Thuê Nhà Đà Nẵng</p>
        </div>
        
        <?php if($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>">
            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" name="email" required>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">Mật khẩu</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">Đăng nhập</button>
            </div>
        </form>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 