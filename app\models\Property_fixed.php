<?php
require_once __DIR__ . '/../libraries/Database.php';

class Property {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    // L<PERSON>y tất cả bất động sản
    public function getAllProperties() {
        $this->db->query('SELECT p.*, pt.name as type_name, u.fullname as owner_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN users u ON p.user_id = u.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          ORDER BY p.created_at DESC');
        return $this->db->resultSet();
    }

    // Lấy bất động sản theo ID
    public function getPropertyById($id) {
        $this->db->query('SELECT p.*, pt.name as type_name, u.fullname as owner_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN users u ON p.user_id = u.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          WHERE p.id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    // Lấy bất động sản theo slug
    public function getPropertyBySlug($slug) {
        $this->db->query('SELECT p.*, pt.name as type_name, u.fullname as owner_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN users u ON p.user_id = u.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          WHERE p.slug = :slug');
        $this->db->bind(':slug', $slug);
        return $this->db->single();
    }

    // Thêm bất động sản mới
    public function create($data) {
        $this->db->query('INSERT INTO properties (user_id, title, slug, description, type_id, address, ward_id, street, city, area, price, price_period, bedrooms, bathrooms, direction, video_url, images, main_image, status, featured)
                          VALUES (:user_id, :title, :slug, :description, :type_id, :address, :ward_id, :street, :city, :area, :price, :price_period, :bedrooms, :bathrooms, :direction, :video_url, :images, :main_image, :status, :featured)');

        // Bind values
        $this->db->bind(':user_id', $data['user_id']);
        $this->db->bind(':title', $data['title']);
        $this->db->bind(':slug', $data['slug']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':type_id', $data['type_id']);
        $this->db->bind(':address', $data['address']);
        $this->db->bind(':ward_id', $data['ward_id'] ?? null);
        $this->db->bind(':street', $data['street'] ?? null);
        $this->db->bind(':city', $data['city'] ?? 'Đà Nẵng');
        $this->db->bind(':area', $data['area'] ?? null);
        $this->db->bind(':price', $data['price']);
        $this->db->bind(':price_period', $data['price_period'] ?? 'month');
        $this->db->bind(':bedrooms', $data['bedrooms'] ?? null);
        $this->db->bind(':bathrooms', $data['bathrooms'] ?? null);
        $this->db->bind(':direction', $data['direction'] ?? null);
        $this->db->bind(':video_url', $data['video_url'] ?? null);
        $this->db->bind(':images', $data['images'] ?? null);
        $this->db->bind(':main_image', $data['main_image'] ?? null);
        $this->db->bind(':status', $data['status'] ?? 'available');
        $this->db->bind(':featured', $data['featured'] ?? 0);

        // Execute
        if ($this->db->execute()) {
            // Lấy ID của bản ghi vừa thêm
            $propertyId = $this->db->lastInsertId();

            // Ghi log để debug
            error_log('Property created with ID: ' . $propertyId);

            return $propertyId;
        } else {
            error_log('Failed to create property');
            return false;
        }
    }

    // Cập nhật bất động sản
    public function update($data) {
        $this->db->query('UPDATE properties SET
                          title = :title,
                          slug = :slug,
                          description = :description,
                          type_id = :type_id,
                          address = :address,
                          ward_id = :ward_id,
                          street = :street,
                          city = :city,
                          area = :area,
                          price = :price,
                          price_period = :price_period,
                          bedrooms = :bedrooms,
                          bathrooms = :bathrooms,
                          direction = :direction,
                          video_url = :video_url,
                          images = :images,
                          main_image = :main_image,
                          status = :status,
                          featured = :featured
                          WHERE id = :id');

        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':title', $data['title']);
        $this->db->bind(':slug', $data['slug']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':type_id', $data['type_id']);
        $this->db->bind(':address', $data['address']);
        $this->db->bind(':ward_id', $data['ward_id'] ?? null);
        $this->db->bind(':street', $data['street'] ?? null);
        $this->db->bind(':city', $data['city'] ?? 'Đà Nẵng');
        $this->db->bind(':area', $data['area'] ?? null);
        $this->db->bind(':price', $data['price']);
        $this->db->bind(':price_period', $data['price_period'] ?? 'month');
        $this->db->bind(':bedrooms', $data['bedrooms'] ?? null);
        $this->db->bind(':bathrooms', $data['bathrooms'] ?? null);
        $this->db->bind(':direction', $data['direction'] ?? null);
        $this->db->bind(':video_url', $data['video_url'] ?? null);
        $this->db->bind(':images', $data['images'] ?? null);
        $this->db->bind(':main_image', $data['main_image'] ?? null);
        $this->db->bind(':status', $data['status'] ?? 'available');
        $this->db->bind(':featured', $data['featured'] ?? 0);

        // Execute
        $result = $this->db->execute();

        // Ghi log để debug
        error_log('Property update result: ' . ($result ? 'success' : 'failed') . ' for ID: ' . $data['id']);

        return $result;
    }

    // Xóa bất động sản
    public function delete($id) {
        $this->db->query('DELETE FROM properties WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }

    // Kiểm tra xem slug đã tồn tại chưa (trừ ID hiện tại)
    public function isSlugExists($slug, $id = 0) {
        $this->db->query('SELECT COUNT(*) as count FROM properties WHERE slug = :slug AND id != :id');
        $this->db->bind(':slug', $slug);
        $this->db->bind(':id', $id);
        $row = $this->db->single();
        return $row->count > 0;
    }

    // Tạo slug từ tiêu đề
    public function createSlug($title) {
        // Chuyển đổi tiếng Việt sang không dấu
        $slug = $this->convertToSlug($title);

        // Kiểm tra xem slug đã tồn tại chưa
        $originalSlug = $slug;
        $count = 1;

        while ($this->isSlugExists($slug)) {
            $slug = $originalSlug . '-' . $count;
            $count++;
        }

        return $slug;
    }

    // Hàm chuyển đổi tiếng Việt sang không dấu
    private function convertToSlug($string) {
        $string = trim($string);
        $string = preg_replace('/[^a-zA-Z0-9ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễếệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ\s]/', '', $string);
        $string = preg_replace('/[\s]+/', ' ', $string);
        $string = str_replace(' ', '-', $string);

        $string = strtolower($string);
        $string = preg_replace('/[àáạảãâầấậẩẫăằắặẳẵ]/u', 'a', $string);
        $string = preg_replace('/[èéẹẻẽêềếệểễ]/u', 'e', $string);
        $string = preg_replace('/[ìíịỉĩ]/u', 'i', $string);
        $string = preg_replace('/[òóọỏõôồốộổỗơờớợởỡ]/u', 'o', $string);
        $string = preg_replace('/[ùúụủũưừứựửữ]/u', 'u', $string);
        $string = preg_replace('/[ỳýỵỷỹ]/u', 'y', $string);
        $string = preg_replace('/đ/u', 'd', $string);

        return $string;
    }

    // Lấy tất cả hình ảnh của bất động sản
    public function getPropertyImages($propertyId) {
        // Lấy thông tin bất động sản
        $this->db->query('SELECT images, main_image FROM properties WHERE id = :id');
        $this->db->bind(':id', $propertyId);
        $property = $this->db->single();

        if ($property && $property->images) {
            // Giải mã JSON thành mảng
            $images = json_decode($property->images, true);

            if (is_array($images)) {
                $result = [];
                foreach ($images as $index => $image) {
                    $isMain = ($property->main_image === $image);
                    $result[] = (object)[
                        'id' => $propertyId . '_' . $index, // Tạo ID có định dạng "propertyId_imageIndex"
                        'property_id' => $propertyId,
                        'image_path' => $image,
                        'is_main' => $isMain ? 1 : 0
                    ];
                }

                // Sắp xếp để ảnh chính hiển thị đầu tiên
                usort($result, function($a, $b) {
                    return $b->is_main - $a->is_main;
                });

                return $result;
            }
        }

        return [];
    }

    // Thêm hình ảnh cho bất động sản
    public function addPropertyImage($propertyId, $imagePath, $isMain = 0) {
        // Lấy thông tin bất động sản
        $this->db->query('SELECT images, main_image FROM properties WHERE id = :id');
        $this->db->bind(':id', $propertyId);
        $property = $this->db->single();

        // Khởi tạo mảng hình ảnh
        $images = [];
        if ($property && $property->images) {
            $images = json_decode($property->images, true) ?? [];
        }

        // Thêm hình ảnh mới vào mảng
        $images[] = $imagePath;

        // Cập nhật main_image nếu cần
        $mainImage = $property->main_image;
        if ($isMain || empty($mainImage)) {
            $mainImage = $imagePath;
        }

        // Cập nhật bảng properties
        $this->db->query('UPDATE properties SET images = :images, main_image = :main_image WHERE id = :id');
        $this->db->bind(':id', $propertyId);
        $this->db->bind(':images', json_encode($images));
        $this->db->bind(':main_image', $mainImage);

        $result = $this->db->execute();

        // Ghi log để debug
        error_log('PropertyImage add result: ' . ($result ? 'success' : 'failed') . ' for property_id: ' . $propertyId . ', image: ' . $imagePath);

        return $result;
    }

    // Lấy tất cả hướng bất động sản
    public function getAllDirections() {
        $this->db->query('SELECT * FROM property_directions WHERE status = 1 ORDER BY name ASC');
        return $this->db->resultSet();
    }

    // Lấy bất động sản nổi bật
    public function getFeaturedProperties($limit = 12) {
        $this->db->query('SELECT p.*, pt.name as type_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          WHERE p.featured = 1 AND p.status = "available"
                          ORDER BY p.created_at DESC
                          LIMIT :limit');
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }

    // Lấy bất động sản theo loại
    public function getPropertiesByType($typeId, $limit = 12) {
        $this->db->query('SELECT p.*, pt.name as type_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          WHERE p.type_id = :type_id AND p.status = "available"
                          ORDER BY p.created_at DESC
                          LIMIT :limit');
        $this->db->bind(':type_id', $typeId);
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }

    // Lấy bất động sản theo slug loại
    public function getPropertiesByTypeSlug($typeSlug, $limit = 12) {
        $this->db->query('SELECT p.*, pt.name as type_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          WHERE pt.slug = :type_slug AND p.status = "available"
                          ORDER BY p.created_at DESC
                          LIMIT :limit');
        $this->db->bind(':type_slug', $typeSlug);
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }

    // Lấy các bất động sản tương tự
    public function getSimilarProperties($typeId, $currentId, $limit = 4) {
        $this->db->query('SELECT p.*, pt.name as type_name, u.fullname as owner_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN users u ON p.user_id = u.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          WHERE p.type_id = :type_id AND p.id != :current_id AND p.status = "available"
                          ORDER BY p.featured DESC, p.created_at DESC
                          LIMIT :limit');

        $this->db->bind(':type_id', $typeId);
        $this->db->bind(':current_id', $currentId);
        $this->db->bind(':limit', $limit);

        return $this->db->resultSet();
    }
}
