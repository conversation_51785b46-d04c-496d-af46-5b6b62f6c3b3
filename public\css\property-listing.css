/*
 * Property Listing Form Styles
 * Custom styles for the multi-step property listing form
 */

:root {
    --primary-color: #ff6b00;
    --primary-hover: #e05f00;
    --primary-light: #fff0e6;
    --success-color: #198754;
    --success-light: rgba(25, 135, 84, 0.1);
    --danger-color: #dc3545;
    --danger-light: rgba(220, 53, 69, 0.1);
    --warning-color: #ffc107;
    --warning-light: rgba(255, 193, 7, 0.1);
    --info-color: #0d6efd;
    --info-light: rgba(13, 110, 253, 0.1);
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
}

/* Form container styling */
.property-listing-form {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Progress indicator styling */
.step-progress {
    height: 6px;
    border-radius: 3px;
    background-color: #f0f0f0;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.step-progress .progress-bar {
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

/* Step indicators */
.step-indicators {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.step-indicator {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.step-indicator.active {
    background-color: var(--primary-color);
    color: #fff;
    box-shadow: 0 2px 8px rgba(255, 107, 0, 0.2);
}

.step-indicator:not(.active) {
    background-color: #f5f5f5;
    color: #666;
}

/* Form group styling */
.form-group {
    margin-bottom: 1.5rem;
}

/* Form label styling */
.property-listing-form .form-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #444;
    margin-bottom: 0.5rem;
    display: block;
}

.property-listing-form .form-label .required {
    color: var(--primary-color);
    margin-left: 2px;
}

/* Form control styling */
.property-listing-form .form-control,
.property-listing-form .form-select {
    height: 45px;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 1px solid #e2e2e2;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    background-color: #fff;
}

.property-listing-form .form-control:focus,
.property-listing-form .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 107, 0, 0.15);
}

.property-listing-form .form-control::placeholder {
    color: #aaa;
    font-size: 0.9rem;
}

/* Textarea styling */
.property-listing-form textarea.form-control {
    height: auto;
    min-height: 120px;
    resize: vertical;
}

/* Form text (help text) styling */
.property-listing-form .form-text {
    font-size: 0.8rem;
    color: #888;
    margin-top: 0.25rem;
}

/* Button styling */
.property-listing-form .btn {
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.property-listing-form .btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.property-listing-form .btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.property-listing-form .btn-outline-secondary {
    border-color: #ddd;
    color: #666;
}

.property-listing-form .btn-outline-secondary:hover {
    background-color: #f5f5f5;
    border-color: #ccc;
    color: #444;
}

/* Invalid form control styling */
.property-listing-form .form-control.is-invalid,
.property-listing-form .form-select.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Alert styling */
.property-listing-form .alert {
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

/* Dropzone styling */
.dropzone-container {
    border: 2px dashed #ddd !important;
    border-radius: 8px;
    padding: 2rem !important;
    background-color: #f9f9f9;
    transition: all 0.2s ease;
}

.dropzone-container:hover {
    border-color: var(--primary-color) !important;
    background-color: #fff0e6;
}

.dropzone {
    min-height: 150px;
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
}

.dropzone .dz-message {
    margin: 2rem 0;
    font-size: 1rem;
    color: #666;
}

.dropzone .dz-preview {
    margin: 1rem;
}

/* Image preview styling */
.image-preview-container {
    margin-top: 2rem;
}

.image-preview-item .card {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
    border: 1px solid #eee;
}

.image-preview-item .card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-preview-item .card-img-top {
    height: 150px;
    object-fit: cover;
}

.image-preview-item .form-check {
    margin-bottom: 0.5rem;
}

.image-preview-item .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.image-preview-item .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.image-preview-item .btn-danger:hover {
    background-color: #bb2d3b;
    border-color: #bb2d3b;
}

/* Modal styling */
.modal-content {
    border-radius: 12px;
    border: none;
    overflow: hidden;
}

.modal-body {
    padding: 2rem;
}

/* Select2 styling */
.select2-container--bootstrap-5 .select2-selection {
    min-height: 45px;
    padding: 0.5rem 1rem;
    font-size: 0.95rem;
    border: 1px solid #e2e2e2;
    border-radius: 8px;
}

.select2-container--bootstrap-5.select2-container--focus .select2-selection,
.select2-container--bootstrap-5.select2-container--open .select2-selection {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 107, 0, 0.15);
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
    padding: 0;
    line-height: 1.5;
    color: #444;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__placeholder {
    color: #aaa;
}

.select2-container--bootstrap-5 .select2-dropdown {
    border-color: var(--primary-color);
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.select2-container--bootstrap-5 .select2-dropdown .select2-search .select2-search__field {
    border-radius: 4px;
    padding: 0.5rem;
    border: 1px solid #e2e2e2;
}

.select2-container--bootstrap-5 .select2-dropdown .select2-search .select2-search__field:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 107, 0, 0.15);
}

.select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
    background-color: var(--primary-color);
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
    height: 45px;
}

/* Validation styles for Select2 */
.is-invalid-select2 {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.is-invalid-select2 .select2-selection {
    border-color: #dc3545 !important;
}

.is-valid-select2 .select2-selection {
    border-color: #198754 !important;
}

/* Additional Select2 styling for better UX */
.select2-dropdown-ward {
    max-width: 100%;
    z-index: 1060 !important; /* Ensure dropdown appears above other elements */
}

.select2-container-ward .select2-selection__clear {
    margin-right: 10px;
    color: #999;
}

.select2-container-ward .select2-selection__clear:hover {
    color: #dc3545;
}

.select2-container--bootstrap-5 .select2-results__option {
    padding: 0.5rem 1rem;
}

.select2-container--bootstrap-5 .select2-results__option--selected {
    background-color: rgba(255, 107, 0, 0.1);
    color: var(--primary-color);
}

.select2-container--bootstrap-5 .select2-results__option--highlighted.select2-results__option--selected {
    background-color: var(--primary-color);
    color: white;
}

/* Ensure dropdown is full width on mobile */
.select2-container {
    width: 100% !important;
}

/* Mobile responsive styles for Select2 */
@media (max-width: 576px) {
    .select2-container--bootstrap-5 .select2-selection {
        min-height: 42px;
        font-size: 0.9rem;
    }

    .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
        height: 42px;
    }

    /* Ensure dropdown is full width and properly positioned on mobile */
    .select2-container--bootstrap-5 .select2-dropdown {
        width: auto !important;
        left: 0 !important;
        right: 0 !important;
        margin-left: 0 !important;
        border-radius: 0 0 8px 8px;
    }

    /* Improve search field on mobile */
    .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
        font-size: 16px; /* Prevent iOS zoom on focus */
        padding: 10px;
    }

    /* Make dropdown options easier to tap on mobile */
    .select2-container--bootstrap-5 .select2-results__option {
        padding: 10px;
        min-height: 44px; /* Apple's recommended minimum touch target size */
    }
}

/* Toast notifications */
.toast-container {
    z-index: 1060;
}

.toast {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.toast.show {
    opacity: 1;
}

/* Background colors for alerts and toasts */
.bg-light-info {
    background-color: rgba(13, 110, 253, 0.1);
}

.bg-success-light {
    background-color: rgba(25, 135, 84, 0.1);
}

.bg-danger-light {
    background-color: rgba(220, 53, 69, 0.1);
}

.bg-warning-light {
    background-color: rgba(255, 193, 7, 0.1);
}

/* Dropzone processing indicator */
.dz-processing-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Dropzone message styling */
.dropzone .dz-message {
    text-align: center;
    margin: 2rem 0;
}

.dropzone .dz-message p {
    margin-bottom: 0.25rem;
    font-weight: 500;
    color: #444;
}

.dropzone .dz-message .small {
    font-size: 0.85rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .property-listing-form .form-control,
    .property-listing-form .form-select {
        height: 42px;
        font-size: 0.9rem;
    }

    .step-indicator {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
    }

    .dropzone .dz-message {
        font-size: 0.9rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }
}

@media (max-width: 576px) {
    .property-listing-form .form-label {
        font-size: 0.85rem;
    }

    .property-listing-form .btn {
        padding: 0.4rem 1rem;
        font-size: 0.9rem;
    }

    .image-preview-item {
        width: 50%;
    }

    .step-indicators {
        flex-direction: column;
        align-items: center;
    }

    .step-indicator {
        margin-bottom: 0.5rem;
        width: 100%;
        text-align: center;
    }
}
