<?php
require_once 'app/models/User.php';

class ProfileController {
    private $userModel;

    public function __construct() {
        $this->userModel = new User();
    }

    public function index() {
        session_start();
        
        // Redirect if not logged in
        if (!isset($_SESSION['user_id'])) {
            header('Location: /thuenhadanang/login');
            exit();
        }

        // Get user data
        $user = $this->userModel->getUserById($_SESSION['user_id']);
        
        $data = [
            'title' => 'Thông tin tài khoản',
            'user' => $user
        ];

        $view = 'profile';
        require_once 'app/views/layout.php';
    }

    public function update() {
        session_start();
        
        // Redirect if not logged in
        if (!isset($_SESSION['user_id'])) {
            header('Location: /thuenhadanang/login');
            exit();
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $userId = $_SESSION['user_id'];
            
            // L<PERSON>y dữ liệu từ form
            $updateData = [
                'fullname' => trim($_POST['fullname']),
                'email' => trim($_POST['email']),
                'phone' => trim($_POST['phone']),
                'zalo' => trim($_POST['zalo']),
                'address' => trim($_POST['address'])
            ];

            // Validate required fields
            if (empty($updateData['fullname'])) {
                $_SESSION['error_message'] = 'Họ và tên không được để trống';
                header('Location: /thuenhadanang/dashboard/profile');
                exit();
            }

            if (empty($updateData['email'])) {
                $_SESSION['error_message'] = 'Email không được để trống';
                header('Location: /thuenhadanang/dashboard/profile');
                exit();
            }

            if (!filter_var($updateData['email'], FILTER_VALIDATE_EMAIL)) {
                $_SESSION['error_message'] = 'Email không hợp lệ';
                header('Location: /thuenhadanang/dashboard/profile');
                exit();
            }

            // Kiểm tra email đã tồn tại chưa (trừ email hiện tại của user)
            $existingUser = $this->userModel->getUserByEmail($updateData['email']);
            if ($existingUser && $existingUser->id != $userId) {
                $_SESSION['error_message'] = 'Email này đã được sử dụng';
                header('Location: /thuenhadanang/dashboard/profile');
                exit();
            }

            // Update user data
            if ($this->userModel->updateUser($userId, $updateData)) {
                $_SESSION['success_message'] = 'Cập nhật thông tin thành công';
                
                // Cập nhật session data nếu cần
                if (isset($_SESSION['user_name'])) {
                    $_SESSION['user_name'] = $updateData['fullname'];
                }
            } else {
                $_SESSION['error_message'] = 'Có lỗi xảy ra khi cập nhật thông tin';
            }

            header('Location: /thuenhadanang/dashboard/profile');
            exit();
        }
    }
} 