<div class="container my-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/thuenhadanang" class="text-decoration-none">Trang chủ</a></li>
            <li class="breadcrumb-item"><a href="/thuenhadanang/properties/<?php echo $property->type_id ? strtolower(str_replace(' ', '-', $property->type_name)) : 'all'; ?>" class="text-decoration-none"><?php echo $property->type_name ?? 'Bất động sản'; ?></a></li>
            <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($property->title); ?></li>
        </ol>
    </nav>
    <!-- End Breadcrumb -->

    <?php if ($viewStatus != 'normal'): ?>
    <!-- Thông báo trạng thái tin đăng -->
    <div class="alert alert-<?php
        echo $viewStatus == 'pending' ? 'warning' :
            ($viewStatus == 'hidden' ? 'danger' :
                ($viewStatus == 'expired' ? 'secondary' : 'info'));
    ?> mb-4">
        <div class="d-flex align-items-center">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <strong><?php echo $statusMessage; ?></strong>
        </div>
        <?php if ($viewStatus == 'pending'): ?>
            <div class="mt-2 small">Tin đăng của bạn sẽ được hiển thị công khai sau khi được phê duyệt.</div>
        <?php elseif ($viewStatus == 'hidden'): ?>
            <div class="mt-2">
                <a href="/thuenhadanang/cho-thue-nha-dat" class="btn btn-outline-light btn-sm mt-2">
                    <i class="bi bi-arrow-left"></i> Quay lại danh sách bất động sản
                </a>
            </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- Cột trái -->
        <div class="col-lg-8">
            <!-- Thư viện ảnh -->
            <div class="property-gallery card mb-4">
                <div class="card-body p-0">
                    <!-- Main Image -->
                    <div class="gallery-main swiper">
                        <div class="swiper-wrapper">
                            <?php foreach ($images as $index => $image): ?>
                            <div class="swiper-slide">
                                <a href="<?php echo $image; ?>" data-fancybox="gallery">
                                    <img src="<?php echo $image; ?>" alt="<?php echo htmlspecialchars($property->title) . ' - Hình ' . ($index + 1); ?>">
                                </a>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Navigation buttons -->
                        <button class="gallery-nav gallery-prev">
                            <i class="bi bi-chevron-left"></i>
                        </button>
                        <button class="gallery-nav gallery-next">
                            <i class="bi bi-chevron-right"></i>
                        </button>
                    </div>

                    <!-- Thumbnail Slider -->
                    <div class="swiper gallery-thumbs">
                        <div class="swiper-wrapper">
                            <?php foreach ($images as $index => $image): ?>
                            <div class="swiper-slide">
                                <img src="<?php echo $image; ?>" alt="<?php echo htmlspecialchars($property->title) . ' - Thumbnail ' . ($index + 1); ?>">
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                </div>
            </div>

            <!-- Thông tin cơ bản -->
            <div class="property-basic-info card mb-4 <?php echo $viewStatus == 'pending' ? 'border-warning' : ($viewStatus == 'expired' ? 'border-secondary' : ''); ?>">
                <div class="card-body">
                    <?php if ($viewStatus == 'pending'): ?>
                    <div class="pending-badge position-absolute top-0 end-0 mt-2 me-2">
                        <span class="badge bg-warning">Đang chờ duyệt</span>
                    </div>
                    <?php elseif ($viewStatus == 'expired'): ?>
                    <div class="expired-badge position-absolute top-0 end-0 mt-2 me-2">
                        <span class="badge bg-secondary">Đã hết hạn</span>
                    </div>
                    <?php endif; ?>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="post-time text-muted">
                            <i class="bi bi-clock"></i> Đăng ngày: <?php echo date('d/m/Y', strtotime($property->created_at)); ?>
                            <?php if ($property->expiration_date): ?>
                            <span class="ms-2">
                                <i class="bi bi-calendar-event"></i> Hết hạn: <?php echo date('d/m/Y', strtotime($property->expiration_date)); ?>
                            </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <h1 class="property-title mb-3"><?php echo htmlspecialchars($property->title); ?></h1>
                    <div class="property-address mb-3">
                        <i class="bi bi-geo-alt-fill text-primary"></i>
                        <?php echo htmlspecialchars($property->address); ?>
                    </div>
                    <div class="property-price mb-0">
                        <?php echo $formattedPrice; ?>
                    </div>
                </div>
            </div>

            <!-- Thông tin chính -->
            <div class="property-main-info card mb-4">
                <div class="card-body">
                    <h2 class="h5 mb-4">Thông tin chính</h2>
                    <div class="row g-3">
                        <div class="col-6 col-md-4">
                            <div class="info-item">
                                <div class="label text-muted">Danh mục</div>
                                <div class="value fw-bold"><?php echo htmlspecialchars($property->type_name); ?></div>
                            </div>
                        </div>
                        <div class="col-6 col-md-4">
                            <div class="info-item">
                                <div class="label text-muted">Diện tích</div>
                                <div class="value fw-bold"><?php echo $property->area; ?> m²</div>
                            </div>
                        </div>
                        <?php if ($property->bedrooms): ?>
                        <div class="col-6 col-md-4">
                            <div class="info-item">
                                <div class="label text-muted">Phòng ngủ</div>
                                <div class="value fw-bold"><?php echo $property->bedrooms; ?> PN</div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if ($property->bathrooms): ?>
                        <div class="col-6 col-md-4">
                            <div class="info-item">
                                <div class="label text-muted">Phòng tắm</div>
                                <div class="value fw-bold"><?php echo $property->bathrooms; ?> WC</div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if ($property->direction): ?>
                        <div class="col-6 col-md-4">
                            <div class="info-item">
                                <div class="label text-muted">Hướng</div>
                                <div class="value fw-bold"><?php echo htmlspecialchars($property->direction); ?></div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="col-6 col-md-4">
                            <div class="info-item">
                                <div class="label text-muted">Khu vực</div>
                                <div class="value fw-bold"><?php echo htmlspecialchars($property->ward_name); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mô tả chi tiết -->
            <div class="property-description card mb-4">
                <div class="card-body">
                    <h2 class="h5 mb-4">Thông tin mô tả</h2>
                    <div class="description">
                        <?php echo nl2br(htmlspecialchars($property->description)); ?>
                    </div>
                </div>
            </div>


        </div>

        <!-- Cột phải -->
        <div class="col-lg-4">
            <!-- Thông tin người đăng -->
            <div class="owner-info card mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <?php
                        // Luôn sử dụng avatar mặc định cho tin đăng free
                        $avatarUrl = '/thuenhadanang/public/uploads/users/default-avatar.jpg';
                        if (!$isFreeProperty && $owner && !empty($owner->avatar)) {
                            $avatarUrl = '/thuenhadanang/public/uploads/users/' . $owner->avatar;
                        }
                        ?>
                        <img src="<?php echo $avatarUrl; ?>" class="rounded-circle avatar-lg me-3" alt="Avatar">
                        <div>
                            <?php if ($isFreeProperty && $contactInfo): ?>
                                <h3 class="h5 mb-1"><?php echo htmlspecialchars($contactInfo->name); ?></h3>
                                <p class="text-muted small mb-0">
                                    <i class="bi bi-clock" style="font-size: 12px;"></i> Tham gia: <?php echo date('d/m/Y', strtotime($property->created_at)); ?>
                                </p>
                            <?php else: ?>
                                <h3 class="h5 mb-1"><?php echo $owner ? htmlspecialchars($owner->fullname) : 'Chủ nhà'; ?></h3>
                                <p class="text-muted small mb-0">
                                    <i class="bi bi-clock"></i> Tham gia: <?php echo $owner ? date('d/m/Y', strtotime($owner->created_at)) : 'N/A'; ?>
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if (!$isExpired): ?>
                    <div class="d-grid gap-2">
                        <?php if ($isFreeProperty && $contactInfo): ?>
                            <!-- Nút điện thoại với data-phone được mã hóa -->
                            <div class="btn btn-primary phone-btn" data-phone="<?php echo base64_encode($contactInfo->phone); ?>">
                                <i class="bi bi-telephone"></i> <span class="phone-placeholder">Nhấn để hiện số</span>
                            </div>
                            <?php if (!empty($contactInfo->zalo)): ?>
                            <!-- Nút Zalo với data-zalo được mã hóa -->
                            <div class="btn btn-outline-primary zalo-btn" data-zalo="<?php echo base64_encode($contactInfo->zalo); ?>">
                                <i class="bi bi-chat"></i> Chat Zalo
                            </div>
                            <?php elseif (!empty($contactInfo->email)): ?>
                            <a href="mailto:<?php echo $contactInfo->email; ?>" class="btn btn-outline-primary">
                                <i class="bi bi-envelope"></i> Gửi email
                            </a>
                            <?php endif; ?>
                        <?php elseif ($owner && !empty($owner->phone)): ?>
                            <!-- Nút điện thoại cho chủ sở hữu -->
                            <div class="btn btn-primary phone-btn" data-phone="<?php echo base64_encode($owner->phone); ?>">
                                <i class="bi bi-telephone"></i> <span class="phone-placeholder">Nhấn để hiện số</span>
                            </div>
                            <?php if (!empty($owner->zalo) || !empty($owner->phone)): ?>
                            <!-- Nút Zalo cho chủ sở hữu -->
                            <div class="btn btn-outline-primary zalo-btn" data-zalo="<?php echo base64_encode(!empty($owner->zalo) ? $owner->zalo : $owner->phone); ?>">
                                <i class="bi bi-chat"></i> Chat Zalo
                            </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <!-- Thông tin liên hệ mặc định -->
                            <div class="btn btn-primary phone-btn" data-phone="<?php echo base64_encode('0905123456'); ?>">
                                <i class="bi bi-telephone"></i> <span class="phone-placeholder">Nhấn để hiện số</span>
                            </div>
                            <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                                <i class="bi bi-envelope"></i> Gửi email
                            </a>
                        <?php endif; ?>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-secondary mt-3 mb-0">
                        <i class="bi bi-info-circle-fill me-2"></i> Tin đã hết hạn
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Lưu ý quan trọng -->
            <div class="important-notes card mb-4">
                <div class="card-body">
                    <h4 class="h6 mb-3">Lưu ý quan trọng</h4>
                    <div class="notes">
                        <div class="note-item mb-3">
                            <i class="bi bi-exclamation-circle text-warning"></i>
                            <span>Chỉ đặt cọc khi xác định được chủ nhà và có thỏa thuận biên nhận rõ ràng</span>
                        </div>
                        <div class="note-item mb-3">
                            <i class="bi bi-exclamation-circle text-warning"></i>
                            <span>Kiểm tra mọi điều khoản và yêu cầu liệt kê tất cả chi phí hàng tháng vào hợp đồng</span>
                        </div>
                        <div class="note-item">
                            <i class="bi bi-exclamation-circle text-warning"></i>
                            <span>Trường hợp phát hiện nội dung tin đăng không chính xác, vui lòng phản ánh đến Thuenhadanang</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Các nút tương tác -->
            <div class="action-buttons d-flex gap-2">
                <button class="btn btn-outline-primary flex-grow-1">
                    <i class="bi bi-bookmark"></i> Lưu tin
                </button>
                <button class="btn btn-outline-primary flex-grow-1">
                    <i class="bi bi-share"></i> Chia sẻ
                </button>
                <button class="btn btn-outline-danger flex-grow-1">
                    <i class="bi bi-flag"></i> Báo xấu
                </button>
            </div>

            <!-- Quảng bá đăng tin -->
            <div class="promote-post card mt-4">
                <div class="card-body text-center">
                    <div class="promote-icon mb-3">
                        <i class="bi bi-megaphone text-primary" style="font-size: 2.5rem;"></i>
                    </div>
                    <h4 class="h5 mb-3">Đăng tin cho thuê miễn phí</h4>
                    <p class="text-muted mb-3">
                        Tiếp cận hàng nghìn khách thuê tiềm năng với tin đăng miễn phí của bạn
                    </p>
                    <div class="promote-features mb-4">
                        <div class="feature-item d-flex align-items-center mb-2">
                            <i class="bi bi-check-circle-fill text-primary me-2"></i>
                            <span>Đăng tin hoàn toàn miễn phí</span>
                        </div>
                        <div class="feature-item d-flex align-items-center mb-2">
                            <i class="bi bi-check-circle-fill text-primary me-2"></i>
                            <span>Được duyệt nhanh trong 24h</span>
                        </div>
                        <div class="feature-item d-flex align-items-center">
                            <i class="bi bi-check-circle-fill text-primary me-2"></i>
                            <span>Hiển thị 30 ngày trên website</span>
                        </div>
                    </div>
                    <a href="#" class="btn btn-primary w-100">
                        <i class="bi bi-plus-circle me-2"></i>Đăng tin ngay
                    </a>
                </div>
            </div>

            <!-- Tư vấn hỗ trợ -->
            <div class="support-card card mt-4">
                <div class="card-body text-center">
                    <div class="support-icon mb-3">
                        <i class="bi bi-headset text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <h4 class="h6 mb-3">Cần tư vấn thêm?</h4>
                    <p class="text-muted small mb-3">
                        Chúng tôi luôn sẵn sàng hỗ trợ bạn 24/7
                    </p>
                    <div class="d-grid gap-2">
                        <a href="tel:1900xxxx" class="btn btn-outline-primary">
                            <i class="bi bi-telephone-fill me-2"></i>1900.xxxx
                        </a>
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                            <i class="bi bi-envelope-fill me-2"></i>Gửi email
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tách riêng phần bất động sản tương tự -->
    <?php if (!empty($similarProperties)): ?>
    <div class="row mt-5">
        <div class="col-12">
            <div class="similar-properties">
                <h2 class="h5 mb-4"><?php echo $property->type_name; ?> tương tự</h2>
                <div class="properties-grid">
                    <?php foreach ($similarProperties as $similarProperty):
                        // Định dạng giá
                        $priceInMillions = $similarProperty->price / 1000000;
                        $formattedPrice = '';

                        if ($priceInMillions >= 1) {
                            // Sử dụng abs() để tránh lỗi với số âm và round() để làm tròn
                            $remainder = abs(round($priceInMillions * 10) % 10);
                            $formattedPrice = $remainder === 0
                                ? (int)$priceInMillions . ' triệu'
                                : number_format($priceInMillions, 1) . ' triệu';
                        } else {
                            $formattedPrice = number_format($similarProperty->price, 0, ',', '.') . ' đồng';
                        }

                        // Thêm đơn vị thời gian
                        switch ($similarProperty->price_period) {
                            case 'day':
                                $formattedPrice .= '/ngày';
                                break;
                            case 'week':
                                $formattedPrice .= '/tuần';
                                break;
                            case 'month':
                                $formattedPrice .= '/tháng';
                                break;
                            case 'quarter':
                                $formattedPrice .= '/quý';
                                break;
                            case 'year':
                                $formattedPrice .= '/năm';
                                break;
                        }

                        // Lấy hình ảnh
                        $propertyImage = '/thuenhadanang/public/uploads/properties/default-property.jpg';
                        if (!empty($similarProperty->main_image)) {
                            $propertyImage = '/thuenhadanang/public/uploads/properties/' . $similarProperty->main_image;
                        }
                    ?>
                        <div class="property-card" data-featured="<?php echo $similarProperty->featured ? 'true' : 'false'; ?>"
                             data-new="<?php echo (strtotime($similarProperty->created_at) > strtotime('-7 days')) ? 'true' : 'false'; ?>">
                            <div class="property-image-container">
                                <a href="/thuenhadanang/<?php echo $similarProperty->slug; ?>-<?php echo $similarProperty->id; ?>">
                                    <img src="<?php echo $propertyImage; ?>" class="property-image" alt="<?php echo htmlspecialchars($similarProperty->title); ?>">
                                </a>

                                <!-- Property Badges -->
                                <div class="property-badges">
                                    <?php if ($similarProperty->featured): ?>
                                    <span class="property-badge featured-badge">Nổi bật</span>
                                    <?php endif; ?>
                                </div>

                                <!-- Wishlist Button -->
                                <button class="wishlist-btn" data-property-id="<?php echo $similarProperty->id; ?>">
                                    <i class="bi bi-heart"></i>
                                </button>
                            </div>

                            <div class="property-content">
                                <div class="property-header">
                                    <h3 class="property-title">
                                        <a href="/thuenhadanang/<?php echo $similarProperty->slug; ?>-<?php echo $similarProperty->id; ?>">
                                            <?php echo htmlspecialchars($similarProperty->title); ?>
                                        </a>
                                    </h3>
                                    <div class="property-location">
                                        <i class="bi bi-geo-alt"></i>
                                        <span><?php echo htmlspecialchars($similarProperty->ward_name); ?></span>
                                    </div>
                                </div>

                                <div class="property-features">
                                    <?php if ($similarProperty->area): ?>
                                    <span class="feature-item">
                                        <i class="bi bi-rulers"></i>
                                        <?php echo $similarProperty->area; ?>m²
                                    </span>
                                    <?php endif; ?>
                                    <?php if ($similarProperty->bedrooms): ?>
                                    <span class="feature-item">
                                        <i class="bi bi-door-closed"></i>
                                        <?php echo $similarProperty->bedrooms; ?> PN
                                    </span>
                                    <?php endif; ?>
                                    <?php if ($similarProperty->bathrooms): ?>
                                    <span class="feature-item">
                                        <i class="bi bi-droplet"></i>
                                        <?php echo $similarProperty->bathrooms; ?> WC
                                    </span>
                                    <?php endif; ?>
                                </div>

                                <div class="property-footer">
                                    <div class="property-price">
                                        <span class="current-price"><?php echo $formattedPrice; ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>