<?php

// Test area filtering for 90+ m² issue
define('BASE_PATH', __DIR__);
define('APP_PATH', BASE_PATH . '/app');

// Load required files
require_once APP_PATH . '/libraries/Database.php';
require_once APP_PATH . '/libraries/UrlHandler.php';
require_once APP_PATH . '/models/PropertyType.php';
require_once APP_PATH . '/models/Ward.php';
require_once APP_PATH . '/models/Property.php';

echo "<h1>Test Area 90+ Filtering Issue</h1>\n";

try {
    // Test URL parsing first
    $urlHandler = new UrlHandler();
    $testUrl = 'cho-thue-nha-dat-tai-an-hai-bac/gia-tren-15-trieu-dt-tren-90m2-2pn';
    
    echo "<h2>1. URL Parsing Test</h2>\n";
    echo "<p><strong>Testing URL:</strong> $testUrl</p>\n";
    
    $result = $urlHandler->parseUrl($testUrl);
    
    echo "<h3>Parsed Result:</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Type:</strong> '" . $result['type'] . "'</li>\n";
    echo "<li><strong>Ward:</strong> '" . $result['ward'] . "'</li>\n";
    echo "<li><strong>Price:</strong> '" . $result['price'] . "'</li>\n";
    echo "<li><strong>Area:</strong> '" . $result['area'] . "' (Expected: '90+')</li>\n";
    echo "<li><strong>Bedrooms:</strong> '" . $result['bedrooms'] . "'</li>\n";
    echo "<li><strong>Matched:</strong> " . ($result['matched'] ? 'Yes' : 'No') . "</li>\n";
    echo "</ul>\n";
    
    if ($result['area'] !== '90+') {
        echo "<p style='color: red;'>❌ <strong>PROBLEM:</strong> Area parsing is incorrect. Expected '90+', got '{$result['area']}'</p>\n";
    } else {
        echo "<p style='color: green;'>✅ Area parsing is correct</p>\n";
    }
    
    // Test Property model query
    echo "<h2>2. Property Model Query Test</h2>\n";
    
    $propertyModel = new Property();
    
    // Test the exact query that would be used
    echo "<p>Testing Property model query with area='90+'...</p>\n";
    
    $properties = $propertyModel->getPropertiesByTypeAndWard(
        '', // type (empty for nha-dat)
        'an-hai-bac', // ward
        '15+', // price
        '90+', // area - this is the problem
        '2', // bedrooms
        'default', // sort
        '1', // bathrooms
        '' // direction
    );
    
    echo "<p><strong>Query Result:</strong> " . count($properties) . " properties found</p>\n";
    
    // Check all properties in the ward to see what areas are available
    echo "<h2>3. All Properties in Ward Analysis</h2>\n";
    
    $allProperties = $propertyModel->getPropertiesByTypeAndWard('', 'an-hai-bac', '', '', '', 'default', '', '');
    
    echo "<p><strong>All properties in An Hải Bắc:</strong> " . count($allProperties) . "</p>\n";
    
    if (count($allProperties) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>ID</th><th>Title</th><th>Area (m²)</th><th>Price (VND)</th><th>Bedrooms</th><th>Meets 90+ Criteria</th></tr>\n";
        
        foreach ($allProperties as $prop) {
            $meetsAreaCriteria = ($prop->area >= 90);
            $rowColor = $meetsAreaCriteria ? '#e8f5e8' : '#ffe8e8';
            $criteriaIcon = $meetsAreaCriteria ? '✅' : '❌';
            
            echo "<tr style='background-color: $rowColor;'>\n";
            echo "<td>{$prop->id}</td>\n";
            echo "<td>" . htmlspecialchars(substr($prop->title, 0, 50)) . "...</td>\n";
            echo "<td><strong>{$prop->area}</strong></td>\n";
            echo "<td>" . number_format($prop->price) . "</td>\n";
            echo "<td>{$prop->bedrooms}</td>\n";
            echo "<td>$criteriaIcon (≥90m²)</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // Test manual SQL query
    echo "<h2>4. Manual SQL Query Test</h2>\n";
    
    $db = new Database();
    
    // Test the exact SQL that should be generated
    $sql = 'SELECT p.*, pt.name as type_name, pt.slug as type_slug, u.fullname as owner_name, w.name as ward_name, w.slug as ward_slug
            FROM properties p
            LEFT JOIN property_types pt ON p.type_id = pt.id
            LEFT JOIN users u ON p.user_id = u.id
            LEFT JOIN wards w ON p.ward_id = w.id
            WHERE p.status = "display" AND p.active = 1
            AND (p.expiration_date IS NULL OR p.expiration_date > NOW())
            AND LOWER(w.slug) = LOWER(:ward_slug)
            AND p.price >= :min_price
            AND p.area >= :min_area
            AND p.bedrooms = :bedrooms';
    
    echo "<h3>SQL Query:</h3>\n";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>\n";
    
    echo "<h3>Parameters:</h3>\n";
    echo "<ul>\n";
    echo "<li>ward_slug: 'an-hai-bac'</li>\n";
    echo "<li>min_price: 15000000</li>\n";
    echo "<li>min_area: 90</li>\n";
    echo "<li>bedrooms: 2</li>\n";
    echo "</ul>\n";
    
    $db->query($sql);
    $db->bind(':ward_slug', 'an-hai-bac');
    $db->bind(':min_price', 15000000);
    $db->bind(':min_area', 90);
    $db->bind(':bedrooms', 2);
    
    $manualResults = $db->resultSet();
    
    echo "<p><strong>Manual SQL Results:</strong> " . count($manualResults) . " properties</p>\n";
    
    if (count($manualResults) > 0) {
        echo "<h4>Properties that should match (area ≥ 90m²):</h4>\n";
        foreach ($manualResults as $prop) {
            echo "<div style='border: 1px solid #green; padding: 10px; margin: 5px; background-color: #e8f5e8;'>\n";
            echo "<strong>ID:</strong> {$prop->id}<br>\n";
            echo "<strong>Title:</strong> " . htmlspecialchars($prop->title) . "<br>\n";
            echo "<strong>Area:</strong> {$prop->area} m²<br>\n";
            echo "<strong>Price:</strong> " . number_format($prop->price) . " VND<br>\n";
            echo "<strong>Bedrooms:</strong> {$prop->bedrooms}<br>\n";
            echo "</div>\n";
        }
    }
    
    // Test if the issue is in the Property model logic
    echo "<h2>5. Property Model Logic Analysis</h2>\n";
    
    // Check how the Property model handles area='90+'
    echo "<p>Testing how Property model processes area='90+'...</p>\n";
    
    $area = '90+';
    echo "<p><strong>Input area:</strong> '$area'</p>\n";
    
    if (strpos($area, '+') !== false) {
        $minArea = floatval(str_replace('+', '', $area));
        echo "<p><strong>Processed minArea:</strong> $minArea</p>\n";
        echo "<p><strong>SQL condition would be:</strong> p.area >= $minArea</p>\n";
        
        // This should work correctly
        echo "<p style='color: green;'>✅ Property model logic for area='90+' looks correct</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Property model is not processing area='90+' correctly</p>\n";
    }
    
    // Check if the issue is in URL parsing
    echo "<h2>6. URL Parsing Deep Dive</h2>\n";
    
    $filterPart = 'gia-tren-15-trieu-dt-tren-90m2-2pn';
    $filterSegments = explode('-', $filterPart);
    
    echo "<p><strong>Filter part:</strong> $filterPart</p>\n";
    echo "<p><strong>Filter segments:</strong></p>\n";
    echo "<pre>" . print_r($filterSegments, true) . "</pre>\n";
    
    // Manual parsing of area
    for ($i = 0; $i < count($filterSegments); $i++) {
        $segment = $filterSegments[$i];
        if ($segment == 'dt') {
            echo "<p>Found 'dt' at index $i</p>\n";
            
            // Check for dt-tren-X-m2 pattern
            if (isset($filterSegments[$i+1]) && $filterSegments[$i+1] == 'tren' &&
                isset($filterSegments[$i+2]) && isset($filterSegments[$i+3]) &&
                $filterSegments[$i+3] == 'm2') {
                
                $areaValue = $filterSegments[$i+2] . '+';
                echo "<p><strong>Parsed area:</strong> '$areaValue'</p>\n";
                
                if ($areaValue === '90+') {
                    echo "<p style='color: green;'>✅ URL parsing for dt-tren-90m2 is correct</p>\n";
                } else {
                    echo "<p style='color: red;'>❌ URL parsing for dt-tren-90m2 is incorrect</p>\n";
                }
                break;
            }
        }
    }
    
    echo "<h2>7. Conclusion</h2>\n";
    
    if ($result['area'] === '90+' && count($manualResults) == 0) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4 style='color: #856404;'>🔍 ROOT CAUSE IDENTIFIED</h4>\n";
        echo "<p style='color: #856404;'>The URL parsing and Property model logic are working correctly.</p>\n";
        echo "<p style='color: #856404;'><strong>The issue is:</strong> There are no properties in the database that meet ALL criteria:</p>\n";
        echo "<ul style='color: #856404;'>\n";
        echo "<li>Ward: An Hải Bắc</li>\n";
        echo "<li>Price: ≥ 15 million VND</li>\n";
        echo "<li>Area: ≥ 90 m²</li>\n";
        echo "<li>Bedrooms: 2</li>\n";
        echo "<li>Bathrooms: 1</li>\n";
        echo "</ul>\n";
        echo "<p style='color: #856404;'>If you're seeing a 76m² property, it means the system is showing properties that don't match the area filter.</p>\n";
        echo "</div>\n";
    } else if ($result['area'] !== '90+') {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4 style='color: #721c24;'>❌ URL PARSING ISSUE</h4>\n";
        echo "<p style='color: #721c24;'>The URL parsing is not correctly extracting area='90+' from the URL.</p>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4 style='color: #155724;'>✅ SYSTEM WORKING CORRECTLY</h4>\n";
        echo "<p style='color: #155724;'>URL parsing and database queries are working as expected.</p>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

?>
