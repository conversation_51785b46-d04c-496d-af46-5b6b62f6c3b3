<?php
// Main App Core
class App {
    protected $controller = 'HomeController';
    protected $method = 'index';
    protected $params = [];

    public function __construct() {
        // Define constants
        define('BASE_PATH', dirname(__DIR__));
        define('APP_PATH', BASE_PATH . '/app');
        define('CORE_PATH', BASE_PATH . '/core');
        define('PUBLIC_PATH', BASE_PATH . '/public');

        // Auto load controllers
        spl_autoload_register(function($className) {
            $controllerPath = APP_PATH . '/controllers/' . $className . '.php';
            if (file_exists($controllerPath)) {
                require_once $controllerPath;
            }
        });

        $url = $this->parseUrl();

        // Controller
        if(isset($url[0])) {
            $controllerName = ucfirst($url[0]) . 'Controller';
            if(file_exists(APP_PATH . '/controllers/' . $controllerName . '.php')) {
                $this->controller = $controllerName;
            unset($url[0]);
            }
        }

        require_once APP_PATH . '/controllers/' . $this->controller . '.php';
        $this->controller = new $this->controller;

        // Method
        if(isset($url[1])) {
            if(method_exists($this->controller, $url[1])) {
            $this->method = $url[1];
            unset($url[1]);
            }
        }

        // Params
        $this->params = $url ? array_values($url) : [];

        call_user_func_array([$this->controller, $this->method], $this->params);
    }

    protected function parseUrl() {
        if(isset($_GET['url'])) {
            return explode('/', filter_var(rtrim($_GET['url'], '/'), FILTER_SANITIZE_URL));
        }
        return [];
    }
}
