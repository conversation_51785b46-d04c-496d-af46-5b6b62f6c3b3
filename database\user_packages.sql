-- <PERSON><PERSON><PERSON> bảng cũ nếu tồn tại
DROP TABLE IF EXISTS `user_packages`;

-- <PERSON><PERSON><PERSON> bảng user_packages (gói dịch vụ của người dùng)
CREATE TABLE `user_packages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'ID người dùng',
  `package_id` int(11) NOT NULL COMMENT 'ID gói dịch vụ',
  `start_date` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'Ngày bắt đầu',
  `end_date` timestamp NOT NULL COMMENT 'Ng<PERSON>y kết thúc',
  `posts_used` int(11) NOT NULL DEFAULT 0 COMMENT 'Số tin đã sử dụng',
  `posts_remaining` int(11) NOT NULL DEFAULT 0 COMMENT 'Số tin còn lại',
  `status` enum('active','expired','cancelled') NOT NULL DEFAULT 'active' COMMENT 'Trạng thái gói',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `package_id` (`package_id`),
  CONSTRAINT `user_packages_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_packages_ibfk_2` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
