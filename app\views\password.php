<?php
// Ensure session is started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="container mt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/thuenhadanang" class="text-decoration-none">Trang chủ</a></li>
        <li class="breadcrumb-item"><a href="/thuenhadanang/dashboard" class="text-decoration-none">Bảng điều khiển</a></li>
        <li class="breadcrumb-item active" aria-current="page">Đ<PERSON>i mật khẩu</li>
    </ol>
</nav>

<div class="container py-4">
    <div class="row">
        <!-- Include Sidebar -->
        <?php require_once 'app/views/partials/dashboard-sidebar.php'; ?>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Debug Session Messages -->
            <?php
            if (isset($_SESSION)) {
                echo '<!-- Session data: ' . print_r($_SESSION, true) . ' -->';
            }
            ?>
            
            <!-- Thông báo -->
            <?php if(isset($_SESSION['success_message']) && !empty($_SESSION['success_message'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>
                    <?php 
                    echo htmlspecialchars($_SESSION['success_message']); 
                    unset($_SESSION['success_message']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if(isset($_SESSION['error_message']) && !empty($_SESSION['error_message'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-circle me-2"></i>
                    <?php 
                    echo htmlspecialchars($_SESSION['error_message']); 
                    unset($_SESSION['error_message']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <div class="d-flex align-items-center">
                        <div>
                            <h5 class="card-title mb-0">Đổi mật khẩu</h5>
                            <small class="text-muted">Cập nhật mật khẩu tài khoản của bạn</small>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form action="/thuenhadanang/dashboard/password/update" method="POST" class="needs-validation" novalidate>
                        <div class="row g-3">
                            <div class="col-12">
                                <div class="form-floating">
                                    <input type="password" class="form-control border-2" id="current_password" name="current_password" 
                                           placeholder="Nhập mật khẩu hiện tại" required>
                                    <label for="current_password">Mật khẩu hiện tại</label>
                                    <div class="invalid-feedback">
                                        Vui lòng nhập mật khẩu hiện tại
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-floating">
                                    <input type="password" class="form-control border-2" id="new_password" name="new_password" 
                                           placeholder="Nhập mật khẩu mới" required minlength="6">
                                    <label for="new_password">Mật khẩu mới</label>
                                    <div class="invalid-feedback">
                                        Mật khẩu phải có ít nhất 6 ký tự
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-floating">
                                    <input type="password" class="form-control border-2" id="confirm_password" name="confirm_password" 
                                           placeholder="Xác nhận mật khẩu mới" required>
                                    <label for="confirm_password">Xác nhận mật khẩu mới</label>
                                    <div class="invalid-feedback">
                                        Vui lòng xác nhận mật khẩu mới
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <button type="reset" class="btn btn-light border-2">
                                <i class="bi bi-arrow-counterclockwise me-2"></i>Đặt lại
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>Cập nhật mật khẩu
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Password Requirements Card -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white py-3">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-shield-lock me-2"></i>Yêu cầu mật khẩu
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li>
                            <i class="bi bi-check-circle text-success me-2"></i>Tối thiểu 6 ký tự
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control {
    border-color: #dee2e6;
}
.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    color: #0d6efd;
    opacity: 0.65;
}
.btn-primary {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
}
.btn-light {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}
.btn-light:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
}
</style>

<script>
// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()

// Toggle password visibility
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (field.type === "password") {
        field.type = "text";
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
    } else {
        field.type = "password";
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
    }
}

// Add password toggle buttons
document.querySelectorAll('input[type="password"]').forEach(field => {
    const button = document.createElement('button');
    button.type = 'button';
    button.className = 'btn btn-outline-secondary position-absolute end-0 top-50 translate-middle-y me-2';
    button.innerHTML = '<i class="bi bi-eye"></i>';
    button.onclick = () => togglePassword(field.id);
    field.parentElement.style.position = 'relative';
    field.parentElement.appendChild(button);
});

// Check password match
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword !== confirmPassword) {
        this.setCustomValidity('Mật khẩu xác nhận không khớp');
    } else {
        this.setCustomValidity('');
    }
});

document.getElementById('new_password').addEventListener('input', function() {
    const confirmPassword = document.getElementById('confirm_password');
    if (confirmPassword.value) {
        if (this.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Mật khẩu xác nhận không khớp');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
});
</script> 