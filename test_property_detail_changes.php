<?php
/**
 * Test Property Detail Changes
 * Kiểm tra các thay đổi trong trang chi tiết bất động sản
 */

// Prevent direct access
if (!defined('TESTING_MODE')) {
    define('TESTING_MODE', true);
}

class PropertyDetailTester {
    private $results = [];
    private $errors = [];
    
    public function __construct() {
        $this->log("🏠 Property Detail Changes Tester initialized");
    }
    
    /**
     * Run all tests
     */
    public function runAllTests() {
        $this->log("🚀 Starting Property Detail Changes Tests...");
        
        // Test 1: Check map section removal
        $this->testMapSectionRemoval();
        
        // Test 2: Check similar properties structure
        $this->testSimilarPropertiesStructure();
        
        // Test 3: Check CSS updates
        $this->testCSSUpdates();
        
        // Test 4: Check HTML structure consistency
        $this->testHTMLStructureConsistency();
        
        $this->generateReport();
    }
    
    /**
     * Test map section removal
     */
    private function testMapSectionRemoval() {
        $this->log("🗺️ Testing map section removal...");
        
        $propertyDetailFile = __DIR__ . '/app/views/property-detail.php';
        
        if (!file_exists($propertyDetailFile)) {
            $this->addError("Property detail file not found: $propertyDetailFile");
            return;
        }
        
        $content = file_get_contents($propertyDetailFile);
        
        // Check that map section is removed
        $mapSectionExists = strpos($content, 'Vị trí trên bản đồ') !== false;
        $mapContainerExists = strpos($content, 'property-map card') !== false;
        $mapDivExists = strpos($content, 'id="map"') !== false;
        
        if (!$mapSectionExists && !$mapContainerExists && !$mapDivExists) {
            $this->addResult("✅ Map section successfully removed");
        } else {
            $this->addError("❌ Map section still exists in property detail");
            if ($mapSectionExists) $this->addError("  - 'Vị trí trên bản đồ' text found");
            if ($mapContainerExists) $this->addError("  - 'property-map card' class found");
            if ($mapDivExists) $this->addError("  - 'id=\"map\"' element found");
        }
        
        $this->log("🗺️ Map section removal test completed");
    }
    
    /**
     * Test similar properties structure
     */
    private function testSimilarPropertiesStructure() {
        $this->log("🏘️ Testing similar properties structure...");
        
        $propertyDetailFile = __DIR__ . '/app/views/property-detail.php';
        
        if (!file_exists($propertyDetailFile)) {
            $this->addError("Property detail file not found");
            return;
        }
        
        $content = file_get_contents($propertyDetailFile);
        
        // Check for new structure elements
        $hasPropertiesGrid = strpos($content, 'properties-grid') !== false;
        $hasPropertyCard = strpos($content, 'property-card') !== false;
        $hasPropertyImageContainer = strpos($content, 'property-image-container') !== false;
        $hasPropertyContent = strpos($content, 'property-content') !== false;
        $hasPropertyHeader = strpos($content, 'property-header') !== false;
        $hasPropertyFooter = strpos($content, 'property-footer') !== false;
        $hasWishlistBtn = strpos($content, 'wishlist-btn') !== false;
        $hasPropertyBadges = strpos($content, 'property-badges') !== false;
        
        // Check that old structure is removed
        $hasOldCardStructure = strpos($content, 'col-lg-3 col-md-6') !== false;
        $hasOldCardClass = strpos($content, 'class="card h-100"') !== false;
        $hasOldCardImgWrapper = strpos($content, 'card-img-wrapper') !== false;
        $hasOldCardBody = strpos($content, 'card-body') !== false;
        $hasOldCardTitle = strpos($content, 'card-title') !== false;
        
        // Evaluate results
        $newStructureScore = 0;
        $newStructureTotal = 8;
        
        if ($hasPropertiesGrid) $newStructureScore++;
        if ($hasPropertyCard) $newStructureScore++;
        if ($hasPropertyImageContainer) $newStructureScore++;
        if ($hasPropertyContent) $newStructureScore++;
        if ($hasPropertyHeader) $newStructureScore++;
        if ($hasPropertyFooter) $newStructureScore++;
        if ($hasWishlistBtn) $newStructureScore++;
        if ($hasPropertyBadges) $newStructureScore++;
        
        if ($newStructureScore === $newStructureTotal) {
            $this->addResult("✅ New property card structure implemented");
        } else {
            $this->addError("❌ New property card structure incomplete ($newStructureScore/$newStructureTotal)");
        }
        
        // Check old structure removal
        if (!$hasOldCardStructure && !$hasOldCardClass) {
            $this->addResult("✅ Old card structure removed");
        } else {
            $this->addError("❌ Old card structure still exists");
        }
        
        $this->log("🏘️ Similar properties structure test completed");
    }
    
    /**
     * Test CSS updates
     */
    private function testCSSUpdates() {
        $this->log("🎨 Testing CSS updates...");
        
        $cssFile = __DIR__ . '/public/css/style.css';
        
        if (!file_exists($cssFile)) {
            $this->addError("CSS file not found: $cssFile");
            return;
        }
        
        $content = file_get_contents($cssFile);
        
        // Check for new CSS structure
        $hasPropertiesGridCSS = strpos($content, '.similar-properties .properties-grid') !== false;
        $hasGridTemplateColumns = strpos($content, 'grid-template-columns') !== false;
        $hasResponsiveGrid = strpos($content, 'repeat(auto-fill, minmax(280px, 1fr))') !== false;
        
        // Check that old CSS is removed
        $hasOldCardCSS = strpos($content, '.similar-properties .card {') !== false;
        $hasOldCardImgWrapper = strpos($content, '.similar-properties .card-img-wrapper') !== false;
        $hasOldCardTitle = strpos($content, '.similar-properties .card-title') !== false;
        
        if ($hasPropertiesGridCSS && $hasGridTemplateColumns && $hasResponsiveGrid) {
            $this->addResult("✅ New CSS grid structure implemented");
        } else {
            $this->addError("❌ New CSS grid structure incomplete");
        }
        
        if (!$hasOldCardCSS) {
            $this->addResult("✅ Old card CSS removed");
        } else {
            $this->addError("❌ Old card CSS still exists");
        }
        
        $this->log("🎨 CSS updates test completed");
    }
    
    /**
     * Test HTML structure consistency with search page
     */
    private function testHTMLStructureConsistency() {
        $this->log("🔍 Testing HTML structure consistency...");
        
        $propertyDetailFile = __DIR__ . '/app/views/property-detail.php';
        $searchFile = __DIR__ . '/app/views/search.php';
        
        if (!file_exists($propertyDetailFile) || !file_exists($searchFile)) {
            $this->addError("Required files not found for consistency check");
            return;
        }
        
        $propertyDetailContent = file_get_contents($propertyDetailFile);
        $searchContent = file_get_contents($searchFile);
        
        // Check for consistent class names
        $consistentClasses = [
            'property-card',
            'property-image-container',
            'property-image',
            'property-badges',
            'property-badge featured-badge',
            'wishlist-btn',
            'property-content',
            'property-header',
            'property-title',
            'property-location',
            'property-features',
            'feature-item',
            'property-footer',
            'property-price',
            'current-price'
        ];
        
        $consistencyScore = 0;
        $totalClasses = count($consistentClasses);
        
        foreach ($consistentClasses as $className) {
            $inPropertyDetail = strpos($propertyDetailContent, $className) !== false;
            $inSearch = strpos($searchContent, $className) !== false;
            
            if ($inPropertyDetail && $inSearch) {
                $consistencyScore++;
            } else {
                $this->addError("❌ Inconsistent class: $className");
            }
        }
        
        if ($consistencyScore === $totalClasses) {
            $this->addResult("✅ HTML structure consistent with search page");
        } else {
            $this->addError("❌ HTML structure inconsistency ($consistencyScore/$totalClasses classes match)");
        }
        
        $this->log("🔍 HTML structure consistency test completed");
    }
    
    /**
     * Add test result
     */
    private function addResult($message) {
        $this->results[] = $message;
        $this->log($message);
    }
    
    /**
     * Add error
     */
    private function addError($message) {
        $this->errors[] = $message;
        $this->log($message, 'ERROR');
    }
    
    /**
     * Log message
     */
    private function log($message, $level = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        echo "[$timestamp] [$level] $message\n";
    }
    
    /**
     * Generate test report
     */
    private function generateReport() {
        $this->log("📊 Generating test report...");
        
        $totalTests = count($this->results) + count($this->errors);
        $passedTests = count($this->results);
        $failedTests = count($this->errors);
        $successRate = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 2) : 0;
        
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "🏠 PROPERTY DETAIL CHANGES TEST REPORT\n";
        echo str_repeat("=", 60) . "\n";
        
        echo "📈 SUMMARY:\n";
        echo "  Total Tests: $totalTests\n";
        echo "  Passed: $passedTests\n";
        echo "  Failed: $failedTests\n";
        echo "  Success Rate: $successRate%\n\n";
        
        if (!empty($this->results)) {
            echo "✅ PASSED TESTS:\n";
            foreach ($this->results as $result) {
                echo "  $result\n";
            }
            echo "\n";
        }
        
        if (!empty($this->errors)) {
            echo "❌ FAILED TESTS:\n";
            foreach ($this->errors as $error) {
                echo "  $error\n";
            }
            echo "\n";
        }
        
        echo "🎯 RECOMMENDATIONS:\n";
        if ($failedTests == 0) {
            echo "  🎉 All tests passed! Property detail changes are ready.\n";
            echo "  📝 Next steps:\n";
            echo "    1. Test on live environment\n";
            echo "    2. Check visual consistency\n";
            echo "    3. Verify responsive design\n";
        } else {
            echo "  🔧 Fix the failed tests before deployment\n";
            echo "  📋 Review the changes in property-detail.php\n";
            echo "  🎨 Check CSS updates in style.css\n";
        }
        
        echo "\n" . str_repeat("=", 60) . "\n";
        
        // Save report to file
        $reportFile = __DIR__ . '/property_detail_test_report_' . date('Y-m-d_H-i-s') . '.txt';
        $reportContent = "Property Detail Changes Test Report\n";
        $reportContent .= "Total Tests: " . $totalTests . "\n";
        $reportContent .= "Passed: " . $passedTests . "\n";
        $reportContent .= "Failed: " . $failedTests . "\n";
        $reportContent .= "Success Rate: " . $successRate . "%\n";
        file_put_contents($reportFile, $reportContent);
        
        $this->log("📄 Report saved to: $reportFile");
    }
}

// Run tests if called directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    echo "🏠 Property Detail Changes Tester\n";
    echo "=================================\n\n";
    
    $tester = new PropertyDetailTester();
    $tester->runAllTests();
    
    echo "\n✨ Testing completed!\n";
    echo "📖 Check the changes in app/views/property-detail.php\n";
    echo "🎨 Check CSS updates in public/css/style.css\n";
}
?>
