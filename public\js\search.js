// JavaScript for the search page
function initializeSearchPage() {
    console.log('Initializing search page...');

    // Khởi tạo Select2
    if ($.fn.select2) {
        $('.select2-ward').select2({
            placeholder: "Chọn phường/xã",
            allowClear: true,
            width: '100%',
            language: {
                noResults: function() {
                    return "Không tìm thấy kết quả";
                }
            }
        });
    }

    // Khởi tạo các dropdown Bootstrap
    var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });

    // Thêm hiệu ứng hover cho các liên kết
    $('.list-group-item a').hover(
        function() {
            $(this).addClass('text-primary');
        },
        function() {
            $(this).removeClass('text-primary');
        }
    );

    // Ngăn dropdown đóng khi click vào nội dung bên trong
    $('.more-filters-dropdown').on('click', function(e) {
        e.stopPropagation();
    });

    // DISABLED: Traditional search handlers - now using AJAX Search system
    // All search functionality is handled by ajax-search.js

    // Note: Keeping this file for other search pages (home, etc.)
    // but disabling search page handlers to avoid conflicts
}

// Hàm xử lý tìm kiếm với URL thân thiện
function handleSearch(formType) {
    let type, ward, price, bedrooms, bathrooms, direction, area, keyword;

    // Lấy giá trị từ form tương ứng
    if (formType === 'desktop') {
        type = $('#desktopType').val();
        ward = $('#desktopWard').val();
        price = $('#desktopPrice').val();
        keyword = $('#desktopSearchForm .search-address').val();
    } else if (formType === 'mobile') {
        type = $('#mobileType').val();
        ward = $('#mobileWard').val();
        price = $('#mobilePrice').val();
        keyword = $('#mobileKeyword').val();
    } else if (formType === 'search') {
        type = $('#searchType').val();
        ward = $('#searchWard').val();
        price = $('#searchPrice').val();
        bedrooms = $('#searchBedrooms').val();
        bathrooms = $('#searchBathrooms').val();
        direction = $('#searchDirection').val();
        area = $('#searchArea').val();
        keyword = $('#searchKeyword').val();
    }

    // Xây dựng URL thân thiện
    let url = '/thuenhadanang';
    let queryParams = {};

    // Thêm keyword vào query params nếu có
    if (keyword && keyword.trim() !== '') {
        queryParams.keyword = keyword.trim();
    }

    // Trường hợp chỉ có type
    if (type && !ward && !price && !bedrooms && !area) {
        url += '/cho-thue-' + type;
    }
    // Trường hợp chỉ có ward
    else if (!type && ward && !price && !bedrooms && !area) {
        url += '/cho-thue-nha-dat-tai-' + ward;
    }
    // Trường hợp chỉ có price
    else if (!type && !ward && price && !bedrooms && !area) {
        url += '/cho-thue-nha-dat/gia-' + formatPriceForUrl(price);
    }
    // Trường hợp chỉ có type và ward
    else if (type && ward && !price && !bedrooms && !area) {
        url += '/cho-thue-' + type + '-tai-' + ward;
    }
    // Trường hợp chỉ có type và price
    else if (type && !ward && price && !bedrooms && !area) {
        url += '/cho-thue-' + type + '/gia-' + formatPriceForUrl(price);
    }
    // Trường hợp chỉ có ward và price
    else if (!type && ward && price && !bedrooms && !area) {
        url += '/cho-thue-nha-dat-tai-' + ward + '/gia-' + formatPriceForUrl(price);
    }
    // Trường hợp có nhiều tham số
    else {
        // Phần đầu URL
        if (type) {
            if (ward) {
                // Sử dụng định dạng URL mới: cho-thue-{type}-tai-{ward}
                url += '/cho-thue-' + type + '-tai-' + ward;
            } else {
                url += '/cho-thue-' + type;
            }
        } else {
            if (ward) {
                url += '/cho-thue-nha-dat-tai-' + ward;
            } else {
                url += '/cho-thue-nha-dat';
            }
        }

        // Tạo phần filter
        let filterParts = [];

        // Thêm price nếu có
        if (price) {
            filterParts.push('gia-' + formatPriceForUrl(price));
        }

        // Thêm area nếu có
        if (area) {
            filterParts.push('dt-' + formatAreaForUrl(area));
        }

        // Thêm bedrooms nếu có
        if (bedrooms) {
            if (bedrooms === '4+') {
                filterParts.push('4pn-tro-len');
            } else {
                filterParts.push(bedrooms + 'pn');
            }
        }

        // Thêm phần filter vào URL nếu có
        if (filterParts.length > 0) {
            url += '/' + filterParts.join('-');
        }
    }

    // Thêm bathrooms vào query params nếu có
    if (bathrooms) {
        queryParams.bathrooms = bathrooms;
    }

    // Thêm direction vào query params nếu có
    if (direction) {
        queryParams.direction = direction;
    }

    // Thêm query params nếu có
    if (Object.keys(queryParams).length > 0) {
        url += '?' + $.param(queryParams);
    }

    // Chuyển hướng đến URL mới
    window.location.href = url;
}

// Hàm định dạng giá cho URL
function formatPriceForUrl(price) {
    switch (price) {
        case '1-3':
            return '1-3-trieu';
        case '3-5':
            return '3-5-trieu';
        case '5-7':
            return '5-7-trieu';
        case '7-10':
            return '7-10-trieu';
        case '10-15':
            return '10-15-trieu';
        case '15+':
            return 'tren-15-trieu';
        default:
            return price;
    }
}

// Hàm định dạng diện tích cho URL
function formatAreaForUrl(area) {
    switch (area) {
        case '0-20':
            return '0-20m2';
        case '20-30':
            return '20-30m2';
        case '30-50':
            return '30-50m2';
        case '50-70':
            return '50-70m2';
        case '70-90':
            return '70-90m2';
        case '90+':
            return 'tren-90m2';
        default:
            return area + 'm2';
    }
}

// Khởi tạo trang khi document ready
$(document).ready(function() {
    initializeSearchPage();
});
