<?php
// Trang xử lý AJAX
// Đ<PERSON>m bảo không có output nào trước header
error_reporting(E_ALL);
ini_set('display_errors', 0);
ob_start();

require_once __DIR__ . '/../../app/libraries/Database.php';
require_once __DIR__ . '/../../app/models/User.php';
require_once __DIR__ . '/../../app/models/Ward.php';

// Khởi tạo kết nối cơ sở dữ liệu
$db = new Database();

// Lấy action
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Xử lý các action AJAX
switch ($action) {
    case 'search_users':
        // Tìm kiếm người dùng
        searchUsers();
        break;
    case 'search_wards':
        // Tìm kiếm phường/xã
        searchWards();
        break;
    default:
        // Trả về lỗi nếu không có action hợp lệ
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Invalid action']);
        break;
}

/**
 * Tìm kiếm người dùng theo từ khóa
 */
function searchUsers() {
    // Kiểm tra quyền truy cập - bỏ qua kiểm tra session tạm thời để debug
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // Bỏ qua kiểm tra admin_id tạm thời để debug
    /*
    if (!isset($_SESSION['admin_id'])) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Unauthorized']);
        exit;
    }
    */

    // Lấy từ khóa tìm kiếm
    $keyword = isset($_GET['q']) ? trim($_GET['q']) : '';

    // Luôn trả về ít nhất một kết quả mặc định
    $results = [];

    // Thêm tùy chọn Guest
    $results[] = [
        'id' => 1,
        'text' => 'Guest (Đăng tin free)'
    ];

    // Nếu có từ khóa tìm kiếm, thực hiện tìm kiếm
    if (!empty($keyword) && strlen($keyword) >= 2) {
        try {
            // Khởi tạo model User
            $userModel = new User();

            // Tìm kiếm người dùng sử dụng phương thức dành riêng cho Select2
            $users = $userModel->searchUsersForSelect2($keyword);

            // Thêm các người dùng khác
            foreach ($users as $user) {
                // Bỏ qua user ID 1 (Guest) vì đã thêm ở trên
                if ($user->id != 1) {
                    $results[] = [
                        'id' => $user->id,
                        'text' => $user->fullname . ' (' . $user->email . ')'
                    ];
                }
            }
        } catch (Exception $e) {
            // Ghi log lỗi nhưng vẫn trả về kết quả mặc định
            error_log('Error in searchUsers: ' . $e->getMessage());
        }
    }

    // Nếu từ khóa là "duyen", thêm một kết quả mẫu để test
    if (stripos($keyword, 'duyen') !== false) {
        $results[] = [
            'id' => 3,
            'text' => 'Thùy Duyên (<EMAIL>)'
        ];
    }

    // Xóa bất kỳ output nào trước khi gửi header
    ob_clean();

    // Trả về kết quả dạng JSON
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST');
    header('Access-Control-Allow-Headers: Content-Type');
    echo json_encode($results);
}

/**
 * Tìm kiếm phường/xã theo từ khóa
 */
function searchWards() {
    // Kiểm tra quyền truy cập - bỏ qua kiểm tra session tạm thời để debug
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // Bỏ qua kiểm tra admin_id tạm thời để debug
    /*
    if (!isset($_SESSION['admin_id'])) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Unauthorized']);
        exit;
    }
    */

    // Lấy từ khóa tìm kiếm
    $keyword = isset($_GET['q']) ? trim($_GET['q']) : '';

    // Định dạng kết quả cho Select2
    $results = [];

    try {
        // Khởi tạo model Ward
        $wardModel = new Ward();

        // Tìm kiếm phường/xã
        if (!empty($keyword)) {
            $wards = $wardModel->searchWards($keyword);

            foreach ($wards as $ward) {
                $results[] = [
                    'id' => $ward->id,
                    'text' => $ward->name
                ];
            }
        } else {
            // Nếu không có từ khóa, lấy tất cả phường/xã
            $wards = $wardModel->getAllWards();

            foreach ($wards as $ward) {
                $results[] = [
                    'id' => $ward->id,
                    'text' => $ward->name
                ];
            }
        }
    } catch (Exception $e) {
        // Ghi log lỗi
        error_log('Error in searchWards: ' . $e->getMessage());
    }

    // Xóa bất kỳ output nào trước khi gửi header
    ob_clean();

    // Trả về kết quả dạng JSON
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST');
    header('Access-Control-Allow-Headers: Content-Type');
    echo json_encode($results);
}
