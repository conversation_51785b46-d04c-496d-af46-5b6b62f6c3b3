<?php
class BaseController {
    protected function view($view, $data = []) {
        // Extract data to make it available in view
        extract($data);
        
        // Start output buffering
        ob_start();
        
        // Include the view file
        $viewFile = VIEW_PATH . '/' . $view . '.php';
        if (file_exists($viewFile)) {
            require_once $viewFile;
        } else {
            throw new Exception("View not found: {$view}");
        }
        
        // Get the view content
        $content = ob_get_clean();
        
        // Include the layout
        require_once LAYOUT_PATH . '.php';
    }

    protected function model($model) {
        require_once APP_PATH . '/models/' . $model . '.php';
        return new $model();
    }
} 