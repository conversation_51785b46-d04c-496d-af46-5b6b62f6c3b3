<?php
// Ki<PERSON>m tra quyền truy cập
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Khởi tạo model
require_once '../app/models/Ward.php';
$wardModel = new Ward();

// Khởi tạo biến thông báo
$message = '';
$messageType = '';

// Xử lý các action
$action = isset($_GET['action']) ? $_GET['action'] : '';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Khởi tạo biến ward cho form edit
$editWard = null;

// Xử lý thêm phường/xã mới
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_ward'])) {
    // Validate dữ liệu
    $errors = [];

    if (empty($_POST['name'])) {
        $errors[] = 'Tên phường/xã không được để trống';
    }

    // Nếu không có lỗi, tiến hành thêm mới
    if (empty($errors)) {
        // Xử lý slug
        $slug = trim($_POST['slug']);

        // Nếu slug rỗng, tạo slug từ tên
        if (empty($slug)) {
            $slug = $wardModel->createSlug($_POST['name']);
        } else {
            // Kiểm tra xem slug đã tồn tại chưa
            if ($wardModel->isSlugExists($slug)) {
                $errors[] = 'Slug đã tồn tại, vui lòng chọn slug khác';
                $message = implode('<br>', $errors);
                $messageType = 'danger';
                // Giữ action là 'add' để hiển thị lại form
                $action = 'add';
                // Hiển thị form với thông báo lỗi
                goto display_form;
            }
        }

        // Chuẩn bị dữ liệu
        $wardData = [
            'name' => trim($_POST['name']),
            'slug' => $slug,
            'status' => isset($_POST['status']) ? 1 : 0
        ];

        // Thêm phường/xã mới
        if ($wardModel->create($wardData)) {
            $message = 'Thêm phường/xã mới thành công!';
            $messageType = 'success';

            // Đặt action về rỗng để hiển thị danh sách
            $action = '';
        } else {
            $message = 'Có lỗi xảy ra! Không thể thêm phường/xã mới.';
            $messageType = 'danger';
        }
    } else {
        // Hiển thị lỗi
        $message = implode('<br>', $errors);
        $messageType = 'danger';
    }
}

// Xử lý cập nhật phường/xã
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_ward'])) {
    // Validate dữ liệu
    $errors = [];

    if (empty($_POST['name'])) {
        $errors[] = 'Tên phường/xã không được để trống';
    }

    // Nếu không có lỗi, tiến hành cập nhật
    if (empty($errors)) {
        $wardId = intval($_POST['ward_id']);
        $currentWard = $wardModel->getWardById($wardId);

        // Xử lý slug
        $slug = trim($_POST['slug']);

        // Nếu slug rỗng, tạo slug từ tên
        if (empty($slug)) {
            $slug = $wardModel->createSlug($_POST['name']);
        }
        // Nếu slug đã thay đổi, kiểm tra xem slug mới đã tồn tại chưa
        else if ($slug != $currentWard->slug) {
            // Kiểm tra xem slug đã tồn tại chưa
            if ($wardModel->isSlugExists($slug, $wardId)) {
                $errors[] = 'Slug đã tồn tại, vui lòng chọn slug khác';
                $editWard = $currentWard;
                $message = implode('<br>', $errors);
                $messageType = 'danger';
                // Giữ action là 'edit' để hiển thị lại form
                $action = 'edit';
                // Hiển thị form với thông báo lỗi
                goto display_form;
            }
        }

        // Chuẩn bị dữ liệu
        $wardData = [
            'id' => $wardId,
            'name' => trim($_POST['name']),
            'slug' => $slug,
            'status' => isset($_POST['status']) ? 1 : 0
        ];

        // Cập nhật phường/xã
        if ($wardModel->update($wardData)) {
            $message = 'Cập nhật phường/xã thành công!';
            $messageType = 'success';

            // Đặt action về rỗng để hiển thị danh sách
            $action = '';
        } else {
            $message = 'Có lỗi xảy ra! Không thể cập nhật phường/xã.';
            $messageType = 'danger';
        }
    } else {
        // Hiển thị lỗi
        $message = implode('<br>', $errors);
        $messageType = 'danger';
    }
}

// Xử lý xóa phường/xã
if ($action == 'delete' && $id > 0) {
    if ($wardModel->delete($id)) {
        $message = 'Xóa phường/xã thành công!';
        $messageType = 'success';
    } else {
        $message = 'Có lỗi xảy ra! Không thể xóa phường/xã.';
        $messageType = 'danger';
    }

    // Đặt action về rỗng để hiển thị danh sách
    $action = '';
}

// Lấy thông tin phường/xã cần chỉnh sửa
if ($action == 'edit' && $id > 0) {
    $editWard = $wardModel->getWardById($id);
    if (!$editWard) {
        $message = 'Không tìm thấy phường/xã!';
        $messageType = 'danger';
    }
}

// Xử lý hiển thị form thêm mới
if ($action == 'add') {
    // Không cần làm gì đặc biệt, chỉ cần hiển thị form
}

// Xử lý tìm kiếm
$searchTerm = '';
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $searchTerm = trim($_GET['search']);
    $wards = $wardModel->searchWards($searchTerm);
} else {
    // Lấy tất cả phường/xã nếu không có tìm kiếm
    $wards = $wardModel->getAllWards();
}

// Label cho goto
display_form:
?>

<!-- Hiển thị thông báo nếu có -->
<?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">Quản lý phường/xã</h1>
    <?php if ($action == ''): ?>
        <a href="index.php?page=wards&action=add" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i> Thêm mới
        </a>
    <?php endif; ?>
</div>

<?php if ($action == 'add' || $action == 'edit'): ?>
    <!-- Form thêm/sửa phường/xã -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-<?php echo $action == 'add' ? 'plus-circle' : 'pencil-square'; ?> me-1"></i>
            <?php echo $action == 'add' ? 'Thêm phường/xã mới' : 'Chỉnh sửa phường/xã'; ?>
        </div>
        <div class="card-body">
            <form action="index.php?page=wards" method="POST">
                <?php if ($action == 'edit'): ?>
                    <input type="hidden" name="ward_id" value="<?php echo $editWard->id; ?>">
                <?php endif; ?>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="name" class="form-label">Tên phường/xã <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required
                               value="<?php echo $action == 'edit' ? htmlspecialchars($editWard->name) : ''; ?>">
                    </div>
                    <div class="col-md-6">
                        <label for="slug" class="form-label">Slug</label>
                        <input type="text" class="form-control" id="slug" name="slug"
                               value="<?php echo $action == 'edit' ? htmlspecialchars($editWard->slug) : ''; ?>"
                               placeholder="Để trống để tự động tạo từ tên">
                        <div class="form-text">Định dạng URL thân thiện, chỉ chứa chữ cái, số và dấu gạch ngang.</div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="status" class="form-label">Trạng thái</label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="status" name="status"
                               <?php echo ($action == 'edit' && $editWard->status == 1) || $action == 'add' ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="status">Kích hoạt</label>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" name="<?php echo $action == 'add' ? 'add_ward' : 'update_ward'; ?>" class="btn btn-success">
                        <i class="bi bi-save me-1"></i> <?php echo $action == 'add' ? 'Thêm mới' : 'Cập nhật'; ?>
                    </button>
                    <a href="index.php?page=wards" class="btn btn-secondary ms-2">
                        <i class="bi bi-x-circle me-1"></i> Hủy
                    </a>
                </div>
            </form>
        </div>
    </div>
<?php else: ?>
    <!-- Form tìm kiếm -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-search me-1"></i>
            Tìm kiếm phường/xã
        </div>
        <div class="card-body">
            <form action="index.php" method="GET" class="row g-3">
                <input type="hidden" name="page" value="wards">

                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" placeholder="Nhập tên phường/xã..." value="<?php echo htmlspecialchars($searchTerm); ?>">
                        <button class="btn btn-primary" type="submit">
                            <i class="bi bi-search me-1"></i> Tìm kiếm
                        </button>
                    </div>
                </div>

                <?php if (!empty($searchTerm)): ?>
                    <div class="col-md-12">
                        <a href="index.php?page=wards" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i> Xóa bộ lọc
                        </a>
                    </div>
                <?php endif; ?>
            </form>
        </div>
    </div>

    <!-- Bảng danh sách phường/xã -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-table me-1"></i>
            Danh sách phường/xã
        </div>
        <div class="card-body">
            <?php if (empty($wards)): ?>
                <div class="alert alert-info">
                    Không tìm thấy phường/xã nào.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th width="5%">ID</th>
                                <th width="30%">Tên phường/xã</th>
                                <th width="30%">Slug</th>
                                <th width="15%">Trạng thái</th>
                                <th width="20%">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($wards as $ward): ?>
                                <tr>
                                    <td><?php echo $ward->id; ?></td>
                                    <td><?php echo htmlspecialchars($ward->name); ?></td>
                                    <td><?php echo htmlspecialchars($ward->slug); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $ward->status == 1 ? 'success' : 'danger'; ?>">
                                            <?php echo $ward->status == 1 ? 'Kích hoạt' : 'Vô hiệu'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="index.php?page=wards&action=edit&id=<?php echo $ward->id; ?>" class="btn btn-sm btn-primary">
                                            <i class="bi bi-pencil-square"></i> Sửa
                                        </a>
                                        <a href="index.php?page=wards&action=delete&id=<?php echo $ward->id; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Bạn có chắc chắn muốn xóa phường/xã này?');">
                                            <i class="bi bi-trash"></i> Xóa
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<!-- JavaScript để tự động tạo slug từ tên -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');

    if (nameInput && slugInput) {
        nameInput.addEventListener('input', function() {
            // Chỉ tự động tạo slug nếu trường slug rỗng
            if (slugInput.value === '') {
                slugInput.value = createSlug(nameInput.value);
            }
        });
    }

    // Hàm tạo slug từ chuỗi
    function createSlug(str) {
        // Chuyển đổi tiếng Việt sang không dấu
        str = str.toLowerCase();
        str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
        str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
        str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
        str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
        str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
        str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
        str = str.replace(/đ/g, 'd');

        // Xóa ký tự đặc biệt
        str = str.replace(/[^a-z0-9\s-]/g, '');

        // Xóa khoảng trắng thay bằng dấu gạch ngang
        str = str.replace(/[\s]+/g, '-');

        return str;
    }
});
</script>