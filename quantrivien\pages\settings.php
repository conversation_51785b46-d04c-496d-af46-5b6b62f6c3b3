<?php
// Trang cài đặt
require_once '../app/libraries/Database.php';
require_once '../app/models/User.php';

// Khởi tạo đối tượng User
$userModel = new User();

// Lấy thông tin admin hiện tại
$adminId = $_SESSION['admin_id'];
$admin = $userModel->getUserById($adminId);

// Xử lý cập nhật thông tin
$message = '';
$messageType = '';

if($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Cập nhật thông tin cá nhân
    if(isset($_POST['update_profile'])) {
        $data = [
            'id' => $adminId,
            'fullname' => trim($_POST['fullname']),
            'email' => trim($_POST['email']),
            'phone' => trim($_POST['phone']),
            'zalo' => trim($_POST['zalo']),
            'address' => trim($_POST['address']),
            'role' => 'admin' // Đ<PERSON>m bảo giữ nguyên quyền admin
        ];
        
        // Upload avatar nếu có
        if(isset($_FILES['avatar']) && $_FILES['avatar']['error'] == 0) {
            $target_dir = "../public/uploads/users/";
            $file_extension = pathinfo($_FILES['avatar']['name'], PATHINFO_EXTENSION);
            $new_filename = 'admin_' . time() . '.' . $file_extension;
            $target_file = $target_dir . $new_filename;
            
            // Di chuyển file upload
            if(move_uploaded_file($_FILES['avatar']['tmp_name'], $target_file)) {
                $data['avatar'] = $new_filename;
            }
        }
        
        // Cập nhật thông tin
        if($userModel->updateUser($adminId, $data)) {
            $message = 'Cập nhật thông tin thành công!';
            $messageType = 'success';
            
            // Cập nhật session
            $_SESSION['admin_name'] = $data['fullname'];
            $_SESSION['admin_email'] = $data['email'];
            
            // Refresh lại thông tin admin
            $admin = $userModel->getUserById($adminId);
        } else {
            $message = 'Có lỗi xảy ra! Không thể cập nhật thông tin.';
            $messageType = 'danger';
        }
    }
    
    // Đổi mật khẩu
    if(isset($_POST['change_password'])) {
        $current_password = trim($_POST['current_password']);
        $new_password = trim($_POST['new_password']);
        $confirm_password = trim($_POST['confirm_password']);
        
        // Kiểm tra mật khẩu hiện tại
        if(!password_verify($current_password, $admin->password)) {
            $message = 'Mật khẩu hiện tại không đúng!';
            $messageType = 'danger';
        } 
        // Kiểm tra mật khẩu mới
        elseif($new_password != $confirm_password) {
            $message = 'Mật khẩu mới không khớp!';
            $messageType = 'danger';
        }
        // Kiểm tra độ dài mật khẩu
        elseif(strlen($new_password) < 6) {
            $message = 'Mật khẩu mới phải có ít nhất 6 ký tự!';
            $messageType = 'danger';
        }
        else {
            // Hash mật khẩu
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            
            // Cập nhật mật khẩu
            if($userModel->updatePassword($adminId, $hashed_password)) {
                $message = 'Đổi mật khẩu thành công!';
                $messageType = 'success';
            } else {
                $message = 'Có lỗi xảy ra! Không thể đổi mật khẩu.';
                $messageType = 'danger';
            }
        }
    }
}
?>

<div class="container-fluid px-4">
    <h1 class="mt-2 mb-4">Cài đặt</h1>
    
    <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="row">
        <!-- Thông tin cá nhân -->
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-person-fill me-1"></i>
                    Thông tin cá nhân
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-3 text-center">
                            <img src="<?php echo BASE_URL . '/public/uploads/users/' . $admin->avatar; ?>" 
                                 alt="Avatar" class="rounded-circle mb-3" width="120" height="120">
                            <div class="mb-3">
                                <label for="avatar" class="form-label">Ảnh đại diện</label>
                                <input type="file" class="form-control" id="avatar" name="avatar">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="fullname" class="form-label">Họ tên</label>
                            <input type="text" class="form-control" id="fullname" name="fullname" 
                                   value="<?php echo $admin->fullname; ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo $admin->email; ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">Số điện thoại</label>
                            <input type="text" class="form-control" id="phone" name="phone" 
                                   value="<?php echo $admin->phone; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="zalo" class="form-label">Zalo</label>
                            <input type="text" class="form-control" id="zalo" name="zalo" 
                                   value="<?php echo $admin->zalo; ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Địa chỉ</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?php echo $admin->address; ?></textarea>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" name="update_profile" class="btn btn-primary">Cập nhật thông tin</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Đổi mật khẩu -->
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-shield-lock-fill me-1"></i>
                    Đổi mật khẩu
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Mật khẩu hiện tại</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_password" class="form-label">Mật khẩu mới</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Xác nhận mật khẩu mới</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" name="change_password" class="btn btn-primary">Đổi mật khẩu</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div> 
