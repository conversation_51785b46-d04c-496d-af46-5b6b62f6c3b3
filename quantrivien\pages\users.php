<?php
// Trang quản lý người dùng
require_once '../app/libraries/Database.php';
require_once '../app/models/User.php';

// Khởi tạo đối tượng User
$userModel = new User();

// Xử lý action (nếu có)
$action = isset($_GET['action']) ? $_GET['action'] : '';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$message = '';
$messageType = '';

// Khởi tạo biến user cho form edit
$editUser = null;

// X<PERSON> lý thêm người dùng mới
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_user'])) {
    // Validate dữ liệu
    $errors = [];

    if (empty($_POST['fullname'])) {
        $errors[] = 'Họ tên không được để trống';
    }

    if (empty($_POST['email'])) {
        $errors[] = 'Email không được để trống';
    } elseif (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Email không hợp lệ';
    } elseif ($userModel->findUserByEmail($_POST['email'])) {
        $errors[] = 'Email đã tồn tại trong hệ thống';
    }

    if (empty($_POST['password'])) {
        $errors[] = 'Mật khẩu không được để trống';
    } elseif (strlen($_POST['password']) < 6) {
        $errors[] = 'Mật khẩu phải có ít nhất 6 ký tự';
    }

    // Nếu không có lỗi, tiến hành thêm mới
    if (empty($errors)) {
        // Chuẩn bị dữ liệu
        $userData = [
            'fullname' => trim($_POST['fullname']),
            'email' => trim($_POST['email']),
            'password' => password_hash(trim($_POST['password']), PASSWORD_DEFAULT),
            'phone' => !empty($_POST['phone']) ? trim($_POST['phone']) : null,
            'zalo' => !empty($_POST['zalo']) ? trim($_POST['zalo']) : null,
            'address' => !empty($_POST['address']) ? trim($_POST['address']) : null,
            'role' => 'user', // Mặc định là user
            'avatar' => 'default-avatar.jpg' // Mặc định avatar
        ];

        // Xử lý upload avatar nếu có
        if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] == 0) {
            // Xử lý dữ liệu base64 từ cropper
            if (!empty($_POST['avatar_data'])) {
                $avatarData = $_POST['avatar_data'];
                $avatarData = str_replace('data:image/jpeg;base64,', '', $avatarData);
                $avatarData = str_replace('data:image/png;base64,', '', $avatarData);
                $avatarData = str_replace(' ', '+', $avatarData);
                $avatarData = base64_decode($avatarData);

                if ($avatarData) {
                    $uploadDir = __DIR__ . '/../../public/uploads/users/';

                    // Tạo thư mục nếu chưa tồn tại
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0777, true);
                    }

                    // Tạo tên file mới với tiền tố avatar_ và số ngẫu nhiên
                    $fileName = 'avatar_' . time() . '_' . mt_rand(1000, 9999) . '.jpg';
                    $targetFile = $uploadDir . $fileName;

                    // Lưu file
                    if (file_put_contents($targetFile, $avatarData)) {
                        $userData['avatar'] = $fileName;
                    }
                }
            }
        }

        // Thêm người dùng mới
        if ($userModel->create($userData)) {
            $message = 'Thêm người dùng mới thành công!';
            $messageType = 'success';

            // Thay vì chuyển hướng, đặt action về rỗng để hiển thị danh sách
            $action = '';
        } else {
            $message = 'Có lỗi xảy ra! Không thể thêm người dùng mới.';
            $messageType = 'danger';
        }
    } else {
        // Hiển thị lỗi
        $message = implode('<br>', $errors);
        $messageType = 'danger';
    }
}

// Xử lý xóa người dùng
if ($action == 'delete' && $id > 0) {
    // Lấy thông tin user trước khi xóa để lấy tên file avatar
    $userToDelete = $userModel->getUserById($id);

    if ($userToDelete) {
        // Xóa file avatar nếu không phải avatar mặc định
        if (!empty($userToDelete->avatar) && $userToDelete->avatar != 'default-avatar.jpg') {
            $uploadDir = __DIR__ . '/../../public/uploads/users/';
            $avatarPath = $uploadDir . $userToDelete->avatar;

            if (file_exists($avatarPath)) {
                unlink($avatarPath);
                error_log('Deleted avatar of user ID ' . $id . ': ' . $userToDelete->avatar);
            }
        }

        // Xóa user trong database
        if ($userModel->delete($id)) {
            $message = 'Xóa người dùng thành công!';
            $messageType = 'success';
        } else {
            $message = 'Có lỗi xảy ra! Không thể xóa người dùng.';
            $messageType = 'danger';
        }
    } else {
        $message = 'Không tìm thấy người dùng cần xóa!';
        $messageType = 'danger';
    }
}

// Xử lý cập nhật avatar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_avatar'])) {
    $userId = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;

    if (empty($_POST['avatar_data'])) {
        $message = 'Vui lòng cắt ảnh trước khi lưu!';
        $messageType = 'danger';
    } else {
        // Lấy thông tin user hiện tại
        $currentUser = $userModel->getUserById($userId);

        if (!$currentUser) {
            $message = 'Không tìm thấy người dùng!';
            $messageType = 'danger';
        } else {
            // Xử lý dữ liệu base64
            $avatarData = $_POST['avatar_data'];
            $avatarData = str_replace('data:image/jpeg;base64,', '', $avatarData);
            $avatarData = str_replace('data:image/png;base64,', '', $avatarData);
            $avatarData = str_replace(' ', '+', $avatarData);
            $avatarData = base64_decode($avatarData);

            if (!$avatarData) {
                $message = 'Dữ liệu ảnh không hợp lệ!';
                $messageType = 'danger';
            } else {
                $uploadDir = __DIR__ . '/../../public/uploads/users/';

                // Tạo thư mục nếu chưa tồn tại
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                // Tạo tên file mới
                $fileName = 'avatar_' . time() . '_' . mt_rand(1000, 9999) . '.jpg';
                $targetFile = $uploadDir . $fileName;

                // Xóa avatar cũ nếu có và không phải avatar mặc định
                if (!empty($currentUser->avatar) && $currentUser->avatar != 'default-avatar.jpg') {
                    $oldAvatarPath = $uploadDir . $currentUser->avatar;
                    if (file_exists($oldAvatarPath)) {
                        unlink($oldAvatarPath);
                        error_log('Deleted old avatar: ' . $currentUser->avatar);
                    }
                }

                // Lưu file mới
                if (file_put_contents($targetFile, $avatarData)) {
                    // Cập nhật thông tin avatar trong database
                    $userData = [
                        'avatar' => $fileName
                    ];

                    if ($userModel->updateUser($userId, $userData)) {
                        $message = 'Cập nhật avatar thành công!';
                        $messageType = 'success';

                        // Cập nhật lại thông tin user
                        $editUser = $userModel->getUserById($userId);
                    } else {
                        $message = 'Có lỗi xảy ra khi cập nhật thông tin avatar!';
                        $messageType = 'danger';
                    }
                } else {
                    $message = 'Có lỗi xảy ra khi lưu file avatar!';
                    $messageType = 'danger';
                }
            }
        }
    }
}

// Xử lý cập nhật người dùng
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_user'])) {
    $userId = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;

    // Validate dữ liệu
    $errors = [];

    if (empty($_POST['fullname'])) {
        $errors[] = 'Họ tên không được để trống';
    }

    if (empty($_POST['email'])) {
        $errors[] = 'Email không được để trống';
    } elseif (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Email không hợp lệ';
    } else {
        // Kiểm tra email đã tồn tại chưa (trừ email hiện tại của user)
        $existingUser = $userModel->getUserByEmail($_POST['email']);
        if ($existingUser && $existingUser->id != $userId) {
            $errors[] = 'Email đã được sử dụng bởi tài khoản khác';
        }
    }

    // Nếu không có lỗi, tiến hành cập nhật
    if (empty($errors)) {
        // Kiểm tra xem user hiện tại có phải admin không
        $currentUser = $userModel->getUserById($userId);

        $userData = [
            'fullname' => $_POST['fullname'],
            'email' => $_POST['email'],
            'phone' => $_POST['phone'] ?? null,
            'zalo' => $_POST['zalo'] ?? null,
            'address' => $_POST['address'] ?? null,
            'role' => $currentUser->role // Giữ nguyên quyền hiện tại
        ];

        // Cập nhật mật khẩu nếu có
        if (!empty($_POST['password'])) {
            $hashedPassword = password_hash($_POST['password'], PASSWORD_DEFAULT);
            $userModel->updatePassword($userId, $hashedPassword);
        }

        // Cập nhật thông tin người dùng
        if ($userModel->updateUser($userId, $userData)) {
            // Thay vì chuyển hướng, đặt biến để hiển thị thông báo thành công
            $message = 'Cập nhật thông tin người dùng thành công!';
            $messageType = 'success';

            // Đặt action về rỗng để ẩn form edit
            $action = '';
            $editUser = null;
        } else {
            $message = 'Có lỗi xảy ra! Không thể cập nhật thông tin người dùng.';
            $messageType = 'danger';
        }
    } else {
        // Hiển thị lỗi
        $message = implode('<br>', $errors);
        $messageType = 'danger';

        // Lấy lại thông tin user để hiển thị form
        $editUser = $userModel->getUserById($userId);
    }
}

// Lấy thông tin người dùng cần chỉnh sửa
if ($action == 'edit' && $id > 0) {
    $editUser = $userModel->getUserById($id);
    if (!$editUser) {
        $message = 'Không tìm thấy người dùng!';
        $messageType = 'danger';
    }
}

// Xử lý hiển thị form thêm mới
if ($action == 'add') {
    // Không cần làm gì đặc biệt, chỉ cần hiển thị form
}

// Không cần xử lý thông báo từ redirect nữa vì đã thay đổi cách xử lý

// Xử lý tìm kiếm
$searchTerm = '';
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $searchTerm = trim($_GET['search']);
    $users = $userModel->searchUsers($searchTerm);
} else {
    // Lấy tất cả người dùng nếu không có tìm kiếm
    $users = $userModel->getAllUsers();
}
?>

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mt-2">Quản lý người dùng</h1>
        <a href="index.php?page=users&action=add" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i> Thêm mới
        </a>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Form tìm kiếm -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-search me-1"></i>
            Tìm kiếm người dùng
        </div>
        <div class="card-body">
            <form action="index.php" method="GET" class="row g-3">
                <input type="hidden" name="page" value="users">

                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" placeholder="Nhập email hoặc tên người dùng..." value="<?php echo htmlspecialchars($searchTerm); ?>">
                        <button class="btn btn-primary" type="submit">
                            <i class="bi bi-search me-1"></i> Tìm kiếm
                        </button>
                    </div>
                </div>

                <div class="col-md-6">
                    <?php if (!empty($searchTerm)): ?>
                        <a href="index.php?page=users" class="btn btn-secondary">
                            <i class="bi bi-x-circle me-1"></i> Xóa bộ lọc
                        </a>
                        <span class="ms-3">
                            Kết quả tìm kiếm cho: <strong><?php echo htmlspecialchars($searchTerm); ?></strong>
                            (<?php echo count($users); ?> kết quả)
                        </span>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <?php if ($action == 'add'): ?>
        <!-- Form thêm mới người dùng -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-person-plus-fill me-1"></i>
                Thêm người dùng mới
            </div>
            <div class="card-body">
                <form action="index.php?page=users" method="POST" enctype="multipart/form-data" id="addUserForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="fullname" class="form-label">Họ tên <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="fullname" name="fullname" required>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="password" class="form-label">Mật khẩu <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text">Mật khẩu phải có ít nhất 6 ký tự</div>
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label">Số điện thoại</label>
                            <input type="text" class="form-control" id="phone" name="phone">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="zalo" class="form-label">Zalo</label>
                            <input type="text" class="form-control" id="zalo" name="zalo">
                        </div>
                        <div class="col-md-6">
                            <label for="address" class="form-label">Địa chỉ</label>
                            <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="avatarInput" class="form-label">Ảnh đại diện</label>
                            <input type="file" class="form-control" id="avatarInput" name="avatar" accept="image/*">
                            <div class="form-text">Chỉ chấp nhận file ảnh (JPG, JPEG, PNG, GIF)</div>
                        </div>
                    </div>

                    <div id="cropContainer" style="display: none;" class="mb-3">
                        <h6>Cắt ảnh đại diện (bắt buộc)</h6>
                        <div class="img-container mb-2" style="max-width: 100%; max-height: 400px;">
                            <img id="cropperImage" src="" alt="Ảnh cần crop" style="max-width: 100%;">
                        </div>
                        <button type="button" id="cropButton" class="btn btn-primary">
                            <i class="bi bi-crop me-1"></i> Cắt ảnh
                        </button>
                    </div>

                    <div id="previewContainer" style="display: none;" class="mb-3 text-center">
                        <h6>Xem trước</h6>
                        <img id="avatarPreview" src="" alt="Avatar Preview" class="img-thumbnail rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                        <input type="hidden" name="avatar_data" id="avatarData">
                    </div>

                    <div class="mt-4">
                        <button type="submit" name="add_user" class="btn btn-success">
                            <i class="bi bi-person-plus-fill me-1"></i> Thêm người dùng
                        </button>
                        <a href="index.php?page=users" class="btn btn-secondary ms-2">
                            <i class="bi bi-x-circle me-1"></i> Hủy
                        </a>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($action == 'edit' && $editUser): ?>
        <!-- Form chỉnh sửa người dùng -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-pencil-square me-1"></i>
                Chỉnh sửa thông tin người dùng
            </div>
            <div class="card-body">
                <form action="index.php?page=users" method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="user_id" value="<?php echo $editUser->id; ?>">

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="fullname" class="form-label">Họ tên <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="fullname" name="fullname"
                                   value="<?php echo htmlspecialchars($editUser->fullname); ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?php echo htmlspecialchars($editUser->email); ?>" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="phone" class="form-label">Số điện thoại</label>
                            <input type="text" class="form-control" id="phone" name="phone"
                                   value="<?php echo htmlspecialchars($editUser->phone ?? ''); ?>">
                        </div>
                        <div class="col-md-6">
                            <label for="zalo" class="form-label">Zalo</label>
                            <input type="text" class="form-control" id="zalo" name="zalo"
                                   value="<?php echo htmlspecialchars($editUser->zalo ?? ''); ?>">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="password" class="form-label">Mật khẩu mới</label>
                            <input type="password" class="form-control" id="password" name="password"
                                   placeholder="Để trống nếu không thay đổi">
                            <div class="form-text">Chỉ nhập nếu muốn thay đổi mật khẩu</div>
                        </div>
                        <!-- Đã xóa phần chọn quyền -->
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="address" class="form-label">Địa chỉ</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($editUser->address ?? ''); ?></textarea>
                        </div>
                    </div>

                    <div class="mt-4">
                        <button type="submit" name="update_user" class="btn btn-primary">
                            <i class="bi bi-save me-1"></i> Lưu thay đổi
                        </button>
                        <a href="index.php?page=users" class="btn btn-secondary ms-2">
                            <i class="bi bi-x-circle me-1"></i> Hủy
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Card quản lý avatar -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-image me-1"></i>
                Quản lý ảnh đại diện
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <h5>Avatar hiện tại</h5>
                        <?php if (!empty($editUser->avatar)): ?>
                            <img id="currentAvatar" src="<?php echo BASE_URL . '/public/uploads/users/' . $editUser->avatar; ?>"
                                 alt="Current Avatar" class="img-thumbnail rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                        <?php else: ?>
                            <img id="currentAvatar" src="<?php echo BASE_URL . '/public/uploads/users/default-avatar.jpg'; ?>"
                                 alt="Current Avatar" class="img-thumbnail rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                        <?php endif; ?>
                    </div>
                    <div class="col-md-8">
                        <h5>Cập nhật avatar mới</h5>
                        <form id="avatarForm" action="index.php?page=users&action=update_avatar&id=<?php echo $editUser->id; ?>" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="user_id" value="<?php echo $editUser->id; ?>">
                            <div class="mb-3">
                                <label for="avatarInput" class="form-label">Chọn ảnh</label>
                                <input type="file" class="form-control" id="avatarInput" name="avatar" accept="image/*">
                                <div class="form-text">Chỉ chấp nhận file ảnh (JPG, JPEG, PNG, GIF)</div>
                            </div>

                            <div id="cropContainer" style="display: none;" class="mb-3">
                                <h6>Cắt ảnh đại diện (bắt buộc)</h6>
                                <div class="img-container mb-2" style="max-width: 100%; max-height: 400px;">
                                    <img id="cropperImage" src="" alt="Ảnh cần crop" style="max-width: 100%;">
                                </div>
                                <button type="button" id="cropButton" class="btn btn-primary">
                                    <i class="bi bi-crop me-1"></i> Cắt ảnh
                                </button>
                            </div>

                            <div id="previewContainer" style="display: none;" class="mb-3 text-center">
                                <h6>Xem trước</h6>
                                <img id="avatarPreview" src="" alt="Avatar Preview" class="img-thumbnail rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                                <input type="hidden" name="avatar_data" id="avatarData">
                            </div>

                            <button type="submit" id="saveAvatarBtn" name="update_avatar" class="btn btn-success" style="display: none;">
                                <i class="bi bi-save me-1"></i> Lưu avatar
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

<!-- Thêm CSS và JS cho Cropper.js -->
<?php if ($action == 'edit' && $editUser || $action == 'add'): ?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Các biến toàn cục
    let cropper;
    const avatarInput = document.getElementById('avatarInput');
    const cropperImage = document.getElementById('cropperImage');
    const cropContainer = document.getElementById('cropContainer');
    const previewContainer = document.getElementById('previewContainer');
    const avatarPreview = document.getElementById('avatarPreview');
    const cropButton = document.getElementById('cropButton');
    const avatarData = document.getElementById('avatarData');
    const saveAvatarBtn = document.getElementById('saveAvatarBtn');
    const addUserForm = document.getElementById('addUserForm');

    // Xử lý khi người dùng chọn file
    if (avatarInput) {
        avatarInput.addEventListener('change', function(e) {
            if (e.target.files.length) {
                // Lấy file đã chọn
                const file = e.target.files[0];

                // Kiểm tra loại file
                if (!file.type.match('image.*')) {
                    alert('Vui lòng chọn file hình ảnh');
                    return;
                }

                // Đọc file và hiển thị trong cropper
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Hiển thị ảnh trong cropper
                    cropperImage.src = e.target.result;

                    // Hiển thị container crop
                    cropContainer.style.display = 'block';

                    // Ẩn container preview và nút lưu (nếu có)
                    previewContainer.style.display = 'none';
                    if (saveAvatarBtn) {
                        saveAvatarBtn.style.display = 'none';
                    }

                    // Hủy cropper cũ nếu có
                    if (cropper) {
                        cropper.destroy();
                    }

                    // Khởi tạo cropper mới
                    cropper = new Cropper(cropperImage, {
                        aspectRatio: 1, // Tỷ lệ 1:1 cho avatar
                        viewMode: 1,    // Giới hạn khung nhìn trong canvas
                        dragMode: 'move',
                        guides: true,
                        center: true,
                        highlight: true,
                        cropBoxMovable: true,
                        cropBoxResizable: true,
                        toggleDragModeOnDblclick: false
                    });
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Xử lý khi người dùng nhấn nút Crop
    if (cropButton) {
        cropButton.addEventListener('click', function() {
            if (!cropper) return;

            // Lấy dữ liệu ảnh đã crop
            const canvas = cropper.getCroppedCanvas({
                width: 300,
                height: 300,
                minWidth: 100,
                minHeight: 100,
                maxWidth: 1000,
                maxHeight: 1000,
                fillColor: '#fff',
                imageSmoothingEnabled: true,
                imageSmoothingQuality: 'high',
            });

            // Chuyển đổi canvas thành dữ liệu base64
            const croppedImageData = canvas.toDataURL('image/jpeg', 0.8);

            // Hiển thị ảnh đã crop
            avatarPreview.src = croppedImageData;
            previewContainer.style.display = 'block';

            // Lưu dữ liệu ảnh vào input hidden
            avatarData.value = croppedImageData;

            // Hiển thị nút lưu nếu có
            if (saveAvatarBtn) {
                saveAvatarBtn.style.display = 'block';
            }
        });
    }

    // Xử lý form submit cho form avatar
    const avatarForm = document.getElementById('avatarForm');
    if (avatarForm) {
        avatarForm.addEventListener('submit', function(e) {
            // Kiểm tra xem đã crop ảnh chưa
            if (!avatarData.value) {
                e.preventDefault();
                alert('Vui lòng cắt ảnh trước khi lưu');
                return;
            }
        });
    }

    // Xử lý form submit cho form thêm mới user
    if (addUserForm) {
        addUserForm.addEventListener('submit', function(e) {
            // Nếu đã chọn file ảnh nhưng chưa crop
            if (avatarInput.files.length > 0 && !avatarData.value) {
                e.preventDefault();
                alert('Vui lòng cắt ảnh đại diện trước khi lưu');
                return;
            }
        });
    }
});
</script>
<?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-table me-1"></i>
            Danh sách người dùng
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Avatar</th>
                            <th>Họ tên</th>
                            <th>Email</th>
                            <th>Số điện thoại</th>
                            <th>Quyền</th>
                            <th>Ngày tạo</th>
                            <th>Hành động</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($users)): ?>
                            <tr>
                                <td colspan="8" class="text-center">
                                    <?php if (!empty($searchTerm)): ?>
                                        Không tìm thấy người dùng nào phù hợp với từ khóa "<?php echo htmlspecialchars($searchTerm); ?>"
                                    <?php else: ?>
                                        Không có dữ liệu người dùng
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?php echo $user->id; ?></td>
                                    <td>
                                        <img src="<?php echo BASE_URL . '/public/uploads/users/' . $user->avatar; ?>"
                                             alt="Avatar" class="rounded-circle" width="40" height="40">
                                    </td>
                                    <td><?php echo $user->fullname; ?></td>
                                    <td><?php echo $user->email; ?></td>
                                    <td><?php echo $user->phone ?? 'N/A'; ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $user->role == 'admin' ? 'danger' : 'primary'; ?>">
                                            <?php echo $user->role; ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('d/m/Y', strtotime($user->created_at)); ?></td>
                                    <td>
                                        <a href="index.php?page=users&action=edit&id=<?php echo $user->id; ?>" class="btn btn-sm btn-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <?php if ($user->role != 'admin'): ?>
                                            <a href="index.php?page=users&action=delete&id=<?php echo $user->id; ?>"
                                               class="btn btn-sm btn-danger"
                                               onclick="return confirm('Bạn có chắc chắn muốn xóa người dùng này?');">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

