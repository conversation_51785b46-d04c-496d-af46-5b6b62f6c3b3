<?php
// Bắt đầu output buffering để tránh lỗi "headers already sent"
ob_start();

// Trang quản lý bất động sản
require_once '../app/libraries/Database.php';
require_once '../app/models/Property.php';
require_once '../app/models/PropertyType.php';
require_once '../app/models/Ward.php';
require_once '../app/models/User.php';
require_once '../app/models/PropertyContact.php';

// Khởi tạo các đối tượng model
$propertyModel = new Property();
$propertyTypeModel = new PropertyType();
$wardModel = new Ward();
$userModel = new User();
$propertyContactModel = new PropertyContact();

// Xử lý action (nếu có)
$action = isset($_GET['action']) ? $_GET['action'] : '';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Xử lý gia hạn tin đăng
if ($action == 'extend' && $id > 0) {
    // Lấy thông tin bất động sản
    $property = $propertyModel->getPropertyById($id);

    if ($property) {
        // Gia hạn tin đăng thêm 14 ngày
        if ($propertyModel->extendProperty($id)) {
            $message = 'Gia hạn tin đăng thành công! Tin đăng đã được gia hạn thêm 14 ngày.';
            $messageType = 'success';
        } else {
            $message = 'Có lỗi xảy ra! Không thể gia hạn tin đăng.';
            $messageType = 'danger';
        }
    } else {
        $message = 'Không tìm thấy bất động sản!';
        $messageType = 'danger';
    }

    // Chuyển hướng về trang danh sách
    header('Location: index.php?page=properties&message=' . urlencode($message) . '&message_type=' . $messageType);
    exit;
}

// Thư mục upload tạm thời
$tempUploadDir = __DIR__ . '/../../public/uploads/temp/';
// Thư mục upload chính thức
$uploadDir = __DIR__ . '/../../public/uploads/properties/';

// Tạo thư mục nếu chưa tồn tại
if (!file_exists($tempUploadDir)) {
    error_log('Temp directory does not exist: ' . $tempUploadDir);
    if (!mkdir($tempUploadDir, 0777, true)) {
        error_log('Failed to create temp directory: ' . $tempUploadDir . ' - ' . error_get_last()['message']);
    } else {
        // Đảm bảo quyền ghi
        chmod($tempUploadDir, 0777);
        error_log('Temp directory created: ' . $tempUploadDir);
    }
} else {
    error_log('Temp directory exists: ' . $tempUploadDir);
    // Kiểm tra quyền ghi
    if (!is_writable($tempUploadDir)) {
        error_log('Temp directory is not writable: ' . $tempUploadDir);
        chmod($tempUploadDir, 0777);
        error_log('Changed permissions for temp directory');
    }
}

if (!file_exists($uploadDir)) {
    error_log('Upload directory does not exist: ' . $uploadDir);
    if (!mkdir($uploadDir, 0777, true)) {
        error_log('Failed to create upload directory: ' . $uploadDir . ' - ' . error_get_last()['message']);
    } else {
        // Đảm bảo quyền ghi
        chmod($uploadDir, 0777);
        error_log('Upload directory created: ' . $uploadDir);
    }
} else {
    error_log('Upload directory exists: ' . $uploadDir);
    // Kiểm tra quyền ghi
    if (!is_writable($uploadDir)) {
        error_log('Upload directory is not writable: ' . $uploadDir);
        chmod($uploadDir, 0777);
        error_log('Changed permissions for upload directory');
    }
}
$message = '';
$messageType = '';

// Xử lý xóa bất động sản
if ($action == 'delete' && $id > 0) {
    // Lấy thông tin bất động sản
    $property = $propertyModel->getPropertyById($id);

    if ($property) {
        // Ghi log bắt đầu quá trình xóa
        error_log('Starting deletion process for property ID: ' . $id);

        // Lấy danh sách hình ảnh
        $images = $propertyModel->getPropertyImages($id);
        $imageDeleteErrors = [];

        // Xóa các file hình ảnh
        foreach ($images as $image) {
            $imagePath = __DIR__ . '/../../public/uploads/properties/' . $image->image_path;
            if (file_exists($imagePath)) {
                try {
                    if (unlink($imagePath)) {
                        error_log('Successfully deleted image file: ' . $imagePath);
                    } else {
                        $errorMsg = error_get_last() ? error_get_last()['message'] : 'Unknown error';
                        error_log('Failed to delete image file: ' . $imagePath . ' - ' . $errorMsg);
                        $imageDeleteErrors[] = $image->image_path;
                    }
                } catch (Exception $e) {
                    error_log('Exception when deleting image file: ' . $imagePath . ' - ' . $e->getMessage());
                    $imageDeleteErrors[] = $image->image_path;
                }
            } else {
                error_log('Image file not found: ' . $imagePath);
            }
        }

        // Xóa thông tin liên hệ nếu là tin đăng free (user_id = 1)
        if ($property->user_id == 1) {
            try {
                if ($propertyContactModel->delete($id)) {
                    error_log('Successfully deleted contact information for property ID: ' . $id);
                } else {
                    error_log('Failed to delete contact information for property ID: ' . $id);
                }
            } catch (Exception $e) {
                error_log('Exception when deleting contact information: ' . $e->getMessage());
            }
        }

        // Xóa bất động sản
        if ($propertyModel->delete($id)) {
            $message = 'Xóa bất động sản thành công!';
            if (!empty($imageDeleteErrors)) {
                error_log('Some images could not be deleted: ' . implode(', ', $imageDeleteErrors));
                $message .= ' Tuy nhiên, một số hình ảnh không thể xóa hoàn toàn.';
            }
            $messageType = 'success';
            error_log('Successfully deleted property ID: ' . $id);
        } else {
            $message = 'Có lỗi xảy ra! Không thể xóa bất động sản.';
            $messageType = 'danger';
            error_log('Failed to delete property ID: ' . $id);
        }
    } else {
        $message = 'Không tìm thấy bất động sản!';
        $messageType = 'danger';
        error_log('Property not found for deletion. ID: ' . $id);
    }
}

// Xử lý upload file tạm thời
if ($action == 'upload_temp') {
    // Tắt output buffering để tránh lỗi
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Đặt header JSON
    header('Content-Type: application/json');

    try {
        // Kiểm tra quyền truy cập
        if (!isset($_SESSION['admin_id'])) {
            echo json_encode(['success' => false, 'error' => 'Unauthorized']);
            exit;
        }

        // Kiểm tra file upload
        if (!isset($_FILES['file']) || $_FILES['file']['error'] != 0) {
            $error = isset($_FILES['file']) ? 'Error code: ' . $_FILES['file']['error'] : 'No file uploaded';
            echo json_encode(['success' => false, 'error' => 'No file uploaded or upload error: ' . $error]);
            exit;
        }

        $file = $_FILES['file'];
        $fileName = $file['name'];
        $fileTmpName = $file['tmp_name'];
        $fileSize = $file['size'];
        $fileType = $file['type'];

        // Kiểm tra loại file
        if (!in_array($fileType, ['image/jpeg', 'image/jpg', 'image/png'])) {
            echo json_encode(['success' => false, 'error' => 'Invalid file type: ' . $fileType]);
            exit;
        }

        // Kiểm tra kích thước file (1MB = 1048576 bytes)
        if ($fileSize > 1048576) {
            echo json_encode(['success' => false, 'error' => 'File too large: ' . ($fileSize / 1048576) . 'MB']);
            exit;
        }

        // Kiểm tra kích thước hình ảnh
        $imageInfo = getimagesize($fileTmpName);
        if ($imageInfo[0] < 300 || $imageInfo[1] < 300) {
            echo json_encode(['success' => false, 'error' => 'Image dimensions too small: ' . $imageInfo[0] . 'x' . $imageInfo[1]]);
            exit;
        }

        // Tạo thư mục tạm nếu chưa tồn tại
        $tempUploadDir = __DIR__ . '/../../public/uploads/temp/';
        if (!file_exists($tempUploadDir)) {
            if (!mkdir($tempUploadDir, 0777, true)) {
                echo json_encode(['success' => false, 'error' => 'Failed to create temp directory: ' . error_get_last()['message']]);
                exit;
            }
        }

        // Kiểm tra quyền ghi vào thư mục
        if (!is_writable($tempUploadDir)) {
            echo json_encode(['success' => false, 'error' => 'Temp directory is not writable: ' . $tempUploadDir]);
            exit;
        }

        // Tạo tên file mới
        $newFileName = 'temp_' . time() . '_' . mt_rand(1000, 9999) . '.jpg';
        $targetFile = $tempUploadDir . $newFileName;

        // Upload file
        if (move_uploaded_file($fileTmpName, $targetFile)) {
            echo json_encode(['success' => true, 'filename' => $newFileName]);
        } else {
            $lastError = error_get_last();
            $errorMsg = $lastError ? $lastError['message'] : 'Unknown error';
            echo json_encode(['success' => false, 'error' => 'Failed to upload file: ' . $errorMsg]);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Exception: ' . $e->getMessage()]);
    }
    exit;
}

// Xử lý xóa file tạm thời
if ($action == 'remove_temp') {
    // Tắt output buffering để tránh lỗi
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Đặt header JSON
    header('Content-Type: application/json');

    try {
        // Kiểm tra quyền truy cập
        if (!isset($_SESSION['admin_id'])) {
            echo json_encode(['success' => false, 'error' => 'Unauthorized']);
            exit;
        }

        // Kiểm tra tên file
        if (!isset($_POST['filename'])) {
            echo json_encode(['success' => false, 'error' => 'No filename provided']);
            exit;
        }

        $filename = $_POST['filename'];

        // Kiểm tra tên file hợp lệ (chỉ cho phép xóa file trong thư mục temp)
        if (!preg_match('/^temp_\d+_\d+\.jpg$/', $filename)) {
            echo json_encode(['success' => false, 'error' => 'Invalid filename: ' . $filename]);
            exit;
        }

        // Đường dẫn đến thư mục tạm
        $tempUploadDir = __DIR__ . '/../../public/uploads/temp/';
        $filePath = $tempUploadDir . $filename;

        // Kiểm tra xem file có tồn tại không
        if (!file_exists($filePath)) {
            echo json_encode(['success' => true, 'message' => 'File not found, already deleted']);
            exit;
        }

        // Xóa file
        if (unlink($filePath)) {
            echo json_encode(['success' => true]);
        } else {
            $lastError = error_get_last();
            $errorMsg = $lastError ? $lastError['message'] : 'Unknown error';
            echo json_encode(['success' => false, 'error' => 'Failed to delete file: ' . $errorMsg]);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Exception: ' . $e->getMessage()]);
    }
    exit;
}

// Nếu action là add hoặc edit, hiển thị form tương ứng
if ($action == 'add' || $action == 'edit') {
    // Lấy danh sách loại hình bất động sản
    $propertyTypes = $propertyTypeModel->getAllPropertyTypes();

    // Lấy danh sách phường/xã
    $wards = $wardModel->getAllWards();

    // Lấy danh sách hướng
    $directions = $propertyModel->getAllDirections();

    // Lấy danh sách người dùng
    $users = $userModel->getAllUsers();

    // Nếu là edit, lấy thông tin bất động sản
    if ($action == 'edit' && $id > 0) {
        $editProperty = $propertyModel->getPropertyById($id);

        if (!$editProperty) {
            $message = 'Không tìm thấy bất động sản!';
            $messageType = 'danger';
            $action = ''; // Quay lại danh sách
        } else {
            // Lấy danh sách hình ảnh
            $propertyImages = $propertyModel->getPropertyImages($id);

            // Lấy thông tin liên hệ nếu là tin đăng free
            if ($editProperty->user_id == 1) {
                $contactInfo = $propertyContactModel->getContactByPropertyId($id);
            }
        }
    }

    // Xử lý form submit
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        if (isset($_POST['save_property'])) {
            // Validate dữ liệu
            $errors = [];

            if (empty($_POST['title'])) {
                $errors[] = 'Tiêu đề không được để trống';
            }

            if (empty($_POST['description'])) {
                $errors[] = 'Mô tả không được để trống';
            }

            if (empty($_POST['type_id'])) {
                $errors[] = 'Loại hình không được để trống';
            }

            if (empty($_POST['ward_id'])) {
                $errors[] = 'Phường / Xã không được để trống';
            }

            if (empty($_POST['street'])) {
                $errors[] = 'Đường phố không được để trống';
            }

            if (empty($_POST['address'])) {
                $errors[] = 'Địa chỉ không được để trống';
            }

            if (empty($_POST['price']) || !is_numeric($_POST['price'])) {
                $errors[] = 'Giá cho thuê không hợp lệ';
            }

            // Kiểm tra chủ sở hữu nếu không chọn đăng free
            if (!isset($_POST['is_guest']) && empty($_POST['user_id'])) {
                $errors[] = 'Chủ sở hữu không được để trống';
            }

            // Kiểm tra thông tin liên hệ nếu chọn đăng free
            if (isset($_POST['is_guest'])) {
                if (empty($_POST['contact_name'])) {
                    $errors[] = 'Tên liên hệ không được để trống';
                }

                if (empty($_POST['contact_phone'])) {
                    $errors[] = 'Số điện thoại liên hệ không được để trống';
                }
            }

            // Nếu không có lỗi, tiến hành lưu dữ liệu
            if (empty($errors)) {
                // Xử lý slug
                $slug = !empty($_POST['slug']) ? trim($_POST['slug']) : '';

                // Nếu slug rỗng, tạo slug từ tiêu đề
                if (empty($slug)) {
                    $slug = $propertyModel->createSlug($_POST['title']);
                } else {
                    // Kiểm tra xem slug đã tồn tại chưa
                    $propertyId = $action == 'edit' ? $id : 0;
                    if ($propertyModel->isSlugExists($slug, $propertyId)) {
                        $errors[] = 'Slug đã tồn tại, vui lòng chọn slug khác';
                        $message = implode('<br>', $errors);
                        $messageType = 'danger';
                        goto display_form;
                    }
                }

                // Chuẩn bị dữ liệu
                $propertyData = [
                    'title' => trim($_POST['title']),
                    'slug' => $slug,
                    'description' => trim($_POST['description']),
                    'type_id' => intval($_POST['type_id']),
                    'address' => trim($_POST['address']),
                    'ward_id' => !empty($_POST['ward_id']) ? intval($_POST['ward_id']) : null,
                    'street' => !empty($_POST['street']) ? trim($_POST['street']) : null,
                    'city' => !empty($_POST['city']) ? trim($_POST['city']) : 'Đà Nẵng',
                    'area' => !empty($_POST['area']) ? floatval($_POST['area']) : null,
                    'price' => floatval($_POST['price']),
                    'price_period' => $_POST['price_period'],
                    'bedrooms' => !empty($_POST['bedrooms']) ? intval($_POST['bedrooms']) : null,
                    'bathrooms' => !empty($_POST['bathrooms']) ? intval($_POST['bathrooms']) : null,
                    'direction' => !empty($_POST['direction']) ? $_POST['direction'] : null,
                    'video_url' => !empty($_POST['video_url']) ? trim($_POST['video_url']) : null,
                    'status' => $_POST['status'],
                    'active' => isset($_POST['active']) ? intval($_POST['active']) : 0,
                    'featured' => isset($_POST['featured']) ? 1 : 0,
                    'user_id' => isset($_POST['is_guest']) ? 1 : intval($_POST['user_id'])
                ];

                // Xử lý ngày đăng
                if (!empty($_POST['created_at'])) {
                    $propertyData['created_at'] = date('Y-m-d H:i:s', strtotime($_POST['created_at']));
                } else {
                    $propertyData['created_at'] = date('Y-m-d H:i:s');
                }

                // Xử lý ngày hết hạn
                if (!empty($_POST['expiration_date'])) {
                    $propertyData['expiration_date'] = date('Y-m-d H:i:s', strtotime($_POST['expiration_date']));
                } else {
                    // Tính ngày hết hạn là 14 ngày kể từ ngày đăng
                    $propertyData['expiration_date'] = date('Y-m-d H:i:s', strtotime($propertyData['created_at'] . ' +14 days'));
                }

                // Cập nhật thời gian updated_at
                $propertyData['updated_at'] = date('Y-m-d H:i:s');

                if ($action == 'add') {
                    // Xử lý hình ảnh đã upload qua Dropzone trước khi tạo bất động sản
                    $uploadSuccess = 0;
                    $uploadErrors = [];
                    $propertyImages = [];
                    $mainImage = null;

                    // Kiểm tra danh sách file đã upload
                    if (!empty($_POST['property_images_list'])) {
                        try {
                            $uploadedFiles = json_decode($_POST['property_images_list'], true);

                            if (is_array($uploadedFiles) && count($uploadedFiles) > 0) {
                                // Kiểm tra số lượng hình ảnh
                                if (count($uploadedFiles) > 10) {
                                    $uploadErrors[] = 'Chỉ được phép upload tối đa 10 hình ảnh!';
                                } else {
                                    // Tạo ID tạm thời cho property
                                    $tempId = time() . '_' . mt_rand(1000, 9999);

                                    // Xử lý từng file
                                    foreach ($uploadedFiles as $tempFileName) {
                                        // Kiểm tra tên file hợp lệ
                                        if (!preg_match('/^temp_\d+_\d+\.jpg$/', $tempFileName)) {
                                            $uploadErrors[] = 'Tên file không hợp lệ: ' . $tempFileName;
                                            continue;
                                        }

                                        $tempFilePath = $tempUploadDir . $tempFileName;

                                        // Kiểm tra file tồn tại
                                        if (!file_exists($tempFilePath)) {
                                            $uploadErrors[] = 'File không tồn tại: ' . $tempFileName;
                                            continue;
                                        }

                                        // Tạo tên file mới
                                        $newFileName = 'property_' . $tempId . '_' . time() . '_' . mt_rand(1000, 9999) . '.jpg';
                                        $targetFile = $uploadDir . $newFileName;

                                        // Di chuyển file từ thư mục tạm sang thư mục chính
                                        if (copy($tempFilePath, $targetFile)) {
                                            // Ghi log để debug
                                            error_log('File copied successfully from ' . $tempFilePath . ' to ' . $targetFile);

                                            // Xóa file tạm sau khi copy thành công
                                            if (@unlink($tempFilePath)) {
                                                error_log('Temp file deleted: ' . $tempFilePath);
                                            } else {
                                                error_log('Failed to delete temp file: ' . $tempFilePath);
                                            }

                                            // Thêm vào mảng hình ảnh
                                            $propertyImages[] = $newFileName;

                                            // Đặt ảnh chính nếu chưa có
                                            if ($uploadSuccess == 0) {
                                                $mainImage = $newFileName;
                                            }

                                            $uploadSuccess++;
                                        } else {
                                            $lastError = error_get_last();
                                            $errorMsg = $lastError ? $lastError['message'] : 'Unknown error';
                                            error_log('Failed to copy file: ' . $tempFilePath . ' to ' . $targetFile . ' - ' . $errorMsg);
                                            $uploadErrors[] = 'Không thể di chuyển file: ' . $tempFileName . ' - ' . $errorMsg;
                                        }
                                    }
                                }
                            }
                        } catch (Exception $e) {
                            $uploadErrors[] = 'Lỗi xử lý danh sách file: ' . $e->getMessage();
                        }
                    }

                    // Thêm thông tin hình ảnh vào dữ liệu bất động sản
                    if (!empty($propertyImages)) {
                        $propertyData['images'] = json_encode($propertyImages);
                        $propertyData['main_image'] = $mainImage;
                    }

                    // Thêm mới bất động sản
                    $propertyId = $propertyModel->create($propertyData);

                    if ($propertyId) {
                        // Nếu là tin đăng free, lưu thông tin liên hệ
                        if (isset($_POST['is_guest'])) {
                            $contactData = [
                                'property_id' => $propertyId,
                                'name' => trim($_POST['contact_name']),
                                'phone' => trim($_POST['contact_phone']),
                                'zalo' => !empty($_POST['contact_zalo']) ? trim($_POST['contact_zalo']) : null,
                                'email' => !empty($_POST['contact_email']) ? trim($_POST['contact_email']) : null
                            ];

                            $propertyContactModel->create($contactData);
                        }

                        $message = 'Thêm bất động sản mới thành công!';
                        if ($uploadSuccess > 0) {
                            $message .= ' Đã upload thành công ' . $uploadSuccess . ' hình ảnh.';
                        }
                        if (!empty($uploadErrors)) {
                            $message .= ' Có ' . count($uploadErrors) . ' lỗi khi upload hình ảnh: ' . implode(', ', $uploadErrors);
                        }

                        $messageType = 'success';

                        // Chuyển đến trang edit
                        header('Location: index.php?page=properties&action=edit&id=' . $propertyId . '&message=' . urlencode($message) . '&messageType=' . $messageType);
                        exit;
                    } else {
                        $message = 'Có lỗi xảy ra! Không thể thêm bất động sản mới.';
                        $messageType = 'danger';
                    }
                } else {
                    // Cập nhật bất động sản
                    $propertyData['id'] = $id;

                    // Lấy thông tin bất động sản hiện tại
                    $existingProperty = $propertyModel->getPropertyById($id);

                    // Giữ nguyên thông tin hình ảnh hiện tại
                    if ($existingProperty) {
                        // Lấy thông tin hình ảnh từ database
                        $db = new Database();
                        $db->query('SELECT images, main_image FROM properties WHERE id = :id');
                        $db->bind(':id', $id);
                        $propertyImages = $db->single();

                        if ($propertyImages) {
                            $propertyData['images'] = $propertyImages->images;
                            $propertyData['main_image'] = $propertyImages->main_image;
                        }
                    }

                    if ($propertyModel->update($propertyData)) {
                        // Nếu là tin đăng free, lưu hoặc cập nhật thông tin liên hệ
                        if (isset($_POST['is_guest'])) {
                            $contactData = [
                                'property_id' => $id,
                                'name' => trim($_POST['contact_name']),
                                'phone' => trim($_POST['contact_phone']),
                                'zalo' => !empty($_POST['contact_zalo']) ? trim($_POST['contact_zalo']) : null,
                                'email' => !empty($_POST['contact_email']) ? trim($_POST['contact_email']) : null
                            ];

                            $propertyContactModel->saveOrUpdate($contactData);
                        } else {
                            // Nếu không phải tin đăng free nhưng trước đó là tin đăng free, xóa thông tin liên hệ
                            if ($existingProperty && $existingProperty->user_id == 1 && $propertyData['user_id'] != 1) {
                                $propertyContactModel->delete($id);
                            }
                        }

                        $message = 'Cập nhật bất động sản thành công!';
                        $messageType = 'success';

                        // Refresh lại thông tin bất động sản
                        $editProperty = $propertyModel->getPropertyById($id);
                        $propertyImages = $propertyModel->getPropertyImages($id);

                        // Refresh lại thông tin liên hệ nếu là tin đăng free
                        if ($editProperty->user_id == 1) {
                            $contactInfo = $propertyContactModel->getContactByPropertyId($id);
                        }
                    } else {
                        $message = 'Có lỗi xảy ra! Không thể cập nhật bất động sản.';
                        $messageType = 'danger';
                    }
                }
            } else {
                // Hiển thị lỗi
                $message = implode('<br>', $errors);
                $messageType = 'danger';
            }
        }

        // Xử lý upload hình ảnh
        if (isset($_POST['upload_images']) && $action == 'edit') {
            // Kiểm tra xem có file được upload không
            if (!empty($_FILES['property_images']['name'][0])) {
                $uploadDir = __DIR__ . '/../../public/uploads/properties/';

                // Tạo thư mục nếu chưa tồn tại
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                // Lấy thông tin bất động sản hiện tại
                $db = new Database();
                $db->query('SELECT images, main_image FROM properties WHERE id = :id');
                $db->bind(':id', $id);
                $property = $db->single();

                // Khởi tạo mảng hình ảnh
                $images = [];
                if ($property && $property->images) {
                    $images = json_decode($property->images, true) ?? [];
                }

                // Lấy số lượng hình ảnh hiện có
                $imageCount = count($images);

                // Kiểm tra số lượng hình ảnh
                if ($imageCount + count($_FILES['property_images']['name']) > 10) {
                    $message = 'Chỉ được phép upload tối đa 10 hình ảnh!';
                    $messageType = 'danger';
                } else {
                    $uploadErrors = [];
                    $uploadSuccess = 0;
                    $mainImage = $property->main_image;

                    // Xử lý từng file
                    foreach ($_FILES['property_images']['name'] as $key => $name) {
                        if ($_FILES['property_images']['error'][$key] == 0) {
                            $tmpName = $_FILES['property_images']['tmp_name'][$key];
                            $fileSize = $_FILES['property_images']['size'][$key];
                            $fileType = $_FILES['property_images']['type'][$key];

                            // Kiểm tra loại file
                            if (!in_array($fileType, ['image/jpeg', 'image/jpg', 'image/png'])) {
                                $uploadErrors[] = 'File ' . $name . ' không phải là hình ảnh hợp lệ (chỉ chấp nhận JPG, JPEG, PNG)';
                                continue;
                            }

                            // Kiểm tra kích thước file (2MB = 2097152 bytes) - tăng giới hạn cho file đã nén
                            if ($fileSize > 2097152) {
                                $uploadErrors[] = 'File ' . $name . ' vượt quá kích thước cho phép (tối đa 2MB)';
                                continue;
                            }

                            // Ghi log thông tin file để debug
                            error_log('Processing file: ' . $name . ', Size: ' . $fileSize . ' bytes, Type: ' . $fileType);

                            // Kiểm tra kích thước hình ảnh
                            $imageInfo = getimagesize($tmpName);
                            if ($imageInfo[0] < 300 || $imageInfo[1] < 300) {
                                $uploadErrors[] = 'File ' . $name . ' có kích thước quá nhỏ (tối thiểu 300x300 pixels)';
                                continue;
                            }

                            // Tạo tên file mới
                            $newFileName = 'property_' . $id . '_' . time() . '_' . mt_rand(1000, 9999) . '.jpg';
                            $targetFile = $uploadDir . $newFileName;

                            // Upload file
                            if (move_uploaded_file($tmpName, $targetFile)) {
                                // Thêm vào mảng hình ảnh
                                $images[] = $newFileName;

                                // Đặt ảnh chính nếu chưa có
                                if (empty($mainImage)) {
                                    $mainImage = $newFileName;
                                }

                                $uploadSuccess++;
                                error_log('File uploaded successfully: ' . $newFileName);
                            } else {
                                $uploadErrors[] = 'Không thể upload file ' . $name;
                                error_log('Failed to upload file: ' . $name);
                            }
                        }
                    }

                    if ($uploadSuccess > 0) {
                        // Cập nhật thông tin hình ảnh trong database
                        $db = new Database();
                        $db->query('UPDATE properties SET images = :images, main_image = :main_image WHERE id = :id');
                        $db->bind(':id', $id);
                        $db->bind(':images', json_encode($images));
                        $db->bind(':main_image', $mainImage);

                        if ($db->execute()) {
                            $message = 'Đã upload thành công ' . $uploadSuccess . ' hình ảnh.';
                            $messageType = 'success';
                            error_log('Images updated in database for property ID: ' . $id);
                        } else {
                            $message = 'Đã upload hình ảnh nhưng không thể cập nhật thông tin trong cơ sở dữ liệu.';
                            $messageType = 'warning';
                            error_log('Failed to update images in database for property ID: ' . $id);
                        }

                        if (!empty($uploadErrors)) {
                            $message .= '<br>Có ' . count($uploadErrors) . ' lỗi: <br>' . implode('<br>', $uploadErrors);
                            $messageType = 'warning';
                        }

                        // Lưu thông báo vào session để hiển thị sau khi chuyển hướng
                        $_SESSION['message'] = $message;
                        $_SESSION['messageType'] = $messageType;

                        // Sử dụng JavaScript để chuyển hướng thay vì header()
                        echo '<script>window.location.href = "index.php?page=properties&action=edit&id=' . $id . '";</script>';
                        exit;
                    } else {
                        $message = 'Không thể upload hình ảnh: <br>' . implode('<br>', $uploadErrors);
                        $messageType = 'danger';
                    }
                }
            } else {
                $message = 'Vui lòng chọn hình ảnh để upload!';
                $messageType = 'warning';
            }
        }

        // Xử lý xóa hình ảnh
        if (isset($_POST['delete_image']) && $action == 'edit') {
            $imageId = $_POST['image_id']; // Không chuyển đổi thành int vì ID bây giờ là chuỗi (property_id_index)

            if ($propertyModel->deletePropertyImage($imageId)) {
                $message = 'Xóa hình ảnh thành công!';
                $messageType = 'success';
                error_log('Image deleted successfully: ' . $imageId);

                // Lưu thông báo vào session để hiển thị sau khi chuyển hướng
                $_SESSION['message'] = $message;
                $_SESSION['messageType'] = $messageType;

                // Sử dụng JavaScript để chuyển hướng thay vì header()
                echo '<script>window.location.href = "index.php?page=properties&action=edit&id=' . $id . '";</script>';
                exit;
            } else {
                $message = 'Có lỗi xảy ra! Không thể xóa hình ảnh.';
                $messageType = 'danger';
                error_log('Failed to delete image: ' . $imageId);

                // Lưu thông báo vào session để hiển thị sau khi chuyển hướng
                $_SESSION['message'] = $message;
                $_SESSION['messageType'] = $messageType;

                // Sử dụng JavaScript để chuyển hướng thay vì header()
                echo '<script>window.location.href = "index.php?page=properties&action=edit&id=' . $id . '";</script>';
                exit;
            }
        }

        // Xử lý đặt ảnh chính
        if (isset($_POST['set_main_image']) && $action == 'edit') {
            $imageId = $_POST['image_id']; // Không chuyển đổi thành int vì ID bây giờ là chuỗi (property_id_index)

            if ($propertyModel->setMainImage($imageId, $id)) {
                $message = 'Đặt ảnh chính thành công!';
                $messageType = 'success';
                error_log('Main image set successfully: ' . $imageId);

                // Lưu thông báo vào session để hiển thị sau khi chuyển hướng
                $_SESSION['message'] = $message;
                $_SESSION['messageType'] = $messageType;

                // Sử dụng JavaScript để chuyển hướng thay vì header()
                echo '<script>window.location.href = "index.php?page=properties&action=edit&id=' . $id . '";</script>';
                exit;
            } else {
                $message = 'Có lỗi xảy ra! Không thể đặt ảnh chính.';
                $messageType = 'danger';
                error_log('Failed to set main image: ' . $imageId);

                // Lưu thông báo vào session để hiển thị sau khi chuyển hướng
                $_SESSION['message'] = $message;
                $_SESSION['messageType'] = $messageType;

                // Sử dụng JavaScript để chuyển hướng thay vì header()
                echo '<script>window.location.href = "index.php?page=properties&action=edit&id=' . $id . '";</script>';
                exit;
            }
        }

        // Xử lý lưu thứ tự hình ảnh
        if (isset($_POST['save_image_order']) && $action == 'edit') {
            // Kiểm tra xem có phải là yêu cầu AJAX không
            $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

            // Ghi log để debug
            error_log('Processing save_image_order request. Is AJAX: ' . ($isAjax ? 'Yes' : 'No'));
            error_log('POST data: ' . print_r($_POST, true));

            $success = false;
            $message = '';

            if (!empty($_POST['image_order_data'])) {
                try {
                    $imageOrder = json_decode($_POST['image_order_data'], true);
                    error_log('Decoded image order: ' . print_r($imageOrder, true));

                    if (is_array($imageOrder) && count($imageOrder) > 0) {
                        // Lấy thông tin bất động sản hiện tại
                        $property = $propertyModel->getPropertyById($id);

                        if ($property) {
                            // Lấy danh sách hình ảnh hiện tại
                            $currentImages = $propertyModel->getPropertyImages($id);
                            error_log('Current images: ' . print_r($currentImages, true));

                            // Tạo mảng mới theo thứ tự đã sắp xếp
                            $newImagesOrder = [];
                            $newMainImage = null;

                            // Lặp qua thứ tự mới
                            foreach ($imageOrder as $imageId) {
                                // Tìm hình ảnh tương ứng
                                foreach ($currentImages as $image) {
                                    if ($image->id === $imageId) {
                                        $newImagesOrder[] = $image->image_path;

                                        // Đặt ảnh đầu tiên làm ảnh chính
                                        if ($newMainImage === null) {
                                            $newMainImage = $image->image_path;
                                        }

                                        break;
                                    }
                                }
                            }

                            error_log('New images order: ' . print_r($newImagesOrder, true));
                            error_log('New main image: ' . $newMainImage);

                            // Cập nhật thứ tự hình ảnh và ảnh chính
                            if (!empty($newImagesOrder)) {
                                // Cập nhật vào database
                                $db = new Database();
                                $db->query('UPDATE properties SET images = :images, main_image = :main_image WHERE id = :id');
                                $db->bind(':id', $id);
                                $db->bind(':images', json_encode($newImagesOrder));
                                $db->bind(':main_image', $newMainImage);

                                if ($db->execute()) {
                                    $success = true;
                                    $message = 'Cập nhật thứ tự hình ảnh thành công!';
                                    $messageType = 'success';
                                    error_log('Image order updated successfully for property ID: ' . $id);

                                    // Refresh lại danh sách hình ảnh
                                    $propertyImages = $propertyModel->getPropertyImages($id);
                                } else {
                                    $message = 'Có lỗi xảy ra! Không thể cập nhật thứ tự hình ảnh.';
                                    $messageType = 'danger';
                                    error_log('Failed to update image order for property ID: ' . $id . '. Database error: ' . print_r($db->errorInfo(), true));
                                }
                            } else {
                                $message = 'Không có hình ảnh nào để sắp xếp.';
                                $messageType = 'warning';
                                error_log('No images to sort');
                            }
                        } else {
                            $message = 'Không tìm thấy bất động sản!';
                            $messageType = 'danger';
                            error_log('Property not found. ID: ' . $id);
                        }
                    } else {
                        $message = 'Dữ liệu thứ tự hình ảnh không hợp lệ.';
                        $messageType = 'danger';
                        error_log('Invalid image order data');
                    }
                } catch (Exception $e) {
                    $message = 'Lỗi xử lý dữ liệu thứ tự hình ảnh: ' . $e->getMessage();
                    $messageType = 'danger';
                    error_log('Error processing image order data: ' . $e->getMessage());
                }
            } else {
                $message = 'Không có dữ liệu thứ tự hình ảnh.';
                $messageType = 'warning';
                error_log('No image order data');
            }

            // Nếu là yêu cầu AJAX, trả về JSON
            if ($isAjax) {
                // Xóa bất kỳ output nào đã được gửi
                if (ob_get_length()) ob_clean();

                // Đặt header và trả về JSON
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => $success,
                    'message' => $message
                ]);
                exit;
            }
        }
    }

    display_form:

    // Hiển thị form thêm/sửa bất động sản
    include 'property-form.php';
    exit;
}

// Lấy thông báo từ session (nếu có)
if (isset($_SESSION['message']) && isset($_SESSION['messageType'])) {
    $message = $_SESSION['message'];
    $messageType = $_SESSION['messageType'];

    // Xóa thông báo khỏi session sau khi đã lấy
    unset($_SESSION['message']);
    unset($_SESSION['messageType']);
}
// Lấy thông báo từ URL (nếu có) - giữ lại để tương thích với code cũ
else if (isset($_GET['message']) && isset($_GET['messageType'])) {
    $message = urldecode($_GET['message']);
    $messageType = $_GET['messageType'];
}

// Lấy danh sách bất động sản
$properties = $propertyModel->getAllProperties();
?>

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mt-2">Quản lý bất động sản</h1>
        <a href="index.php?page=properties&action=add" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i> Thêm mới
        </a>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-table me-1"></i>
            Danh sách bất động sản
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Hình ảnh</th>
                            <th>Tiêu đề</th>
                            <th>Loại</th>
                            <th>Địa chỉ</th>
                            <th>Giá</th>
                            <th>Chủ sở hữu</th>
                            <th>Trạng thái</th>
                            <th>Ngày hết hạn</th>
                            <th>Hành động</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($properties)): ?>
                            <tr>
                                <td colspan="10" class="text-center">Không có dữ liệu</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($properties as $property): ?>
                                <tr>
                                    <td><?php echo $property->id; ?></td>
                                    <td>
                                        <?php
                                        // Lấy hình ảnh đầu tiên của bất động sản
                                        $images = $propertyModel->getPropertyImages($property->id);
                                        $imagePath = !empty($images) ? $images[0]->image_path : 'default-property.jpg';
                                        ?>
                                        <img src="<?php echo BASE_URL . '/public/uploads/properties/' . $imagePath; ?>"
                                             alt="Property" class="img-thumbnail" width="80">
                                    </td>
                                    <td><?php echo $property->title; ?></td>
                                    <td><?php echo $property->type_name; ?></td>
                                    <td><?php echo $property->address; ?></td>
                                    <td>
                                        <?php echo number_format($property->price); ?> VNĐ
                                        <small class="d-block text-muted">
                                            <?php
                                            $periods = [
                                                'day' => 'ngày',
                                                'week' => 'tuần',
                                                'month' => 'tháng',
                                                'quarter' => 'quý',
                                                'year' => 'năm'
                                            ];
                                            echo '/' . ($periods[$property->price_period] ?? $property->price_period);
                                            ?>
                                        </small>
                                    </td>
                                    <td><?php echo $property->owner_name ?? 'N/A'; ?></td>
                                    <td>
                                        <?php
                                        $isExpired = false;
                                        if ($property->expiration_date && strtotime($property->expiration_date) < time()) {
                                            $isExpired = true;
                                        }

                                        if ($isExpired): ?>
                                            <span class="badge bg-danger">Hết hạn</span>
                                        <?php elseif ($property->status === 'display' && $property->active == 1): ?>
                                            <span class="badge bg-success">Đang hoạt động</span>
                                        <?php elseif ($property->active == 0): ?>
                                            <span class="badge bg-warning">Chờ duyệt</span>
                                        <?php elseif ($property->active == 2): ?>
                                            <span class="badge bg-danger">Bị từ chối</span>
                                        <?php elseif ($property->status === 'hide'): ?>
                                            <span class="badge bg-secondary">Đã ẩn</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Không xác định</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($property->expiration_date): ?>
                                            <?php
                                            $expirationDate = new DateTime($property->expiration_date);
                                            $now = new DateTime();
                                            $interval = $now->diff($expirationDate);
                                            $daysLeft = $interval->invert ? -$interval->days : $interval->days;

                                            echo date('d/m/Y', strtotime($property->expiration_date));

                                            if ($daysLeft > 0) {
                                                echo ' <small class="text-success">(' . $daysLeft . ' ngày nữa)</small>';
                                            } else if ($daysLeft == 0) {
                                                echo ' <small class="text-warning">(Hết hạn hôm nay)</small>';
                                            } else {
                                                echo ' <small class="text-danger">(Đã hết hạn)</small>';
                                            }
                                            ?>
                                        <?php else: ?>
                                            <span class="text-muted">Không có</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="index.php?page=properties&action=edit&id=<?php echo $property->id; ?>" class="btn btn-sm btn-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="index.php?page=properties&action=delete&id=<?php echo $property->id; ?>"
                                           class="btn btn-sm btn-danger"
                                           onclick="return confirm('Bạn có chắc chắn muốn xóa bất động sản này?');">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                        <a href="index.php?page=properties&action=extend&id=<?php echo $property->id; ?>"
                                           class="btn btn-sm btn-success"
                                           onclick="return confirm('Bạn có chắc chắn muốn gia hạn thêm 14 ngày cho bất động sản này?');">
                                            <i class="bi bi-calendar-plus"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php
// Kết thúc output buffering
ob_end_flush();
?>