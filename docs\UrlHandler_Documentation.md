# UrlHandler Documentation

## Tổng quan

UrlHandler là một class tập trung để xử lý tất cả logic URL SEO-friendly trong dự án. Nó thay thế các điều kiện if-else phức tạp bằng một cách tiếp cận thống nhất và dễ bảo trì.

## Lợi ích

1. **Tập trung hóa**: Tất cả logic xử lý URL được đặt trong một class duy nhất
2. **Dễ mở rộng**: Thêm field mới chỉ cần cập nhật một nơi
3. **D<PERSON> bảo trì**: <PERSON>h<PERSON>ng cần thêm nhiều điều kiện if-else cho mỗi trường hợp
4. **Linh hoạt**: <PERSON><PERSON> thể xử lý nhiều định dạng URL khác nhau
5. **T<PERSON><PERSON><PERSON> thích ngược**: Hỗ trợ tất cả URL hiện có

## Cấu trúc URL được hỗ trợ

### URL cơ bản
- `cho-thue-nha-dat` - Tất cả bất động sản
- `cho-thue-{type}` - Theo loại hình (ví dụ: cho-thue-can-ho)
- `cho-thue-nha-dat-tai-{ward}` - Theo phường/xã
- `cho-thue-{type}-tai-{ward}` - Theo loại hình và phường/xã

### URL với bộ lọc
- `cho-thue-{type}/gia-{price}-trieu` - Với giá
- `cho-thue-{type}/dt-{area}-m2` - Với diện tích
- `cho-thue-{type}/{bedrooms}pn` - Với số phòng ngủ
- `cho-thue-{type}/gia-{price}-trieu-dt-{area}-m2-{bedrooms}pn` - Kết hợp nhiều bộ lọc

### Định dạng giá
- `gia-5-7-trieu` - Khoảng giá từ 5-7 triệu
- `gia-tren-10-trieu` - Giá trên 10 triệu

### Định dạng diện tích
- `dt-50-70-m2` - Diện tích từ 50-70m²
- `dt-tren-90-m2` - Diện tích trên 90m²

### Định dạng phòng ngủ
- `2pn` - 2 phòng ngủ
- `4pn-tro-len` - 4+ phòng ngủ

## Cách sử dụng

### 1. Khởi tạo UrlHandler

```php
require_once BASE_PATH . '/app/libraries/UrlHandler.php';
$urlHandler = new UrlHandler();
```

### 2. Phân tích URL

```php
$url = 'cho-thue-can-ho-tai-hai-chau-1/gia-5-7-trieu-dt-50-70m2-2pn';
$params = $urlHandler->parseUrl($url);

// Kết quả:
// [
//     'type' => 'can-ho',
//     'ward' => 'hai-chau-1',
//     'price' => '5-7',
//     'area' => '50-70',
//     'bedrooms' => '2',
//     'matched' => true,
//     'controller' => 'SearchController',
//     'action' => 'filterByTypeAndWard'
// ]
```

### 3. Tạo URL

```php
$params = [
    'type' => 'can-ho',
    'ward' => 'hai-chau-1',
    'price' => '5-7',
    'area' => '50-70',
    'bedrooms' => '2'
];

$url = $urlHandler->buildUrl($params);
// Kết quả: /thuenhadanang/cho-thue-can-ho-tai-hai-chau-1/gia-5-7-trieu-dt-50-70m2-2pn
```

## Tích hợp với SearchController

### Cập nhật constructor

```php
public function __construct() {
    // ... existing code ...
    
    require_once BASE_PATH . '/app/libraries/UrlHandler.php';
    $this->urlHandler = new UrlHandler();
}
```

### Sử dụng trong các phương thức

```php
// Tạo URL SEO-friendly
private function buildSeoFriendlyUrl($params) {
    return $this->urlHandler->buildUrl($params);
}

// Tạo URL bộ lọc
public function buildFilterUrl($paramName, $paramValue) {
    // Phân tích URL hiện tại
    $currentUrl = $_SERVER['REQUEST_URI'];
    $path = preg_replace('/^\/thuenhadanang\//', '', parse_url($currentUrl, PHP_URL_PATH));
    $parsedParams = $this->urlHandler->parseUrl($path);
    
    // Cập nhật tham số
    $parsedParams[$paramName] = $paramValue;
    
    // Tạo URL mới
    return $this->urlHandler->buildUrl($parsedParams);
}
```

## Cập nhật index.php

### Trước (phức tạp)

```php
// Hàng trăm dòng code với nhiều điều kiện if-else phức tạp
if (preg_match('/^cho-thue-([a-z0-9-]+)-tai-([a-z0-9-]+)\/gia-(.+)$/', $url, $matches)) {
    // Logic xử lý phức tạp...
} else if (preg_match('/^cho-thue-([a-z0-9-]+)\/(.+)$/', $url, $matches)) {
    // Logic xử lý phức tạp khác...
}
// ... nhiều điều kiện khác
```

### Sau (đơn giản)

```php
// Load UrlHandler
require_once APP_PATH . '/libraries/UrlHandler.php';
$urlHandler = new UrlHandler();
$parsedParams = $urlHandler->parseUrl($url);

// Kiểm tra nếu URL được nhận diện
if ($parsedParams['matched']) {
    $controllerName = $parsedParams['controller'];
    $action = $parsedParams['action'];
    
    // Load controller và thực thi action
    $controllerFile = APP_PATH . '/controllers/' . $controllerName . '.php';
    if (file_exists($controllerFile)) {
        require_once $controllerFile;
        $controller = new $controllerName();
        $controller->$action($parsedParams['type'], $parsedParams['ward'], $parsedParams);
        exit;
    }
}
```

## Mở rộng trong tương lai

### Thêm field mới

1. Cập nhật mảng `$params` trong `parseUrl()`:
```php
$params = [
    'type' => '',
    'ward' => '',
    'price' => '',
    'area' => '',
    'bedrooms' => '',
    'bathrooms' => '',
    'direction' => '',
    'new_field' => ''  // Thêm field mới
];
```

2. Thêm logic parsing trong `parseFilters()`:
```php
// Xử lý field mới
else if ($segment == 'new-field') {
    $params['new_field'] = $this->parseNewField($filterSegments, $i);
}
```

3. Thêm logic building trong `buildFilterSegment()`:
```php
// Thêm new_field
if (!empty($params['new_field'])) {
    $filterParts[] = 'new-field-' . $this->formatNewFieldForUrl($params['new_field']);
}
```

## Testing

Sử dụng file `test_url_handler.php` để kiểm tra:

```bash
php test_url_handler.php
```

File này sẽ test các trường hợp:
- Parsing URL thành parameters
- Building URL từ parameters
- Round-trip testing (parse -> build -> parse)

## Lưu ý quan trọng

1. **Tương thích ngược**: UrlHandler được thiết kế để tương thích với tất cả URL hiện có
2. **Performance**: Sử dụng caching cho database queries trong PropertyType và Ward models
3. **Error handling**: Luôn kiểm tra `$parsedParams['matched']` trước khi sử dụng
4. **Logging**: UrlHandler có logging chi tiết để debug
5. **Validation**: Luôn validate type và ward với database trước khi sử dụng
