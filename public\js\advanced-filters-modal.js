/**
 * Advanced Filters Modal Component
 * Airbnb-style modal for advanced property search filters
 */

class AdvancedFiltersModal {
    constructor() {
        this.isOpen = false;
        this.filters = {
            bedrooms: '',
            bathrooms: '',
            area: '',
            direction: ''
        };
        this.originalFilters = {};
        this.onApplyCallback = null;

        this.init();
    }

    init() {
        this.createModal();
        this.setupEventListeners();
        console.log('🎛️ Advanced Filters Modal initialized');
    }

    createModal() {
        // Create modal HTML structure
        const modalHTML = `
            <div class="advanced-filters-modal" id="advancedFiltersModal">
                <div class="modal-backdrop" id="modalBackdrop"></div>
                <div class="modal-container">
                    <div class="modal-header">
                        <h2 class="modal-title">
                            <i class="bi bi-sliders me-2"></i>
                            B<PERSON> lọc nâng cao
                        </h2>
                        <button class="modal-close-btn" id="modalCloseBtn">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>

                    <div class="modal-body">
                        <!-- Bedrooms Section -->
                        <div class="filter-section">
                            <h3 class="filter-section-title">Phòng ngủ</h3>
                            <div class="filter-options-grid">
                                <button type="button" class="filter-option-btn" data-filter="bedrooms" data-value="">
                                    Tất cả
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="bedrooms" data-value="1">
                                    1 phòng
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="bedrooms" data-value="2">
                                    2 phòng
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="bedrooms" data-value="3">
                                    3 phòng
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="bedrooms" data-value="4+">
                                    4+ phòng
                                </button>
                            </div>
                        </div>

                        <!-- Bathrooms Section -->
                        <div class="filter-section">
                            <h3 class="filter-section-title">Phòng tắm</h3>
                            <div class="filter-options-grid">
                                <button type="button" class="filter-option-btn" data-filter="bathrooms" data-value="">
                                    Tất cả
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="bathrooms" data-value="1">
                                    1 phòng
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="bathrooms" data-value="2">
                                    2 phòng
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="bathrooms" data-value="3+">
                                    3+ phòng
                                </button>
                            </div>
                        </div>

                        <!-- Area Section -->
                        <div class="filter-section">
                            <h3 class="filter-section-title">Diện tích</h3>
                            <div class="filter-options-grid">
                                <button type="button" class="filter-option-btn" data-filter="area" data-value="">
                                    Tất cả
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="area" data-value="0-20">
                                    Dưới 20m²
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="area" data-value="20-30">
                                    20-30m²
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="area" data-value="30-50">
                                    30-50m²
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="area" data-value="50-70">
                                    50-70m²
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="area" data-value="70-90">
                                    70-90m²
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="area" data-value="90+">
                                    Trên 90m²
                                </button>
                            </div>
                        </div>

                        <!-- Direction Section -->
                        <div class="filter-section">
                            <h3 class="filter-section-title">Hướng nhà</h3>
                            <div class="filter-options-grid direction-grid">
                                <button type="button" class="filter-option-btn" data-filter="direction" data-value="">
                                    Tất cả
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="direction" data-value="dong">
                                    <i class="bi bi-arrow-right me-1"></i>Đông
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="direction" data-value="tay">
                                    <i class="bi bi-arrow-left me-1"></i>Tây
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="direction" data-value="nam">
                                    <i class="bi bi-arrow-down me-1"></i>Nam
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="direction" data-value="bac">
                                    <i class="bi bi-arrow-up me-1"></i>Bắc
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="direction" data-value="dong-bac">
                                    <i class="bi bi-arrow-up-right me-1"></i>Đông Bắc
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="direction" data-value="dong-nam">
                                    <i class="bi bi-arrow-down-right me-1"></i>Đông Nam
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="direction" data-value="tay-bac">
                                    <i class="bi bi-arrow-up-left me-1"></i>Tây Bắc
                                </button>
                                <button type="button" class="filter-option-btn" data-filter="direction" data-value="tay-nam">
                                    <i class="bi bi-arrow-down-left me-1"></i>Tây Nam
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn-clear-all" id="clearAllFiltersBtn">
                            <i class="bi bi-x-circle me-2"></i>
                            Xóa tất cả
                        </button>
                        <button type="button" class="btn-show-results" id="showResultsBtn">
                            <i class="bi bi-search me-2"></i>
                            <span class="results-text">Xem kết quả</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add modal to DOM
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.modal = document.getElementById('advancedFiltersModal');

        // Add CSS styles
        this.addModalStyles();
    }

    addModalStyles() {
        const styles = document.createElement('style');
        styles.id = 'advanced-filters-modal-styles';
        styles.textContent = `
            .advanced-filters-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1050;
                display: none;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .advanced-filters-modal.show {
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 1;
            }

            .modal-backdrop {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1051;
            }

            .modal-container {
                position: relative;
                background: white;
                border-radius: 16px;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
                max-width: 600px;
                width: 90%;
                max-height: 90vh;
                overflow: hidden;
                transform: scale(0.9);
                transition: transform 0.3s ease;
                z-index: 1052;
            }

            .advanced-filters-modal.show .modal-container {
                transform: scale(1);
            }

            .modal-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 24px 24px 16px;
                border-bottom: 1px solid #e5e7eb;
            }

            .modal-title {
                margin: 0;
                font-size: 20px;
                font-weight: 600;
                color: #1f2937;
                display: flex;
                align-items: center;
            }

            .modal-close-btn {
                background: none;
                border: none;
                padding: 8px;
                border-radius: 50%;
                cursor: pointer;
                color: #6b7280;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
            }

            .modal-close-btn:hover {
                background: #f3f4f6;
                color: #374151;
            }

            .modal-body {
                padding: 24px;
                max-height: 60vh;
                overflow-y: auto;
            }

            .filter-section {
                margin-bottom: 32px;
            }

            .filter-section:last-child {
                margin-bottom: 0;
            }

            .filter-section-title {
                font-size: 16px;
                font-weight: 600;
                color: #1f2937;
                margin: 0 0 16px 0;
            }

            .filter-options-grid {
                display: grid;
                grid-template-columns: repeat(5, 1fr);
                gap: 8px;
            }

            .direction-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .filter-option-btn {
                background: #f9fafb;
                border: 2px solid #e5e7eb;
                border-radius: 6px;
                padding: 6px 8px;
                font-size: 12px;
                font-weight: 500;
                color: #374151;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 32px;
                text-align: center;
            }

            .filter-option-btn:hover {
                border-color: #d1d5db;
                background: #f3f4f6;
            }

            .filter-option-btn.active {
                background: #3b82f6;
                border-color: #3b82f6;
                color: white;
            }

            .filter-option-btn.active:hover {
                background: #2563eb;
                border-color: #2563eb;
            }

            .modal-footer {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 24px;
                border-top: 1px solid #e5e7eb;
                background: #f9fafb;
            }

            .btn-clear-all {
                background: none;
                border: none;
                color: #6b7280;
                font-size: 13px;
                font-weight: 500;
                cursor: pointer;
                padding: 6px 12px;
                border-radius: 6px;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
            }

            .btn-clear-all:hover {
                background: #f3f4f6;
                color: #374151;
            }

            .btn-show-results {
                background: #3b82f6;
                border: none;
                color: white;
                font-size: 13px;
                font-weight: 600;
                padding: 8px 16px;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                min-width: 120px;
                justify-content: center;
            }

            .btn-show-results:hover {
                background: #2563eb;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }

            .btn-show-results:active {
                transform: translateY(0);
            }

            /* Mobile Responsive */
            @media (max-width: 768px) {
                .modal-container {
                    width: 95%;
                    max-height: 95vh;
                    border-radius: 12px;
                }

                .modal-header {
                    padding: 20px 20px 16px;
                }

                .modal-title {
                    font-size: 18px;
                }

                .modal-body {
                    padding: 20px;
                    max-height: 70vh;
                }

                .filter-options-grid {
                    grid-template-columns: repeat(3, 1fr);
                    gap: 6px;
                }

                .direction-grid {
                    grid-template-columns: repeat(2, 1fr);
                }

                .filter-option-btn {
                    padding: 6px 8px;
                    font-size: 11px;
                    min-height: 32px;
                }

                .modal-footer {
                    padding: 16px 20px;
                    flex-direction: column;
                    gap: 12px;
                }

                .btn-show-results {
                    width: 100%;
                }
            }

            /* Animation for filter count badge */
            .filter-count-badge {
                background: #ef4444;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: 600;
                margin-left: 8px;
                animation: bounceIn 0.3s ease;
            }

            @keyframes bounceIn {
                0% { transform: scale(0); }
                50% { transform: scale(1.2); }
                100% { transform: scale(1); }
            }
        `;

        document.head.appendChild(styles);
    }

    setupEventListeners() {
        // Close modal events
        const closeBtn = document.getElementById('modalCloseBtn');
        const backdrop = document.getElementById('modalBackdrop');

        closeBtn?.addEventListener('click', () => this.close());
        backdrop?.addEventListener('click', () => this.close());

        // ESC key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });

        // Filter option buttons
        this.modal.addEventListener('click', (e) => {
            if (e.target.classList.contains('filter-option-btn')) {
                this.handleFilterSelection(e.target);
            }
        });

        // Clear all button
        const clearAllBtn = document.getElementById('clearAllFiltersBtn');
        clearAllBtn?.addEventListener('click', () => this.clearAllFilters());

        // Show results button
        const showResultsBtn = document.getElementById('showResultsBtn');
        showResultsBtn?.addEventListener('click', () => this.applyFilters());
    }

    open() {
        if (this.isOpen) return;

        // Store original filters for cancel functionality
        this.originalFilters = { ...this.filters };

        // Load current filters from form
        this.loadCurrentFilters();

        // Show modal
        this.modal.classList.add('show');
        this.isOpen = true;

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Update UI
        this.updateFilterButtons();
        this.updateResultsButton();

        console.log('🎛️ Advanced Filters Modal opened');
    }

    close() {
        if (!this.isOpen) return;

        this.modal.classList.remove('show');
        this.isOpen = false;

        // Restore body scroll
        document.body.style.overflow = '';

        console.log('🎛️ Advanced Filters Modal closed');
    }

    loadCurrentFilters() {
        // Load filters from form fields
        const bedroomsField = document.getElementById('searchBedrooms');
        const bathroomsField = document.getElementById('searchBathrooms');
        const areaField = document.getElementById('searchArea');
        const directionField = document.getElementById('searchDirection');

        this.filters = {
            bedrooms: bedroomsField?.value || '',
            bathrooms: bathroomsField?.value || '',
            area: areaField?.value || '',
            direction: directionField?.value || ''
        };
    }

    handleFilterSelection(button) {
        const filterType = button.dataset.filter;
        const filterValue = button.dataset.value;

        // Update filter value
        this.filters[filterType] = filterValue;

        // Update button states for this filter type
        const filterButtons = this.modal.querySelectorAll(`[data-filter="${filterType}"]`);
        filterButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');

        // Update results button
        this.updateResultsButton();

        console.log(`🎛️ Filter updated: ${filterType} = ${filterValue}`);
    }

    updateFilterButtons() {
        // Update active states based on current filters
        Object.keys(this.filters).forEach(filterType => {
            const value = this.filters[filterType];
            const button = this.modal.querySelector(`[data-filter="${filterType}"][data-value="${value}"]`);

            // Clear all buttons for this filter type
            const filterButtons = this.modal.querySelectorAll(`[data-filter="${filterType}"]`);
            filterButtons.forEach(btn => btn.classList.remove('active'));

            // Set active button
            if (button) {
                button.classList.add('active');
            }
        });
    }

    updateResultsButton() {
        const showResultsBtn = document.getElementById('showResultsBtn');
        const resultsText = showResultsBtn?.querySelector('.results-text');

        if (!showResultsBtn || !resultsText) return;

        // Count active filters
        const activeFiltersCount = Object.values(this.filters).filter(value => value !== '').length;

        // Text luôn là "Xem kết quả" - không hiển thị số
        resultsText.textContent = 'Xem kết quả';

        if (activeFiltersCount > 0) {
            // Add badge if not exists
            if (!showResultsBtn.querySelector('.filter-count-badge')) {
                const badge = document.createElement('span');
                badge.className = 'filter-count-badge';
                badge.textContent = activeFiltersCount;
                showResultsBtn.appendChild(badge);
            } else {
                showResultsBtn.querySelector('.filter-count-badge').textContent = activeFiltersCount;
            }
        } else {
            // Remove badge when no filters
            const badge = showResultsBtn.querySelector('.filter-count-badge');
            if (badge) {
                badge.remove();
            }
        }
    }

    clearAllFilters() {
        // Reset all filters
        this.filters = {
            bedrooms: '',
            bathrooms: '',
            area: '',
            direction: ''
        };

        // Update UI
        this.updateFilterButtons();
        this.updateResultsButton();

        console.log('🎛️ All filters cleared');
    }

    applyFilters() {
        // Update form fields
        this.updateFormFields();

        // Close modal
        this.close();

        // Trigger search callback
        if (this.onApplyCallback) {
            this.onApplyCallback(this.filters);
        }

        console.log('🎛️ Filters applied:', this.filters);
    }

    updateFormFields() {
        // Update hidden form fields with selected filters
        const bedroomsField = document.getElementById('searchBedrooms');
        const bathroomsField = document.getElementById('searchBathrooms');
        const areaField = document.getElementById('searchArea');
        const directionField = document.getElementById('searchDirection');

        if (bedroomsField) bedroomsField.value = this.filters.bedrooms;
        if (bathroomsField) bathroomsField.value = this.filters.bathrooms;
        if (areaField) areaField.value = this.filters.area;
        if (directionField) directionField.value = this.filters.direction;
    }

    // Public methods
    setOnApplyCallback(callback) {
        this.onApplyCallback = callback;
    }

    getFilters() {
        return { ...this.filters };
    }

    setFilters(filters) {
        this.filters = { ...this.filters, ...filters };
        this.updateFilterButtons();
        this.updateResultsButton();
    }
}

// Export for use
window.AdvancedFiltersModal = AdvancedFiltersModal;
