<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="container mt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/thuenhadanang" class="text-decoration-none">Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Bảng điều khiển</li>
    </ol>
</nav>

<div class="container py-4">
    <div class="row">
        <!-- Include Sidebar -->
        <?php require_once 'app/views/partials/dashboard-sidebar.php'; ?>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Thống kê tổng quan -->
            <div class="row g-3 mb-4">
                <div class="col-sm-6 col-lg-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-3">
                                    <div class="bg-primary bg-opacity-10 p-3 rounded">
                                        <i class="bi bi-house text-primary fs-4"></i>
                                    </div>
                                </div>
                                <div>
                                    <p class="mb-0 text-muted small">Tổng tin đăng</p>
                                    <h4 class="mb-0"><?php echo $data['stats']->total_properties ?? 0; ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6 col-lg-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-3">
                                    <div class="bg-success bg-opacity-10 p-3 rounded">
                                        <i class="bi bi-check-circle text-success fs-4"></i>
                                    </div>
                                </div>
                                <div>
                                    <p class="mb-0 text-muted small">Đang hoạt động</p>
                                    <h4 class="mb-0"><?php echo $data['stats']->active_properties ?? 0; ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6 col-lg-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-3">
                                    <div class="bg-warning bg-opacity-10 p-3 rounded">
                                        <i class="bi bi-exclamation-circle text-warning fs-4"></i>
                                    </div>
                                </div>
                                <div>
                                    <p class="mb-0 text-muted small">Hết hạn</p>
                                    <h4 class="mb-0"><?php echo $data['stats']->expired_properties ?? 0; ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Thông tin gói dịch vụ -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3 border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Gói dịch vụ hiện tại</h5>
                        <a href="/thuenhadanang/pricing" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-box-seam me-1"></i>Xem bảng giá
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($data['user_package']): ?>
                        <?php
                        $userPackage = $data['user_package'];

                        // Lấy thông tin package để kiểm tra giá
                        require_once 'app/models/Package.php';
                        $packageModel = new Package();
                        $package = $packageModel->getPackageById($userPackage->package_id);

                        // Kiểm tra xem có phải gói miễn phí không
                        $isFreePackage = $package && $package->price == 0;

                        // Chỉ kiểm tra hết hạn nếu không phải gói miễn phí
                        $isExpired = !$isFreePackage && strtotime($userPackage->end_date) < time();

                        // Tính số ngày còn lại (chỉ cho gói trả phí)
                        if ($isFreePackage) {
                            $daysLeft = null; // Vĩnh viễn
                        } else {
                            $daysLeft = max(0, ceil((strtotime($userPackage->end_date) - time()) / 86400));
                        }

                        // Sử dụng số tin thực tế thay vì số tin lưu trong database
                        $actualPostsUsed = $userPackage->actual_posts_used ?? $userPackage->posts_used;
                        $actualPostsRemaining = $userPackage->actual_posts_remaining ?? $userPackage->posts_remaining;
                        $postLimit = $userPackage->package_post_limit ?? $userPackage->post_limit;

                        $usagePercentage = $postLimit > 0 ? ($actualPostsUsed / $postLimit) * 100 : 0;
                        ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0 me-3">
                                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                                            <i class="bi bi-box-seam text-primary fs-4"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($userPackage->package_name); ?></h6>
                                        <div class="d-flex align-items-center">
                                            <?php if ($isExpired): ?>
                                                <span class="badge bg-danger me-2">Hết hạn</span>
                                            <?php elseif ($isFreePackage): ?>
                                                <span class="badge bg-success me-2">Vĩnh viễn</span>
                                            <?php elseif ($daysLeft !== null && $daysLeft <= 7): ?>
                                                <span class="badge bg-warning me-2">Sắp hết hạn</span>
                                            <?php else: ?>
                                                <span class="badge bg-success me-2">Đang hoạt động</span>
                                            <?php endif; ?>
                                            <small class="text-muted">
                                                <?php if ($isExpired): ?>
                                                    Đã hết hạn
                                                <?php elseif ($isFreePackage): ?>
                                                    Không giới hạn thời gian
                                                <?php else: ?>
                                                    Còn <?php echo $daysLeft; ?> ngày
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="small text-muted">Tin đăng đã sử dụng</span>
                                        <span class="small fw-bold">
                                            <?php echo $actualPostsUsed; ?>/<?php echo $postLimit; ?>
                                        </span>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <?php
                                        $progressClass = 'bg-success';
                                        if ($usagePercentage >= 80) {
                                            $progressClass = 'bg-danger';
                                        } elseif ($usagePercentage >= 60) {
                                            $progressClass = 'bg-warning';
                                        }
                                        ?>
                                        <div class="progress-bar <?php echo $progressClass; ?>"
                                             style="width: <?php echo $usagePercentage; ?>%"></div>
                                    </div>
                                    <div class="d-flex justify-content-between mt-1">
                                        <small class="text-muted">Còn lại: <?php echo $actualPostsRemaining; ?> tin</small>
                                        <?php if ($actualPostsRemaining <= 2): ?>
                                        <small class="text-danger">Sắp hết lượt đăng!</small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="bi bi-calendar me-1"></i>
                                    Bắt đầu: <?php echo date('d/m/Y', strtotime($userPackage->start_date)); ?>
                                </small>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="bi bi-calendar-x me-1"></i>
                                    <?php if ($isFreePackage): ?>
                                        Kết thúc: Vĩnh viễn
                                    <?php else: ?>
                                        Kết thúc: <?php echo date('d/m/Y', strtotime($userPackage->end_date)); ?>
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>

                        <?php if ($isExpired || (!$isFreePackage && $actualPostsRemaining <= 2)): ?>
                        <div class="alert alert-warning mt-3 mb-0">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <div class="flex-grow-1">
                                    <?php if ($isExpired): ?>
                                        Gói dịch vụ của bạn đã hết hạn. Vui lòng liên hệ để gia hạn.
                                    <?php else: ?>
                                        Bạn sắp hết lượt đăng tin. Hãy nâng cấp gói để tiếp tục sử dụng dịch vụ.
                                    <?php endif; ?>
                                </div>
                                <a href="https://zalo.me/0944170391" target="_blank" class="btn btn-sm btn-warning">
                                    <i class="bi bi-chat-dots me-1"></i>Liên hệ nâng cấp
                                </a>
                            </div>
                        </div>
                        <?php elseif ($isFreePackage && $actualPostsRemaining <= 2): ?>
                        <div class="alert alert-info mt-3 mb-0">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-info-circle me-2"></i>
                                <div class="flex-grow-1">
                                    Bạn sắp hết lượt đăng tin trong gói miễn phí. Hãy nâng cấp lên gói Chuyên nghiệp để có thêm nhiều tin đăng.
                                </div>
                                <a href="https://zalo.me/0944170391" target="_blank" class="btn btn-sm btn-primary">
                                    <i class="bi bi-chat-dots me-1"></i>Nâng cấp ngay
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-box-seam fs-1 text-muted"></i>
                            <h6 class="mt-3 mb-2">Chưa có gói dịch vụ</h6>
                            <p class="text-muted mb-3">Bạn chưa được gán gói dịch vụ nào. Vui lòng liên hệ admin để được hỗ trợ.</p>
                            <a href="https://zalo.me/0944170391" target="_blank" class="btn btn-primary">
                                <i class="bi bi-chat-dots me-2"></i>Liên hệ hỗ trợ
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Tin đăng gần đây -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3 border-0">
                    <h5 class="mb-0">Tin đăng gần đây</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($data['recent_properties'])): ?>
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th scope="col">Tiêu đề</th>
                                        <th scope="col">Trạng thái</th>
                                        <th scope="col">Ngày đăng</th>
                                        <th scope="col"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['recent_properties'] as $property): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php
                                                // Xác định hình ảnh hiển thị
                                                $imagePath = 'default-property.jpg'; // Hình ảnh mặc định

                                                // Nếu có main_image, sử dụng nó
                                                if (!empty($property->main_image)) {
                                                    $imagePath = $property->main_image;
                                                }
                                                // Nếu có images (JSON), lấy hình ảnh đầu tiên
                                                else if (!empty($property->images)) {
                                                    $imagesArray = json_decode($property->images, true);
                                                    if (is_array($imagesArray) && count($imagesArray) > 0) {
                                                        $imagePath = $imagesArray[0];
                                                    }
                                                }
                                                ?>
                                                <img src="/thuenhadanang/public/uploads/properties/<?php echo htmlspecialchars($imagePath); ?>"
                                                     class="rounded me-2"
                                                     style="width: 40px; height: 40px; object-fit: cover;">
                                                <div class="text-truncate" style="max-width: 200px;">
                                                    <?php echo htmlspecialchars($property->title); ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            // Kiểm tra ngày hết hạn
                                            $isExpired = false;
                                            if ($property->expiration_date && strtotime($property->expiration_date) < time()) {
                                                $isExpired = true;
                                            }

                                            if ($isExpired): ?>
                                                <span class="badge bg-danger">Hết hạn</span>
                                            <?php elseif ($property->status === 'display' && $property->active == 1): ?>
                                                <span class="badge bg-success">Đang hoạt động</span>
                                            <?php elseif ($property->active == 0): ?>
                                                <span class="badge bg-warning">Chờ duyệt</span>
                                            <?php elseif ($property->active == 2 || $property->status === 'hide'): ?>
                                                <span class="badge bg-secondary">Đã ẩn</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('d/m/Y', strtotime($property->created_at)); ?></td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="/thuenhadanang/property/<?php echo $property->id; ?>"
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="/thuenhadanang/property/edit/<?php echo $property->id; ?>"
                                                   class="btn btn-sm btn-outline-secondary">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center py-4 mb-0">Bạn chưa có tin đăng nào</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>