<?php
require_once 'app/models/User.php';

class RegisterController {
    private $userModel;
    
    public function __construct() {
        $this->userModel = new User();
    }

    public function index() {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Nếu đã đăng nhập thì chuyển về trang chủ
        if (isset($_SESSION['user_id'])) {
            header('Location: /thuenhadanang');
            exit;
        }

        // Thiết lập tiêu đề trang
        $title = 'Đăng ký - Thuê Nhà Đà Nẵng';
        
        // Lấy thông báo lỗi nếu có
        $error = $_SESSION['register_error'] ?? '';
        unset($_SESSION['register_error']);
        
        // Thiết lập view và data
        $view = 'register';
        $data = ['error' => $error];
        
        // Render layout với view
        require 'app/views/layout.php';
    }

    public function create() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Khởi tạo session nếu chưa có
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            // Lấy và lọc dữ liệu từ form
            $data = [
                'fullname' => filter_input(INPUT_POST, 'fullname', FILTER_SANITIZE_SPECIAL_CHARS),
                'email' => filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL),
                'password' => $_POST['password'] ?? '',
                'confirm_password' => $_POST['confirm_password'] ?? '',
                'avatar' => 'default-avatar.jpg',
                'role' => 'user'
            ];

            // Validate dữ liệu
            $errors = [];
            
            if (empty($data['fullname'])) {
                $errors[] = 'Vui lòng nhập họ tên';
            }
            
            if (empty($data['email'])) {
                $errors[] = 'Vui lòng nhập email';
            } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Email không hợp lệ';
            } elseif ($this->userModel->findUserByEmail($data['email'])) {
                $errors[] = 'Email đã được sử dụng';
            }
            
            if (empty($data['password'])) {
                $errors[] = 'Vui lòng nhập mật khẩu';
            } elseif (strlen($data['password']) < 6) {
                $errors[] = 'Mật khẩu phải có ít nhất 6 ký tự';
            }
            
            if ($data['password'] !== $data['confirm_password']) {
                $errors[] = 'Xác nhận mật khẩu không khớp';
            }

            // Nếu có lỗi
            if (!empty($errors)) {
                $_SESSION['register_error'] = implode('<br>', $errors);
                header('Location: /thuenhadanang/register');
                exit;
            }

            // Hash mật khẩu
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);

            // Thêm user mới
            if ($this->userModel->create($data)) {
                // Đăng nhập luôn sau khi đăng ký thành công
                $user = $this->userModel->getUserByEmail($data['email']);
                
                $_SESSION['user_id'] = $user->id;
                $_SESSION['user_role'] = $user->role;
                $_SESSION['user_name'] = $user->fullname;
                $_SESSION['user_email'] = $user->email;
                $_SESSION['user_avatar'] = $user->avatar;

                header('Location: /thuenhadanang');
                exit;
            } else {
                $_SESSION['register_error'] = 'Có lỗi xảy ra, vui lòng thử lại';
                header('Location: /thuenhadanang/register');
                exit;
            }
        }

        // Nếu không phải POST request, chuyển về trang đăng ký
        header('Location: /thuenhadanang/register');
        exit;
    }
} 