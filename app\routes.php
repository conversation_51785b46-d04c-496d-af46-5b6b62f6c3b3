<?php

$routes = [
    // Trang chủ
    '' => ['HomeController', 'index'],
    'index' => ['HomeController', 'index'],

    // Đăng nhập
    'login' => ['LoginController', 'index'],
    'login/auth' => ['LoginController', 'auth'],
    'logout' => ['LogoutController', 'index'],

    // Đăng ký
    'register' => ['RegisterController', 'index'],
    'register/create' => ['RegisterController', 'create'],

    // Quản lý tài khoản
    'dashboard' => ['DashboardController', 'index'],
    'dashboard/update' => ['DashboardController', 'updateProfile'],
    'dashboard/update-avatar' => ['DashboardController', 'updateAvatar'],
    'dashboard/profile' => ['ProfileController', 'index'],
    'dashboard/profile/update' => ['ProfileController', 'update'],
    'dashboard/password' => ['PasswordController', 'index'],
    'dashboard/password/update' => ['PasswordController', 'update'],

    // Quản lý bất động sản của người dùng
    'dashboard/properties' => ['UserPropertiesController', 'index'],

    'property/delete/{id}' => ['UserPropertiesController', 'delete'],
    'property/extend/{id}' => ['UserPropertiesController', 'extend'], // Chỉ chấp nhận POST với CSRF token
    'property/upload-image/{id}' => ['UserPropertiesController', 'uploadImage'],
    'property/delete-image/{id}' => ['UserPropertiesController', 'deleteImage'],
    'property/edit/{id}' => ['UserPropertiesController', 'edit'],
    'property/update/{id}' => ['UserPropertiesController', 'update'],

    // Quên mật khẩu
    'forgot-password' => ['PasswordController', 'forgot'],
    'forgot-password/send' => ['PasswordController', 'sendResetLink'],

    // Tìm kiếm
    'search' => ['SearchController', 'index'],

    // Bảng giá dịch vụ
    'pricing' => ['PricingController', 'index'],

    // API endpoints
    'api/properties' => ['ApiController', 'getProperties'],

    // Đăng tin bất động sản
    'property-listing' => ['PropertyListingController', 'index'],
    'property-listing/step1' => ['PropertyListingController', 'step1'],
    'property-listing/step2' => ['PropertyListingController', 'step2'],
    'property-listing/upload-image' => ['PropertyListingController', 'uploadImage'],
    'property-listing/remove-image' => ['PropertyListingController', 'removeImage'],
    'property-listing/save-property' => ['PropertyListingController', 'saveProperty'],

    // Admin - Quản lý yêu cầu gia hạn (sử dụng AJAX endpoints)
    'quantrivien/extension-requests/approve/{id}' => ['AdminExtensionController', 'approve'],
    'quantrivien/extension-requests/reject/{id}' => ['AdminExtensionController', 'reject'],

    // Chi tiết bất động sản - URL dạng: slug-id/
    '{slug}-{id}/' => ['PropertyController', 'detail']
];