<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Search Console Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .console-container {
            position: fixed;
            top: 0;
            right: 0;
            width: 400px;
            height: 100vh;
            background: #1e1e1e;
            color: #fff;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            z-index: 9999;
            overflow-y: auto;
            padding: 10px;
            border-left: 2px solid #f77316;
        }
        
        .console-header {
            background: #f77316;
            color: white;
            padding: 8px 12px;
            margin: -10px -10px 10px -10px;
            font-weight: bold;
        }
        
        .console-log {
            margin-bottom: 5px;
            padding: 2px 0;
            border-bottom: 1px solid #333;
        }
        
        .log-timestamp {
            color: #888;
            font-size: 10px;
        }
        
        .log-mobile {
            color: #4CAF50;
        }
        
        .log-error {
            color: #f44336;
        }
        
        .log-warn {
            color: #ff9800;
        }
        
        .log-info {
            color: #2196F3;
        }
        
        .main-content {
            margin-right: 420px;
            padding: 20px;
        }
        
        .test-controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-btn {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background: #f77316;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-btn:hover {
            background: #e56b17;
        }
        
        .iframe-container {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        
        .iframe-container iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <!-- Console Panel -->
    <div class="console-container">
        <div class="console-header">
            📱 Mobile Search Console
            <button onclick="clearConsole()" style="float: right; background: none; border: none; color: white; cursor: pointer;">Clear</button>
        </div>
        <div id="consoleOutput"></div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="test-controls">
            <h3>🔍 Mobile Search Test Controls</h3>
            <p>Use these buttons to test mobile search functionality:</p>
            
            <button class="test-btn" onclick="testMobileSearchInit()">Test Mobile Search Init</button>
            <button class="test-btn" onclick="testDesktopAjaxAvailability()">Test Desktop AJAX</button>
            <button class="test-btn" onclick="testAPICall()">Test API Call</button>
            <button class="test-btn" onclick="simulateModalApply()">Simulate Modal Apply</button>
            <button class="test-btn" onclick="testFormSubmission()">Test Form Submission</button>
            
            <hr>
            
            <h4>Instructions:</h4>
            <ol>
                <li>Open the search page in the iframe below</li>
                <li>Open mobile view (F12 → Device toolbar)</li>
                <li>Try to use mobile search filters</li>
                <li>Watch console logs on the right</li>
            </ol>
        </div>
        
        <div class="iframe-container">
            <iframe src="/thuenhadanang/search" id="searchFrame"></iframe>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let consoleOutput;
        
        document.addEventListener('DOMContentLoaded', function() {
            consoleOutput = document.getElementById('consoleOutput');
            logToConsole('🚀 Mobile Search Console initialized', 'info');
            
            // Intercept console logs from iframe
            interceptIframeConsole();
        });
        
        function logToConsole(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `console-log log-${type}`;
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-message">${message}</span>
            `;
            
            consoleOutput.appendChild(logEntry);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '';
            logToConsole('Console cleared', 'info');
        }
        
        function interceptIframeConsole() {
            const iframe = document.getElementById('searchFrame');
            
            iframe.onload = function() {
                try {
                    const iframeWindow = iframe.contentWindow;
                    const originalConsole = iframeWindow.console;
                    
                    // Override console methods
                    ['log', 'error', 'warn', 'info'].forEach(method => {
                        iframeWindow.console[method] = function(...args) {
                            // Call original console method
                            originalConsole[method].apply(originalConsole, args);
                            
                            // Log to our console
                            const message = args.map(arg => 
                                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
                            ).join(' ');
                            
                            if (message.includes('📱')) {
                                logToConsole(message, 'mobile');
                            } else if (method === 'error') {
                                logToConsole(message, 'error');
                            } else if (method === 'warn') {
                                logToConsole(message, 'warn');
                            } else {
                                logToConsole(message, 'info');
                            }
                        };
                    });
                    
                    logToConsole('✅ Console interception setup complete', 'info');
                    
                } catch (error) {
                    logToConsole('❌ Cannot intercept iframe console: ' + error.message, 'error');
                }
            };
        }
        
        async function testMobileSearchInit() {
            logToConsole('🔍 Testing mobile search initialization...', 'info');
            
            try {
                const iframe = document.getElementById('searchFrame');
                const iframeWindow = iframe.contentWindow;
                
                if (iframeWindow.mobileSearch) {
                    logToConsole('✅ Mobile search found', 'mobile');
                    logToConsole('📱 Mobile search methods: ' + Object.getOwnPropertyNames(iframeWindow.mobileSearch.constructor.prototype).join(', '), 'info');
                } else {
                    logToConsole('❌ Mobile search not found', 'error');
                }
                
                if (iframeWindow.ajaxSearch) {
                    logToConsole('✅ Desktop AJAX found', 'info');
                } else {
                    logToConsole('❌ Desktop AJAX not found', 'error');
                }
                
            } catch (error) {
                logToConsole('❌ Error testing init: ' + error.message, 'error');
            }
        }
        
        async function testDesktopAjaxAvailability() {
            logToConsole('🖥️ Testing desktop AJAX availability...', 'info');
            
            try {
                const iframe = document.getElementById('searchFrame');
                const iframeWindow = iframe.contentWindow;
                
                if (iframeWindow.ajaxSearch) {
                    logToConsole('✅ Desktop AJAX exists', 'info');
                    
                    if (typeof iframeWindow.ajaxSearch.updateResults === 'function') {
                        logToConsole('✅ updateResults method available', 'info');
                    } else {
                        logToConsole('❌ updateResults method missing', 'error');
                    }
                    
                    if (typeof iframeWindow.ajaxSearch.updateUrl === 'function') {
                        logToConsole('✅ updateUrl method available', 'info');
                    } else {
                        logToConsole('❌ updateUrl method missing', 'error');
                    }
                } else {
                    logToConsole('❌ Desktop AJAX not available', 'error');
                }
                
            } catch (error) {
                logToConsole('❌ Error testing desktop AJAX: ' + error.message, 'error');
            }
        }
        
        async function testAPICall() {
            logToConsole('🌐 Testing API call...', 'info');
            
            try {
                const testUrl = '/thuenhadanang/api/mobile-search-simple.php?type=can-ho&ward=an-hai-bac&price=15%2B&_t=' + Date.now();
                logToConsole('📡 Calling: ' + testUrl, 'info');
                
                const response = await fetch(testUrl);
                logToConsole('📡 Response status: ' + response.status, 'info');
                
                const data = await response.json();
                logToConsole('📡 Response data: ' + JSON.stringify(data, null, 2), 'info');
                
                if (data.success && data.data.metadata && data.data.metadata.url) {
                    logToConsole('✅ API working, URL: ' + data.data.metadata.url, 'mobile');
                } else {
                    logToConsole('❌ API response missing URL', 'error');
                }
                
            } catch (error) {
                logToConsole('❌ API call failed: ' + error.message, 'error');
            }
        }
        
        async function simulateModalApply() {
            logToConsole('📱 Simulating modal apply...', 'mobile');
            
            try {
                const iframe = document.getElementById('searchFrame');
                const iframeWindow = iframe.contentWindow;
                
                if (iframeWindow.mobileSearch) {
                    // Set some test values
                    const typeSelect = iframe.contentDocument.getElementById('mobileFilterType');
                    const wardSelect = iframe.contentDocument.getElementById('mobileFilterWard');
                    const priceSelect = iframe.contentDocument.getElementById('mobileFilterPrice');
                    
                    if (typeSelect) typeSelect.value = 'can-ho';
                    if (wardSelect) wardSelect.value = 'an-hai-bac';
                    if (priceSelect) priceSelect.value = '15+';
                    
                    logToConsole('📱 Set test values in modal', 'mobile');
                    
                    // Call applyFilters
                    iframeWindow.mobileSearch.applyFilters();
                    logToConsole('📱 Called applyFilters()', 'mobile');
                    
                } else {
                    logToConsole('❌ Mobile search not available', 'error');
                }
                
            } catch (error) {
                logToConsole('❌ Error simulating modal apply: ' + error.message, 'error');
            }
        }
        
        async function testFormSubmission() {
            logToConsole('📝 Testing form submission...', 'info');
            
            try {
                const iframe = document.getElementById('searchFrame');
                const form = iframe.contentDocument.getElementById('mobileSearchForm');
                
                if (form) {
                    logToConsole('✅ Mobile search form found', 'info');
                    logToConsole('📝 Form action: ' + (form.action || 'default'), 'info');
                    logToConsole('📝 Form method: ' + (form.method || 'GET'), 'info');
                    
                    // Check hidden fields
                    const hiddenFields = form.querySelectorAll('input[type="hidden"]');
                    logToConsole('📝 Hidden fields count: ' + hiddenFields.length, 'info');
                    
                    hiddenFields.forEach(field => {
                        logToConsole(`📝 ${field.name}: ${field.value}`, 'info');
                    });
                    
                } else {
                    logToConsole('❌ Mobile search form not found', 'error');
                }
                
            } catch (error) {
                logToConsole('❌ Error testing form: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
