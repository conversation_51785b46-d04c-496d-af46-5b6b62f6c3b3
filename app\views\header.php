<?php
// Khởi tạo session nếu chưa có
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
<header class="modern-header bg-white shadow-sm">
    <div class="container">
        <div class="header-content d-flex align-items-center justify-content-between py-3">
            <!-- Left Section: Logo + Navigation -->
            <div class="header-left d-flex align-items-center">
                <!-- Mobile Menu Button -->
                <button class="navbar-toggler d-lg-none me-3" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileMenu">
                    <i class="bi bi-list"></i>
                </button>

                <!-- Logo -->
                <a href="/thuenhadanang" class="navbar-brand d-flex align-items-center me-4">
                    <div class="logo-icon me-2">
                        <i class="bi bi-house-door-fill"></i>
                    </div>
                    <span class="logo-text"><PERSON><PERSON><PERSON> Nẵng</span>
                </a>

                <!-- Desktop Navigation -->
                <nav class="header-nav d-none d-lg-flex">
                    <a href="/thuenhadanang" class="nav-link">Trang chủ</a>
                    <a href="/thuenhadanang/cho-thue-nha-dat" class="nav-link">Cho thuê</a>
                    <a href="#" class="nav-link">Liên hệ</a>
                </nav>
            </div>

            <!-- Right Section: User Actions -->
            <div class="header-right d-flex align-items-center gap-3">
                <!-- Wishlist Button -->
                <button class="btn btn-wishlist" title="Danh sách yêu thích">
                    <i class="bi bi-heart"></i>
                    <span class="wishlist-text d-none d-md-inline">Yêu thích</span>
                    <span class="wishlist-count">0</span>
                </button>

                <?php if (isset($_SESSION['user_email'])): ?>
                    <!-- User Profile Dropdown -->
                    <div class="dropdown">
                        <button class="btn btn-profile dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <?php if (isset($_SESSION['user_avatar']) && $_SESSION['user_avatar'] != 'default-avatar.jpg'): ?>
                                <img src="<?php echo BASE_URL; ?>/public/uploads/users/<?php echo htmlspecialchars($_SESSION['user_avatar']); ?>"
                                     alt="Avatar" class="profile-avatar">
                            <?php else: ?>
                                <div class="profile-avatar-placeholder">
                                    <i class="bi bi-person-fill"></i>
                                </div>
                            <?php endif; ?>
                            <span class="d-none d-md-inline ms-2"><?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/thuenhadanang/dashboard">
                                <i class="bi bi-person me-2"></i>Tài khoản của tôi
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="bi bi-heart me-2"></i>Danh sách yêu thích
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="bi bi-house me-2"></i>Tin đăng của tôi
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="/thuenhadanang/logout">
                                <i class="bi bi-box-arrow-right me-2"></i>Đăng xuất
                            </a></li>
                        </ul>
                    </div>
                <?php else: ?>
                    <!-- Login Button -->
                    <a href="/thuenhadanang/login" class="btn btn-outline-primary">
                        <i class="bi bi-person-circle me-1"></i>
                        Đăng nhập
                    </a>
                <?php endif; ?>

                <!-- Post Property Button -->
                <a href="#" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    <span class="d-none d-md-inline">Đăng tin</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Mobile Menu Offcanvas -->
    <div class="offcanvas offcanvas-start modern-mobile-menu" id="mobileMenu" tabindex="-1">
        <div class="offcanvas-header">
            <div class="d-flex align-items-center">
                <div class="logo-icon me-2">
                    <i class="bi bi-house-door-fill"></i>
                </div>
                <h5 class="offcanvas-title mb-0">Thuê Nhà Đà Nẵng</h5>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <!-- User Section -->
            <?php if (isset($_SESSION['user_email'])): ?>
                <div class="mobile-user-section mb-4">
                    <div class="d-flex align-items-center p-3 bg-light rounded">
                        <?php if (isset($_SESSION['user_avatar']) && $_SESSION['user_avatar'] != 'default-avatar.jpg'): ?>
                            <img src="<?php echo BASE_URL; ?>/public/uploads/users/<?php echo htmlspecialchars($_SESSION['user_avatar']); ?>"
                                 alt="Avatar" class="rounded-circle me-3" width="40" height="40" style="object-fit: cover;">
                        <?php else: ?>
                            <div class="profile-avatar-placeholder me-3" style="width: 40px; height: 40px;">
                                <i class="bi bi-person-fill"></i>
                            </div>
                        <?php endif; ?>
                        <div>
                            <div class="fw-bold"><?php echo htmlspecialchars($_SESSION['user_name']); ?></div>
                            <small class="text-muted">Thành viên</small>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Navigation Menu -->
            <ul class="nav flex-column mobile-nav-menu">
                <li class="nav-item">
                    <a class="nav-link" href="/thuenhadanang">
                        <i class="bi bi-house me-3"></i>Trang chủ
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/thuenhadanang/cho-thue-nha-dat">
                        <i class="bi bi-search me-3"></i>Cho thuê
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="bi bi-heart me-3"></i>Danh sách yêu thích
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="bi bi-telephone me-3"></i>Liên hệ
                    </a>
                </li>

                <?php if (isset($_SESSION['user_email'])): ?>
                    <li class="nav-item mt-3">
                        <div class="nav-section-title">Tài khoản</div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/thuenhadanang/dashboard">
                            <i class="bi bi-person me-3"></i>Tài khoản của tôi
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="bi bi-house me-3"></i>Tin đăng của tôi
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-danger" href="/thuenhadanang/logout">
                            <i class="bi bi-box-arrow-right me-3"></i>Đăng xuất
                        </a>
                    </li>
                <?php else: ?>
                    <li class="nav-item mt-3">
                        <a class="nav-link btn btn-primary text-white text-center" href="/thuenhadanang/login">
                            <i class="bi bi-person-circle me-2"></i>Đăng nhập
                        </a>
                    </li>
                <?php endif; ?>
            </ul>

            <!-- Post Property Button for Mobile -->
            <div class="mt-4">
                <a href="#" class="btn btn-primary w-100">
                    <i class="bi bi-plus-circle me-2"></i>Đăng tin cho thuê
                </a>
            </div>
        </div>
    </div>
</header>
