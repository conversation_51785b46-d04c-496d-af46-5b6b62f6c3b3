<?php

// Test file for <PERSON><PERSON><PERSON><PERSON><PERSON>
define('BASE_PATH', __DIR__);
define('APP_PATH', BASE_PATH . '/app');

// Load required files
require_once APP_PATH . '/libraries/Database.php';
require_once APP_PATH . '/libraries/UrlHandler.php';
require_once APP_PATH . '/models/PropertyType.php';
require_once APP_PATH . '/models/Ward.php';

echo "<h1>UrlHandler Test</h1>\n";

try {
    $urlHandler = new UrlHandler();
    
    // Test cases for URL parsing
    $testUrls = [
        'cho-thue-can-ho',
        'cho-thue-can-ho-tai-hai-chau-1',
        'cho-thue-can-ho-tai-hai-chau-1/gia-5-7-trieu',
        'cho-thue-can-ho-tai-hai-chau-1/gia-5-7-trieu-dt-50-70m2-2pn',
        'cho-thue-nha-dat',
        'cho-thue-nha-dat-tai-hai-chau-1',
        'cho-thue-nha-dat/gia-tren-10-trieu',
        'cho-thue-can-ho/gia-3-5-trieu-dt-30-50m2-1pn'
    ];
    
    echo "<h2>URL Parsing Tests</h2>\n";
    foreach ($testUrls as $url) {
        echo "<h3>Testing URL: $url</h3>\n";
        $result = $urlHandler->parseUrl($url);
        echo "<pre>" . print_r($result, true) . "</pre>\n";
    }
    
    // Test cases for URL building
    $testParams = [
        [
            'type' => 'can-ho',
            'ward' => 'hai-chau-1',
            'price' => '5-7',
            'area' => '50-70',
            'bedrooms' => '2'
        ],
        [
            'type' => 'nha-rieng',
            'price' => '10+',
            'bedrooms' => '3'
        ],
        [
            'ward' => 'hai-chau-1',
            'price' => '3-5'
        ],
        [
            'type' => '',
            'ward' => 'hai-chau-1',
            'area' => '90+',
            'bedrooms' => '4+'
        ]
    ];
    
    echo "<h2>URL Building Tests</h2>\n";
    foreach ($testParams as $i => $params) {
        echo "<h3>Test Case " . ($i + 1) . "</h3>\n";
        echo "<strong>Input params:</strong><br>\n";
        echo "<pre>" . print_r($params, true) . "</pre>\n";
        
        $url = $urlHandler->buildUrl($params);
        echo "<strong>Generated URL:</strong> $url<br>\n";
        
        // Test round-trip: parse the generated URL
        $parsedBack = $urlHandler->parseUrl(str_replace('/thuenhadanang/', '', $url));
        echo "<strong>Parsed back:</strong><br>\n";
        echo "<pre>" . print_r($parsedBack, true) . "</pre>\n";
        echo "<hr>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

?>
