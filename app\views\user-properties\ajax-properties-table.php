<?php if (empty($data['properties'])): ?>
    <div class="text-center py-5 empty-state">
        <div class="mb-3">
            <i class="bi bi-house-slash text-muted" style="font-size: 3rem;"></i>
        </div>
        <h5 class="text-muted">Không có tin đăng nào</h5>
        <p class="text-muted mb-4">
            <?php if ($data['status'] == 'all'): ?>
                Bạn chưa có tin đăng nào. Hãy đăng tin ngay để tiếp cận hàng ngàn người thuê tiềm năng.
            <?php elseif ($data['status'] == 'active'): ?>
                Bạn chưa có tin đăng nào đang hoạt động.
            <?php elseif ($data['status'] == 'pending'): ?>
                Bạn không có tin đăng nào đang chờ duyệt.
            <?php elseif ($data['status'] == 'expired'): ?>
                Bạn không có tin đăng nào đã hết hạn.
            <?php elseif ($data['status'] == 'hidden'): ?>
                Bạn không có tin đăng nào đang bị ẩn.
            <?php endif; ?>
        </p>
        <a href="/thuenhadanang/property-listing" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i> Đăng tin mới
        </a>
    </div>
<?php else: ?>
    <div class="property-list">
        <?php foreach ($data['properties'] as $property): ?>
            <?php
            // Kiểm tra ngày hết hạn
            $isExpired = false;
            if ($property->expiration_date && strtotime($property->expiration_date) < time()) {
                $isExpired = true;
            }

            // Xác định trạng thái hiển thị
            $statusClass = '';
            $statusText = '';

            if ($isExpired) {
                $statusClass = 'bg-danger';
                $statusText = 'Hết hạn';
            } elseif ($property->status === 'display' && $property->active == 1) {
                $statusClass = 'bg-success';
                $statusText = 'Đang hoạt động';
            } elseif ($property->active == 0) {
                $statusClass = 'bg-warning';
                $statusText = 'Chờ duyệt';
            } elseif ($property->active == 2) {
                $statusClass = 'bg-danger';
                $statusText = 'Bị từ chối';
            } elseif ($property->status === 'hide') {
                $statusClass = 'bg-secondary';
                $statusText = 'Đã ẩn';
            }

            // Tính số ngày còn lại
            $daysLeft = 0;
            $expirationDateFormatted = 'Không giới hạn';
            if ($property->expiration_date) {
                $expirationDate = strtotime($property->expiration_date);
                $now = time();
                $daysLeft = ceil(($expirationDate - $now) / (60 * 60 * 24));
                $expirationDateFormatted = date('d/m/Y', $expirationDate);
            }

            // Định dạng giá
            $priceFormatted = number_format($property->price) . ' đ';
            $periods = [
                'day' => 'ngày',
                'week' => 'tuần',
                'month' => 'tháng',
                'quarter' => 'quý',
                'year' => 'năm'
            ];
            if (isset($periods[$property->price_period])) {
                $priceFormatted .= '/' . $periods[$property->price_period];
            }
            ?>

            <div class="card mb-3 border-0 shadow-sm">
                <div class="row g-0">
                    <!-- Cột bên trái: Hình ảnh -->
                    <div class="col-md-3">
                        <div class="property-image h-100">
                            <?php if (!empty($property->main_image)): ?>
                                <img src="/thuenhadanang/public/uploads/properties/<?php echo htmlspecialchars($property->main_image); ?>"
                                     class="img-fluid rounded-start h-100 w-100"
                                     style="object-fit: cover; max-height: 180px;">
                            <?php else: ?>
                                <div class="bg-light rounded-start d-flex align-items-center justify-content-center h-100">
                                    <i class="bi bi-house text-muted" style="font-size: 3rem;"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Cột giữa: Thông tin tin đăng -->
                    <div class="col-md-6">
                        <div class="card-body">
                            <h5 class="card-title mb-2"><?php echo htmlspecialchars($property->title); ?></h5>

                            <div class="mb-2">
                                <!-- Status badge -->
                                <div class="mb-1">
                                    <span class="badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                </div>
                                <!-- Property type badge -->
                                <div>
                                    <span class="badge bg-info"><?php echo htmlspecialchars($property->type_name); ?></span>
                                </div>
                            </div>

                            <div class="row mb-2">
                                <div class="col-md-12 mb-2">
                                    <small class="text-muted">
                                        <i class="bi bi-calendar-check me-1"></i> Ngày đăng: <?php echo date('d/m/Y', strtotime($property->created_at)); ?>
                                    </small>
                                </div>
                                <?php if ($property->active != 0): // Hide expiration date for pending properties ?>
                                <div class="col-md-12 mb-2">
                                    <small class="<?php echo $isExpired ? 'text-danger' : 'text-success'; ?>">
                                        <i class="bi bi-calendar-x me-1"></i> Hết hạn: <?php echo $expirationDateFormatted; ?>
                                        <?php if ($property->expiration_date): ?>
                                            <?php if ($daysLeft > 0): ?>
                                                <span class="text-success">(Còn <?php echo $daysLeft; ?> ngày)</span>
                                            <?php else: ?>
                                                <span class="text-danger">(Đã hết hạn)</span>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <p class="card-text mb-0">
                                        <strong><i class="bi bi-wallet" style="padding-right: 5px;"></i> <?php echo $priceFormatted; ?></strong>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <?php if (!empty($property->area)): ?>
                                    <p class="card-text mb-0">
                                        <i class="bi bi-arrows-angle-expand me-1"></i> <?php echo $property->area; ?> m²
                                    </p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cột bên phải: Các nút hành động -->
                    <div class="col-md-3">
                        <div class="card-body d-flex flex-column justify-content-center h-100">
                            <div class="d-grid gap-2">
                                <a href="/thuenhadanang/<?php echo $property->slug; ?>-<?php echo $property->id; ?>/"
                                   class="btn btn-outline-primary btn-sm"
                                   target="_blank">
                                    <i class="bi bi-eye me-1"></i> Xem chi tiết
                                </a>

                                <a href="/thuenhadanang/property/edit/<?php echo $property->id; ?>"
                                   class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-pencil me-1"></i> Chỉnh sửa
                                </a>

                                <?php if ($isExpired && $property->extension_status !== 'pending'): ?>
                                <button type="button"
                                        class="btn btn-outline-success btn-sm extend-property-btn"
                                        data-property-id="<?php echo $property->id; ?>"
                                        data-property-title="<?php echo htmlspecialchars($property->title); ?>">
                                    <i class="bi bi-arrow-clockwise me-1"></i> Gia hạn
                                </button>
                                <?php elseif ($property->extension_status === 'pending'): ?>
                                <span class="badge bg-warning text-dark">
                                    <i class="bi bi-clock me-1"></i> Chờ duyệt gia hạn
                                </span>
                                <?php endif; ?>
                                <button type="button"
                                        class="btn btn-outline-danger btn-sm"
                                        data-bs-toggle="modal"
                                        data-bs-target="#deleteModal<?php echo $property->id; ?>">
                                    <i class="bi bi-trash me-1"></i> Xóa
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Delete Modal -->
            <div class="modal fade" id="deleteModal<?php echo $property->id; ?>" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Xác nhận xóa</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <strong>Cảnh báo: Hành động này không thể hoàn tác!</strong>
                            </div>
                            <p>Bạn có chắc chắn muốn xóa tin đăng này?</p>
                            <p>Việc xóa sẽ:</p>
                            <ul>
                                <li>Xóa <strong>vĩnh viễn</strong> tất cả dữ liệu của tin đăng</li>
                                <li>Xóa <strong>tất cả hình ảnh</strong> đã đăng</li>
                                <li>Không thể khôi phục lại sau khi xóa</li>
                            </ul>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                            <a href="/thuenhadanang/property/delete/<?php echo $property->id; ?>" class="btn btn-danger">Xóa</a>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <style>
    /* CSS cho danh sách tin đăng */
    .property-list .card {
        transition: all 0.2s ease;
    }

    /* Đã tắt hiệu ứng hover box-shadow
    .property-list .card:hover {
        box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
    } */

    .property-image {
        min-height: 180px;
        background-color: #f8f9fa;
    }

    /* Ensure consistent height for empty state */
    .empty-state {
        min-height: 300px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    @media (max-width: 767.98px) {
        .property-image {
            min-height: 200px;
            max-height: 200px;
        }

        .empty-state {
            min-height: 400px;
        }
    }
    </style>
<?php endif; ?>
