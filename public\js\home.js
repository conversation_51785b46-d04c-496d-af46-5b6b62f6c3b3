// Hàm khởi tạo trang chủ
function initializeHomePage() {
    console.log('Initializing home page...');

    // Khởi tạo Select2
    if ($.fn.select2) {
        $('.select2-ward').select2({
            placeholder: "Chọn phường/xã",
            allowClear: true,
            width: '100%',
            language: {
                noResults: function() {
                    return "Không tìm thấy kết quả";
                }
            }
        });
    }

    // Ngăn dropdown đóng khi click vào nội dung bên trong
    $('.more-filters-dropdown').on('click', function(e) {
        e.stopPropagation();
    });

    // Xử lý khi click vào nút tìm kiếm trên desktop
    $('#desktopSearchBtn').on('click', function() {
        handleSearch('desktop');
    });

    // Xử lý khi click vào nút tìm kiếm trên mobile
    $('#mobileSearchBtn').on('click', function() {
        handleSearch('mobile');
    });

    // Xử lý khi submit form tìm kiếm trên desktop
    $('#desktopSearchForm').on('submit', function(e) {
        e.preventDefault(); // Ngăn chặn hành vi mặc định của form
        console.log('Desktop search form submitted');
        handleSearch('desktop');
    });

    // Xử lý khi submit form tìm kiếm trên mobile
    $('#mobileSearchForm').on('submit', function(e) {
        e.preventDefault(); // Ngăn chặn hành vi mặc định của form
        console.log('Mobile search form submitted');
        handleSearch('mobile');
    });

    // Tải dữ liệu cho tab đầu tiên (featured)
    loadProperties('featured', 'featured-properties');

    // Tải trước dữ liệu cho tất cả các tab để tránh hiệu ứng giật
    const tabTypes = [
        { id: 'featured', type: 'featured' },
        { id: 'apartment', type: 'can-ho' },
        { id: 'room', type: 'nha-tro-phong-tro' }
    ];

    tabTypes.forEach((tab, index) => {
        const targetId = tab.id + '-properties';

        // Nếu không phải tab đầu tiên, thêm loading placeholder
        if (tab.id !== 'featured') {
            $('#' + targetId).html(`
                <div class="col-12 text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                    <p class="mt-2">Đang tải dữ liệu...</p>
                </div>
            `);

            // Tải dữ liệu ngay khi trang được tải
            setTimeout(() => {
                loadProperties(tab.type, targetId);
            }, 500 * index); // Tải lần lượt để không ảnh hưởng hiệu suất
        }
    });

    // Xử lý sự kiện khi chuyển tab
    $('#propertyTabs .nav-link').on('click', function() {
        const type = $(this).data('type');
        const targetId = $(this).attr('aria-controls').replace('-tab-pane', '-properties');

        // Nếu chưa có dữ liệu, tải dữ liệu
        if ($('#' + targetId).children().length === 0) {
            loadProperties(type, targetId);
        }
        // Không cần hiệu ứng loading nữa
    });
}

// Hàm tải dữ liệu bất động sản
function loadProperties(type, targetElementId) {
    console.log('Loading properties for type:', type, 'target:', targetElementId);

    $.ajax({
        url: '/thuenhadanang/api/properties',
        type: 'GET',
        data: {
            type: type,
            limit: 12
        },
        dataType: 'json',
        success: function(response) {
            console.log('API response:', response);
            if (response.success) {
                renderProperties(response.properties, targetElementId);
            } else {
                showError(targetElementId);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', status, error);
            showError(targetElementId);
        }
    });
}

// Hàm hiển thị dữ liệu bất động sản
function renderProperties(properties, targetElementId) {
    console.log('Rendering', properties.length, 'properties to', targetElementId);

    const container = $('#' + targetElementId);
    container.empty();

    if (properties.length === 0) {
        container.html('<div class="col-12 text-center py-5"><p>Không có bất động sản nào.</p></div>');
        return;
    }

    // Sắp xếp bất động sản để ưu tiên hiển thị các bất động sản nổi bật trước
    properties.sort((a, b) => {
        // Nếu tab là featured, không cần sắp xếp vì tất cả đều là featured
        if (targetElementId === 'featured-properties') {
            return 0;
        }
        // Ưu tiên bất động sản nổi bật
        return b.featured - a.featured;
    });

    // Lấy template
    const template = document.getElementById('property-card-template');

    // Tạo các card bất động sản
    properties.forEach(function(property) {
        const clone = document.importNode(template.content, true);
        const card = clone.querySelector('.card');
        const featuredBadge = clone.querySelector('.property-badge.featured');

        // Hiển thị badge "Nổi bật" nếu là bất động sản nổi bật
        if (property.featured == 1) {
            featuredBadge.style.display = 'block';
        }

        // Cập nhật thông tin
        const propertyLinks = clone.querySelectorAll('.property-link');
        propertyLinks.forEach(link => {
            link.href = property.url;
        });

        clone.querySelector('.property-image').src = property.image;
        clone.querySelector('.property-image').alt = property.title;
        clone.querySelector('.property-title').textContent = property.title;
        clone.querySelector('.property-ward').textContent = property.ward || 'Đà Nẵng';

        if (property.area) {
            clone.querySelector('.property-area').textContent = property.area + 'm²';
        } else {
            clone.querySelector('.property-area').parentNode.style.display = 'none';
        }

        if (property.bedrooms) {
            clone.querySelector('.property-bedrooms').textContent = property.bedrooms + ' PN';
        } else {
            clone.querySelector('.property-bedrooms').parentNode.style.display = 'none';
        }

        if (property.bathrooms) {
            clone.querySelector('.property-bathrooms').textContent = property.bathrooms + ' WC';
        } else {
            clone.querySelector('.property-bathrooms').parentNode.style.display = 'none';
        }

        // Định dạng giá theo yêu cầu (triệu/tháng)
        const priceInMillions = property.price / 1000000;
        let formattedPrice;

        if (priceInMillions >= 1) {
            // Nếu giá >= 1 triệu, hiển thị theo định dạng triệu
            formattedPrice = priceInMillions % 1 === 0
                ? priceInMillions + ' triệu'
                : priceInMillions.toFixed(1) + ' triệu';
        } else {
            // Nếu giá < 1 triệu, hiển thị theo định dạng nghìn
            formattedPrice = property.price.toLocaleString('vi-VN');
        }

        // Thêm đơn vị thời gian
        let timePeriod = '';
        switch (property.price_period) {
            case 'day':
                timePeriod = '/ngày';
                break;
            case 'week':
                timePeriod = '/tuần';
                break;
            case 'month':
                timePeriod = '/tháng';
                break;
            case 'quarter':
                timePeriod = '/quý';
                break;
            case 'year':
                timePeriod = '/năm';
                break;
        }

        clone.querySelector('.property-price').textContent = formattedPrice + timePeriod;

        // Thêm vào container
        container.append(clone);
    });
}

// Hàm hiển thị lỗi
function showError(targetElementId) {
    console.error('Error loading properties for', targetElementId);

    $('#' + targetElementId).html(`
        <div class="col-12 text-center py-5">
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                Có lỗi xảy ra khi tải dữ liệu. Vui lòng thử lại sau.
            </div>
            <button class="btn btn-primary mt-3 reload-btn" data-target="${targetElementId}">
                <i class="bi bi-arrow-clockwise me-2"></i>Tải lại
            </button>
        </div>
    `);

    // Xử lý sự kiện nút tải lại
    $('.reload-btn').on('click', function() {
        const targetId = $(this).data('target');
        const type = targetId.replace('-properties', '');
        loadProperties(type, targetId);
    });
}

// Hàm xử lý tìm kiếm với URL thân thiện
function handleSearch(formType) {
    let type, ward, price, keyword;

    // Lấy giá trị từ form tương ứng
    if (formType === 'desktop') {
        type = $('#desktopType').val();
        ward = $('#desktopWard').val();
        price = $('#desktopPrice').val();
        keyword = $('#desktopSearchForm .search-address').val();
    } else if (formType === 'mobile') {
        type = $('#mobileType').val();
        ward = $('#mobileWard').val();
        price = $('#mobilePrice').val();
        keyword = $('#mobileKeyword').val();
    }

    // Ghi log các tham số tìm kiếm
    console.log('Search parameters:', { type, ward, price, keyword });

    // Xây dựng URL thân thiện
    let url = '/thuenhadanang';
    let queryParams = {};

    // Chuẩn bị từ khóa tìm kiếm (loại bỏ khoảng trắng thừa)
    if (keyword && keyword.trim() !== '') {
        // Lưu từ khóa đã được trim vào queryParams
        // $.param() sẽ tự động mã hóa URL các ký tự đặc biệt và tiếng Việt có dấu
        queryParams.keyword = keyword.trim();
        console.log('Keyword for search (original):', keyword.trim());
        console.log('Keyword for search (encoded):', encodeURIComponent(keyword.trim()));
    }

    // Xây dựng URL cơ bản dựa trên các tham số type, ward, price
    let baseUrl = '';

    // Trường hợp có cả type, ward, price
    if (type && ward && price) {
        baseUrl = '/cho-thue-' + type + '-tai-' + ward + '/gia-' + formatPriceForUrl(price);
        console.log('URL pattern: type + ward + price');
    }
    // Trường hợp có type và ward
    else if (type && ward && !price) {
        baseUrl = '/cho-thue-' + type + '-tai-' + ward;
        console.log('URL pattern: type + ward');
    }
    // Trường hợp có type và price
    else if (type && !ward && price) {
        baseUrl = '/cho-thue-' + type + '/gia-' + formatPriceForUrl(price);
        console.log('URL pattern: type + price');
    }
    // Trường hợp có ward và price
    else if (!type && ward && price) {
        baseUrl = '/cho-thue-nha-dat-tai-' + ward + '/gia-' + formatPriceForUrl(price);
        console.log('URL pattern: ward + price');
    }
    // Trường hợp chỉ có type
    else if (type && !ward && !price) {
        baseUrl = '/cho-thue-' + type;
        console.log('URL pattern: type only');
    }
    // Trường hợp chỉ có ward
    else if (!type && ward && !price) {
        baseUrl = '/cho-thue-nha-dat-tai-' + ward;
        console.log('URL pattern: ward only');
    }
    // Trường hợp chỉ có price
    else if (!type && !ward && price) {
        baseUrl = '/cho-thue-nha-dat/gia-' + formatPriceForUrl(price);
        console.log('URL pattern: price only');
    }
    // Trường hợp không có tham số nào hoặc chỉ có keyword
    else {
        baseUrl = '/cho-thue-nha-dat';
        console.log('URL pattern: default or keyword only');
    }

    // Tạo URL đầy đủ
    url += baseUrl;

    // Thêm query params (bao gồm keyword) nếu có
    if (Object.keys(queryParams).length > 0) {
        url += '?' + $.param(queryParams);
        console.log('Added query parameters:', queryParams);
    }

    console.log('Final search URL:', url);

    // Chuyển hướng đến URL mới
    window.location.href = url;
}

// Hàm định dạng giá cho URL
function formatPriceForUrl(price) {
    switch (price) {
        case '1-3':
            return '1-3-trieu';
        case '3-5':
            return '3-5-trieu';
        case '5-7':
            return '5-7-trieu';
        case '7-10':
            return '7-10-trieu';
        case '10-15':
            return '10-15-trieu';
        case '15+':
            return 'tren-15-trieu';
        default:
            return price;
    }
}

// Đảm bảo mã chỉ chạy sau khi trang đã tải hoàn toàn
$(document).ready(function() {
    console.log('Document ready, initializing home page...');
    initializeHomePage();
});
