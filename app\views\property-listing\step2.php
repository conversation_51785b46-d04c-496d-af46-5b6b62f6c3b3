<!-- Include custom CSS for property listing -->
<link rel="stylesheet" href="/thuenhadanang/public/css/property-listing.css">

<!-- Include Dropzone.js -->
<link rel="stylesheet" href="https://unpkg.com/dropzone@5/dist/min/dropzone.min.css" type="text/css" />
<script src="https://unpkg.com/dropzone@5/dist/min/dropzone.min.js"></script>

<!-- Include Compressor.js for image optimization -->
<script src="https://unpkg.com/compressorjs/dist/compressor.min.js"></script>

<!-- Include Sortable.js for drag and drop functionality -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<!-- Custom CSS for image upload and drag-drop functionality -->
<style>
    .dropzone {
        border: 2px dashed #0d6efd;
        border-radius: 5px;
        background: #f8f9fa;
        min-height: 150px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .dropzone:hover {
        background: #e9ecef;
    }

    .dropzone .dz-message {
        text-align: center;
    }

    .dz-processing-indicator {
        margin-top: 10px;
        padding: 8px 12px;
        background: rgba(13, 110, 253, 0.1);
        border-radius: 4px;
        display: flex;
        align-items: center;
        font-size: 0.875rem;
    }

    .bg-success-light {
        background-color: rgba(25, 135, 84, 0.15);
    }

    .bg-danger-light {
        background-color: rgba(220, 53, 69, 0.15);
    }

    /* CSS cho nút xóa hình ảnh */
    .remove-image {
        padding: 0.25rem 0.5rem;
        min-width: 32px;
        min-height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        opacity: 0.9;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .remove-image:hover {
        opacity: 1;
        transform: scale(1.05);
    }

    /* CSS cho chức năng kéo thả */
    .image-preview-item {
        cursor: grab;
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .image-preview-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .image-preview-item.sortable-ghost {
        opacity: 0.5;
    }

    .image-preview-item.sortable-chosen {
        background-color: #f8f9fa;
        box-shadow: 0 0 15px rgba(13, 110, 253, 0.3);
        transform: scale(1.02);
    }

    .image-preview-item.sortable-drag {
        opacity: 0.8;
        transform: rotate(2deg) scale(1.05);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }

    .image-preview-item .card {
        border: 2px solid transparent;
        transition: border-color 0.2s;
    }

    .image-preview-item:hover .card {
        border-color: #0d6efd;
    }
</style>

<!-- Disable Dropzone auto-discover to prevent double initialization -->
<script>
    // Disable Dropzone auto-discover before any Dropzone elements are rendered
    Dropzone.autoDiscover = false;
</script>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="container mt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/thuenhadanang" class="text-decoration-none">Trang chủ</a></li>
        <li class="breadcrumb-item"><a href="/thuenhadanang/dashboard" class="text-decoration-none">Bảng điều khiển</a></li>
        <li class="breadcrumb-item active" aria-current="page">Đăng tin bất động sản</li>
    </ol>
</nav>

<div class="container py-4">
    <div class="row">
        <!-- Include Sidebar -->
        <?php require_once 'app/views/partials/dashboard-sidebar.php'; ?>

        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card border-0 shadow-sm property-listing-form">
                <div class="card-header bg-white py-3 border-0">
                    <h5 class="mb-0">Đăng tin bất động sản</h5>
                </div>
                <div class="card-body p-4">
                    <!-- Progress Bar -->
                    <div class="step-progress">
                        <div class="progress-bar" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="step-indicators">
                        <span class="step-indicator">Bước 1: Thông tin bất động sản</span>
                        <span class="step-indicator active">Bước 2: Hình ảnh</span>
                    </div>

                    <?php if (!empty($data['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $data['error']; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="form-group mb-4">
                        <h6 class="form-label fw-bold mb-3">Tải lên hình ảnh bất động sản</h6>
                        <div class="alert alert-info bg-light-info border-info">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-info-circle-fill text-primary me-2"></i>
                                <strong>Yêu cầu hình ảnh:</strong>
                            </div>
                            <ul class="mb-0 ps-4">
                                <li>Tối đa 10 hình ảnh</li>
                                <li>Kích thước tối thiểu 300x300 pixels</li>
                                <li>Định dạng: JPG, JPEG, PNG</li>
                                <li>Hình ảnh sẽ được tự động tối ưu hóa (nén và thay đổi kích thước)</li>
                                <li><strong>Mới:</strong> Bạn có thể kéo và thả để sắp xếp lại thứ tự hình ảnh</li>
                            </ul>
                        </div>

                        <!-- Dropzone Upload Area -->
                        <div class="dropzone-container mb-4">
                            <form action="/thuenhadanang/property-listing/upload-image" class="dropzone" id="property-images-upload">
                                <div class="fallback">
                                    <input name="file" type="file" multiple />
                                </div>
                                <div class="dz-message">
                                    <i class="bi bi-cloud-arrow-up fs-2 text-primary mb-2"></i>
                                    <p>Kéo và thả hình ảnh vào đây hoặc nhấp để chọn</p>
                                    <span class="text-muted small">Hình ảnh sẽ được tự động tối ưu hóa</span>
                                </div>
                            </form>
                        </div>

                        <!-- Preview Area -->
                        <div class="image-preview-container mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="form-label fw-bold mb-0">Hình ảnh đã tải lên</h6>
                                <div class="drag-hint small text-muted d-none" id="drag-hint">
                                    <i class="bi bi-arrows-move me-1"></i> Kéo để sắp xếp lại thứ tự
                                </div>
                            </div>
                            <div class="row g-3" id="image-preview-row">
                                <div class="col-12 text-center text-muted py-4 border rounded bg-light" id="no-images-message">
                                    <i class="bi bi-images d-block mb-2 fs-3"></i>
                                    Chưa có hình ảnh nào được tải lên
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="/thuenhadanang/property-listing" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-1"></i> Quay lại
                            </a>
                            <button type="button" id="submit-property" class="btn btn-primary">
                                Hoàn tất đăng tin <i class="bi bi-check-circle ms-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Template for image preview -->
<template id="image-preview-template">
    <div class="col-md-3 col-sm-6 image-preview-item">
        <div class="card h-100">
            <div class="position-relative">
                <img src="" class="card-img-top preview-image" style="height: 150px; object-fit: cover;">
                <button type="button" class="btn btn-sm btn-danger position-absolute top-0 end-0 m-2 remove-image"
                        data-filename="" title="Xóa ảnh">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
            <div class="card-body p-2">
                <div class="form-check">
                    <input class="form-check-input main-image-radio" type="radio" name="main_image" value="">
                    <label class="form-check-label">Đặt làm ảnh chính</label>
                </div>
            </div>
        </div>
    </div>
</template>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-5">
                <div class="spinner-border text-primary mb-4" style="width: 3rem; height: 3rem;" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5 class="mb-2">Đang xử lý...</h5>
                <p class="text-muted mb-0">Vui lòng đợi trong giây lát</p>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-5">
                <div class="mb-4">
                    <div class="d-inline-flex justify-content-center align-items-center bg-success-light rounded-circle p-3 mb-2">
                        <i class="bi bi-check-circle-fill text-success fs-1"></i>
                    </div>
                </div>
                <h4 class="mb-3">Đăng tin thành công!</h4>
                <p class="text-muted mb-4">Tin đăng của bạn đã được gửi và đang chờ phê duyệt.</p>
                <div class="d-grid gap-2">
                    <a href="/thuenhadanang/dashboard/properties" class="btn btn-primary py-2">
                        <i class="bi bi-list-ul me-2"></i> Xem danh sách tin đăng
                    </a>
                    <a href="/thuenhadanang/dashboard" class="btn btn-outline-secondary py-2">
                        <i class="bi bi-house me-2"></i> Về trang chủ
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Error Modal -->
<div class="modal fade" id="errorModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-5">
                <div class="mb-4">
                    <div class="d-inline-flex justify-content-center align-items-center bg-danger-light rounded-circle p-3 mb-2">
                        <i class="bi bi-exclamation-triangle-fill text-danger fs-1"></i>
                    </div>
                </div>
                <h4 class="mb-3">Đã xảy ra lỗi!</h4>
                <p class="text-muted mb-4" id="error-message">Có lỗi xảy ra khi đăng tin. Vui lòng thử lại sau.</p>
                <div class="d-grid">
                    <button type="button" class="btn btn-primary py-2" data-bs-dismiss="modal">
                        <i class="bi bi-arrow-clockwise me-2"></i> Thử lại
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for image upload and property submission -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    let uploadedImages = [];
    const maxImages = 10;
    let sortable; // Biến để lưu trữ instance của Sortable

    // Initialize Dropzone (autoDiscover is already disabled at the top of the page)
    const myDropzone = new Dropzone("#property-images-upload", {
        url: "/thuenhadanang/property-listing/upload-image",
        paramName: "file",
        maxFilesize: 10, // Tăng giới hạn lên 10MB để cho phép tải lên file lớn hơn trước khi nén
        maxFiles: maxImages,
        acceptedFiles: "image/jpeg,image/jpg,image/png",
        addRemoveLinks: false,
        autoProcessQueue: false,
        dictDefaultMessage: "Kéo và thả hình ảnh vào đây hoặc nhấp để chọn",
        dictFileTooBig: "File quá lớn ({{filesize}}MB). Kích thước tối đa: {{maxFilesize}}MB",
        dictInvalidFileType: "Định dạng file không hợp lệ. Chỉ chấp nhận JPG, JPEG, PNG",
        dictMaxFilesExceeded: "Không thể tải lên thêm hình ảnh (tối đa {{maxFiles}} hình)",
        init: function() {
            this.on("addedfile", function(file) {
                // Check if we already have max images
                if (uploadedImages.length >= maxImages) {
                    this.removeFile(file);
                    showToast('warning', `Bạn chỉ có thể tải lên tối đa ${maxImages} hình ảnh.`);
                    return;
                }

                // Add loading indicator
                const loadingIndicator = document.createElement('div');
                loadingIndicator.className = 'dz-processing-indicator';
                loadingIndicator.innerHTML = `
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2">Đang xử lý...</span>
                `;
                file.previewElement.appendChild(loadingIndicator);

                // Hiển thị thông tin về file gốc
                console.log('Original file:', file.name, 'Size:', (file.size / 1024 / 1024).toFixed(2) + 'MB');

                // Cập nhật loading indicator để hiển thị thông tin về quá trình nén
                // Sử dụng biến loadingIndicator đã được khai báo ở trên
                if (loadingIndicator) {
                    loadingIndicator.innerHTML = `
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-2">Đang tối ưu hóa hình ảnh...</span>
                    `;
                }

                // Compress image before upload với cấu hình tối ưu
                new Compressor(file, {
                    quality: 0.8, // 80% quality - cân bằng giữa chất lượng và kích thước
                    maxWidth: 1000, // Giới hạn chiều rộng tối đa
                    maxHeight: 1000, // Thêm giới hạn chiều cao tối đa
                    convertSize: 1000000, // Chuyển đổi sang WebP nếu kích thước > 1MB và trình duyệt hỗ trợ
                    convertTypes: ['image/png', 'image/jpeg'], // Chỉ chuyển đổi PNG và JPEG
                    success: (compressedFile) => {
                        // Hiển thị thông tin về file đã nén
                        console.log('Compressed file size:', (compressedFile.size / 1024 / 1024).toFixed(2) + 'MB',
                                    'Compression ratio:', ((1 - compressedFile.size / file.size) * 100).toFixed(0) + '%');

                        // Cập nhật loading indicator
                        if (loadingIndicator) {
                            loadingIndicator.innerHTML = `
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Đang tải lên (đã giảm ${((1 - compressedFile.size / file.size) * 100).toFixed(0)}%)...</span>
                            `;
                        }

                        // Create a new file with the compressed data
                        const formData = new FormData();
                        formData.append('file', compressedFile, file.name);

                        // Upload the compressed file
                        fetch('/thuenhadanang/property-listing/upload-image', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Add to uploaded images array
                                uploadedImages.push(data.filename);

                                // Add to preview
                                addImagePreview(data.filename);

                                // Remove from dropzone
                                this.removeFile(file);

                                // Hiển thị thông tin về file đã tải lên
                                console.log('Upload successful:', data.filename);

                                // Show success message với thông tin về tối ưu hóa
                                const compressionRatio = ((1 - compressedFile.size / file.size) * 100).toFixed(0);
                                const originalSize = (file.size / 1024 / 1024).toFixed(2);
                                const compressedSize = (compressedFile.size / 1024 / 1024).toFixed(2);

                                showToast(
                                    'success',
                                    `Tải lên thành công! Đã tối ưu hóa từ ${originalSize}MB xuống ${compressedSize}MB (giảm ${compressionRatio}%)`
                                );
                            } else {
                                console.error('Upload error:', data.error);
                                showToast('error', 'Lỗi: ' + data.error);
                                this.removeFile(file);
                            }
                        })
                        .catch(error => {
                            console.error('Upload error:', error);
                            showToast('error', 'Có lỗi xảy ra khi tải lên hình ảnh. Vui lòng thử lại.');
                            this.removeFile(file);
                        });
                    },
                    error: (err) => {
                        console.error('Compression error:', err);
                        showToast('error', 'Có lỗi xảy ra khi tối ưu hóa hình ảnh. Vui lòng thử lại với hình ảnh khác.');
                        this.removeFile(file);
                    },
                    mimeType: 'image/jpeg' // Luôn chuyển đổi sang JPEG để đảm bảo tính tương thích
                });
            });
        }
    });

    // Function to initialize Sortable for drag and drop functionality
    function initSortable() {
        // Destroy existing instance if it exists
        if (sortable) {
            sortable.destroy();
        }

        // Initialize Sortable on the image preview row
        const imagePreviewRow = document.getElementById('image-preview-row');
        sortable = new Sortable(imagePreviewRow, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            handle: '.card', // Chỉ cho phép kéo thả khi click vào card
            onEnd: function(evt) {
                // Cập nhật mảng uploadedImages sau khi kéo thả
                updateImagesOrder();

                // Hiển thị thông báo
                showToast('success', 'Đã cập nhật thứ tự hình ảnh');
            }
        });
    }

    // Function to update images order after drag and drop
    function updateImagesOrder() {
        const newOrder = [];
        const previewItems = document.querySelectorAll('.image-preview-item');

        previewItems.forEach(item => {
            const removeBtn = item.querySelector('.remove-image');
            if (removeBtn && removeBtn.dataset.filename) {
                newOrder.push(removeBtn.dataset.filename);
            }
        });

        // Cập nhật mảng uploadedImages với thứ tự mới
        if (newOrder.length === uploadedImages.length) {
            uploadedImages = newOrder;
            console.log('Updated image order:', uploadedImages);
        }
    }

    // Function to add image preview
    function addImagePreview(filename) {
        // Hide no images message
        document.getElementById('no-images-message').style.display = 'none';

        // Clone template
        const template = document.getElementById('image-preview-template');
        const clone = document.importNode(template.content, true);

        // Set image source
        const imageElement = clone.querySelector('.preview-image');
        imageElement.src = `/thuenhadanang/public/uploads/temp/${filename}`;

        // Add loading effect and fade-in
        imageElement.style.opacity = '0';
        imageElement.onload = function() {
            imageElement.style.transition = 'opacity 0.3s ease';
            imageElement.style.opacity = '1';
        };

        // Set radio button value
        const radioButton = clone.querySelector('.main-image-radio');
        radioButton.value = filename;

        // If this is the first image, check the radio button
        if (uploadedImages.length === 1) {
            radioButton.checked = true;
        }

        // Set remove button data
        const removeButton = clone.querySelector('.remove-image');
        removeButton.dataset.filename = filename;

        // Add event listener to remove button
        removeButton.addEventListener('click', function() {
            removeImage(filename);
        });

        // Append to preview container
        document.getElementById('image-preview-row').appendChild(clone);

        // Initialize or reinitialize Sortable after adding a new image
        initSortable();

        // Show drag hint if we have more than 1 image
        updateDragHintVisibility();
    }

    // Function to update drag hint visibility
    function updateDragHintVisibility() {
        const dragHint = document.getElementById('drag-hint');
        if (uploadedImages.length > 1) {
            dragHint.classList.remove('d-none');
        } else {
            dragHint.classList.add('d-none');
        }
    }

    // Function to remove image
    function removeImage(filename) {
        // Confirm deletion
        if (!confirm('Bạn có chắc chắn muốn xóa hình ảnh này?')) {
            return;
        }

        // Send request to remove file
        fetch('/thuenhadanang/property-listing/remove-image', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `filename=${filename}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove from uploaded images array
                const index = uploadedImages.indexOf(filename);
                if (index > -1) {
                    uploadedImages.splice(index, 1);
                }

                // Remove from preview with animation
                const previewItems = document.querySelectorAll('.image-preview-item');
                previewItems.forEach(item => {
                    const removeBtn = item.querySelector('.remove-image');
                    if (removeBtn.dataset.filename === filename) {
                        // Fade out animation
                        item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                        item.style.opacity = '0';
                        item.style.transform = 'scale(0.9)';

                        // Remove after animation
                        setTimeout(() => {
                            item.remove();

                            // Reinitialize Sortable after removing an image
                            if (uploadedImages.length > 0) {
                                initSortable();
                            }
                        }, 300);
                    }
                });

                // Show no images message if no images left
                if (uploadedImages.length === 0) {
                    setTimeout(() => {
                        document.getElementById('no-images-message').style.display = 'block';
                    }, 300);
                }

                // Update drag hint visibility
                updateDragHintVisibility();

                // If the removed image was the main image, select another one
                const mainImageRadio = document.querySelector('.main-image-radio:checked');
                if (!mainImageRadio && uploadedImages.length > 0) {
                    const firstRadio = document.querySelector('.main-image-radio');
                    if (firstRadio) {
                        firstRadio.checked = true;
                    }
                }

                // Show success message
                showToast('success', 'Đã xóa hình ảnh thành công');
            } else {
                showToast('error', 'Lỗi: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Có lỗi xảy ra khi xóa hình ảnh');
        });
    }

    // Submit property
    document.getElementById('submit-property').addEventListener('click', function() {
        // Check if we have at least one image
        if (uploadedImages.length === 0) {
            showToast('warning', 'Vui lòng tải lên ít nhất một hình ảnh');
            return;
        }

        // Get main image
        const mainImageRadio = document.querySelector('.main-image-radio:checked');
        const mainImage = mainImageRadio ? mainImageRadio.value : uploadedImages[0];

        // Show loading modal
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();

        // Send request to save property
        fetch('/thuenhadanang/property-listing/save-property', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `images=${JSON.stringify(uploadedImages)}&main_image=${mainImage}`
        })
        .then(response => response.json())
        .then(data => {
            // Hide loading modal
            loadingModal.hide();

            if (data.success) {
                // Show success modal
                const successModal = new bootstrap.Modal(document.getElementById('successModal'));
                successModal.show();
            } else {
                // Show error modal
                document.getElementById('error-message').textContent = data.message || 'Có lỗi xảy ra khi đăng tin. Vui lòng thử lại sau.';
                const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
                errorModal.show();
            }
        })
        .catch(error => {
            // Hide loading modal
            loadingModal.hide();

            // Show error modal
            console.error('Error:', error);
            document.getElementById('error-message').textContent = 'Có lỗi xảy ra khi kết nối đến máy chủ. Vui lòng thử lại sau.';
            const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
            errorModal.show();
        });
    });

    // Toast notification function
    function showToast(type, message) {
        // Create toast container if not exists
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }

        // Set toast color based on type
        let bgColor = 'bg-primary';
        let icon = 'bi-info-circle-fill';

        switch (type) {
            case 'success':
                bgColor = 'bg-success';
                icon = 'bi-check-circle-fill';
                break;
            case 'error':
                bgColor = 'bg-danger';
                icon = 'bi-exclamation-circle-fill';
                break;
            case 'warning':
                bgColor = 'bg-warning';
                icon = 'bi-exclamation-triangle-fill';
                break;
        }

        // Create toast element
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white ${bgColor} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi ${icon} me-2"></i> ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;

        // Add toast to container
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // Initialize and show toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
        toast.show();

        // Remove toast after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    }
});
</script>
