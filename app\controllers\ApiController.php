<?php
require_once BASE_PATH . '/app/controllers/BaseController.php';
require_once BASE_PATH . '/app/models/Property.php';
require_once BASE_PATH . '/app/models/PropertyType.php';

class ApiController extends BaseController {
    private $propertyModel;
    private $propertyTypeModel;

    public function __construct() {
        $this->propertyModel = new Property();
        $this->propertyTypeModel = new PropertyType();
    }

    // Phương thức xử lý API lấy bất động sản
    public function getProperties() {
        // Thiết lập header JSON
        header('Content-Type: application/json');

        // Ghi log để gỡ lỗi
        error_log('API getProperties được gọi với tham số: ' . json_encode($_GET));

        // Lấy tham số từ request
        $type = isset($_GET['type']) ? $_GET['type'] : 'featured';
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 12;

        // Giới hạn số lượng tối đa
        if ($limit > 24) {
            $limit = 24;
        }

        $properties = [];
        $typeInfo = null;

        // Lấy dữ liệu theo loại
        if ($type === 'featured') {
            // Lấy bất động sản nổi bật
            $properties = $this->propertyModel->getFeaturedProperties($limit);
            $typeInfo = (object) [
                'id' => 0,
                'name' => 'Bất động sản nổi bật',
                'slug' => 'featured'
            ];
        } else {
            // Kiểm tra xem type có phải là ID hay slug
            if (is_numeric($type)) {
                // Lấy bất động sản theo ID loại
                $properties = $this->propertyModel->getPropertiesByType($type, $limit);
                $typeInfo = $this->propertyTypeModel->getPropertyTypeById($type);
            } else {
                // Lấy bất động sản theo slug loại
                $properties = $this->propertyModel->getPropertiesByTypeSlug($type, $limit);
                $typeInfo = $this->propertyTypeModel->getPropertyTypeBySlug($type);
            }
        }

        // Chuẩn bị dữ liệu trả về
        $result = [
            'success' => true,
            'type' => $typeInfo,
            'properties' => []
        ];

        // Xử lý dữ liệu bất động sản
        foreach ($properties as $property) {
            // Lấy hình ảnh chính
            $mainImage = $property->main_image;
            $imagePath = '/thuenhadanang/public/uploads/properties/' . ($mainImage ?: 'default-property.jpg');

            // Định dạng giá (chỉ trả về giá gốc, để JavaScript định dạng)
            $pricePeriod = '';

            switch ($property->price_period) {
                case 'day':
                    $pricePeriod = 'day';
                    break;
                case 'week':
                    $pricePeriod = 'week';
                    break;
                case 'month':
                    $pricePeriod = 'month';
                    break;
                case 'quarter':
                    $pricePeriod = 'quarter';
                    break;
                case 'year':
                    $pricePeriod = 'year';
                    break;
            }

            // Thêm vào mảng kết quả
            $result['properties'][] = [
                'id' => $property->id,
                'title' => $property->title,
                'slug' => $property->slug,
                'address' => $property->address,
                'ward' => $property->ward_name,
                'price' => $property->price,
                'price_period' => $pricePeriod,
                'area' => $property->area,
                'bedrooms' => $property->bedrooms,
                'bathrooms' => $property->bathrooms,
                'image' => $imagePath,
                'url' => '/thuenhadanang/' . $property->slug . '-' . $property->id,
                'status' => $property->status,
                'featured' => $property->featured
            ];
        }

        // Ghi log kết quả
        error_log('API getProperties trả về ' . count($result['properties']) . ' bất động sản');

        // Trả về kết quả dạng JSON
        echo json_encode($result);
        exit;
    }
}
