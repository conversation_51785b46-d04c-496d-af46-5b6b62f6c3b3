document.addEventListener('DOMContentLoaded', function() {
    // Xử lý hiển thị số điện thoại khi click
    document.querySelectorAll('.phone-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const encodedPhone = this.dataset.phone;
            const phone = atob(encodedPhone);
            this.querySelector('.phone-placeholder').textContent = formatPhoneNumber(phone);
            
            // Tạo link gọi điện sau khi click
            this.addEventListener('click', function() {
                window.location.href = 'tel:' + phone;
            }, {once: true});
        });
    });
    
    // Xử lý nút Zalo
    document.querySelectorAll('.zalo-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const encodedZalo = this.dataset.zalo;
            const zalo = atob(encodedZalo);
            window.open('https://zalo.me/' + zalo, '_blank');
        });
    });
    
    // Hàm định dạng số điện thoại
    function formatPhoneNumber(phone) {
        phone = phone.replace(/[^0-9]/g, '');
        if (phone.length === 10) {
            return phone.substring(0, 4) + '.' + phone.substring(4, 7) + '.' + phone.substring(7);
        }
        return phone;
    }
});
