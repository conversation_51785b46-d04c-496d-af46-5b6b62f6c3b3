<?php
/**
 * Mobile Search API Endpoint
 * Handles mobile-specific search requests
 */

// Disable error display for clean JSON
ini_set('display_errors', 0);
error_reporting(0);

// Set headers for API response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Start output buffering to catch any unwanted output
ob_start();

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Log to file instead of output
    error_log('Mobile Search API Request: ' . $_SERVER['REQUEST_METHOD'] . ' ' . $_SERVER['REQUEST_URI']);
    error_log('Mobile Search API Params: ' . json_encode($_GET));

    // Include required files with error checking
    $requiredFiles = [
        __DIR__ . '/../app/libraries/Core.php',
        __DIR__ . '/../app/libraries/Database.php',
        __DIR__ . '/../app/models/Property.php',
        __DIR__ . '/../app/models/PropertyType.php',
        __DIR__ . '/../app/models/Ward.php',
        __DIR__ . '/../app/controllers/SearchApiController.php'
    ];

    foreach ($requiredFiles as $file) {
        if (!file_exists($file)) {
            throw new Exception("Required file not found: $file");
        }
        require_once $file;
    }

    // Use SearchApiController instead of MobileSearchController for now
    $controller = new SearchApiController();

    // Route to appropriate method based on action parameter
    $action = isset($_GET['action']) ? $_GET['action'] : 'search';

    switch ($action) {
        case 'search':
            // Use existing ajaxSearch method
            $controller->ajaxSearch();
            break;

        case 'filters':
            // Use existing getFilterOptions method
            $controller->getFilterOptions();
            break;

        case 'suggestions':
        case 'quick':
            // Simple response for now
            ob_clean();
            echo json_encode([
                'success' => true,
                'data' => [
                    'properties' => [],
                    'count' => 0,
                    'suggestions' => []
                ],
                'message' => 'Feature coming soon'
            ]);
            break;

        default:
            throw new Exception('Invalid action: ' . $action);
    }

} catch (Exception $e) {
    // Clean any unwanted output
    ob_clean();

    // Log the error
    error_log('Mobile Search API Error: ' . $e->getMessage());
    error_log('Mobile Search API Stack Trace: ' . $e->getTraceAsString());

    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => [
            'message' => 'Mobile search API error',
            'details' => $e->getMessage(),
            'mobile' => true
        ],
        'timestamp' => time()
    ]);
}

// End output buffering
ob_end_flush();
?>
