<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hybrid Search System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .log-container {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .test-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .test-button {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="mb-4">🔬 Hybrid Search System Test</h1>
        
        <!-- Test Overview -->
        <div class="test-section">
            <h3>📋 Test Overview</h3>
            <p>This page tests the Hybrid Search approach that prevents double-loading of data on search pages.</p>
            <div class="row">
                <div class="col-md-6">
                    <h5>✅ What should work:</h5>
                    <ul>
                        <li>Server-rendered pages skip skeleton loading</li>
                        <li>Pages from homepage search skip skeleton</li>
                        <li>AJAX search works normally after user interaction</li>
                        <li>Browser back/forward works correctly</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>❌ What should be prevented:</h5>
                    <ul>
                        <li>Double-loading of same data</li>
                        <li>Unnecessary skeleton animations</li>
                        <li>Flash of content during page load</li>
                        <li>Redundant AJAX requests</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="test-section">
            <h3>🎮 Test Controls</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>Hybrid Detection Tests:</h5>
                    <button class="btn btn-primary test-button" onclick="testServerDataDetection()">
                        Test Server Data Detection
                    </button>
                    <button class="btn btn-primary test-button" onclick="testReferrerDetection()">
                        Test Referrer Detection
                    </button>
                    <button class="btn btn-primary test-button" onclick="testHybridInitialization()">
                        Test Hybrid Initialization
                    </button>
                </div>
                <div class="col-md-6">
                    <h5>Search Functionality Tests:</h5>
                    <button class="btn btn-success test-button" onclick="testAjaxSearch()">
                        Test AJAX Search
                    </button>
                    <button class="btn btn-success test-button" onclick="testHistoryManagement()">
                        Test History Management
                    </button>
                    <button class="btn btn-success test-button" onclick="testSkeletonBehavior()">
                        Test Skeleton Behavior
                    </button>
                </div>
            </div>
            <div class="mt-3">
                <button class="btn btn-warning" onclick="runAllTests()">🚀 Run All Tests</button>
                <button class="btn btn-secondary" onclick="clearLogs()">🗑️ Clear Logs</button>
                <button class="btn btn-info" onclick="exportLogs()">📄 Export Logs</button>
            </div>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="testResults"></div>
        </div>

        <!-- Console Logs -->
        <div class="test-section">
            <h3>📝 Console Logs</h3>
            <div id="consoleLogs" class="log-container"></div>
        </div>

        <!-- System Status -->
        <div class="test-section">
            <h3>⚙️ System Status</h3>
            <div id="systemStatus" class="row"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test logging system
        let testLogs = [];
        
        function logTest(message, type = 'info', details = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                timestamp,
                message,
                type,
                details
            };
            
            testLogs.push(logEntry);
            
            // Display in console logs
            const logsContainer = document.getElementById('consoleLogs');
            const logElement = document.createElement('div');
            logElement.innerHTML = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            if (details) {
                logElement.innerHTML += `<br>Details: ${JSON.stringify(details, null, 2)}`;
            }
            logsContainer.appendChild(logElement);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            
            console.log(`[Hybrid Test] ${message}`, details);
        }

        function showTestResult(title, status, message, details = null) {
            const resultsContainer = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `test-result ${status}`;
            resultElement.innerHTML = `
                <strong>${title}</strong>: ${message}
                ${details ? `<br><small>${details}</small>` : ''}
            `;
            resultsContainer.appendChild(resultElement);
        }

        // Test functions
        function testServerDataDetection() {
            logTest('Testing server data detection...', 'info');
            
            try {
                // Check for data attribute
                const mainContent = document.querySelector('.main-content-section');
                const hasDataAttribute = mainContent && mainContent.dataset.serverRendered === 'true';
                
                // Check for results container
                const resultsContainer = document.querySelector('.properties-grid');
                const hasResults = resultsContainer && resultsContainer.children.length > 0;
                
                // Check for results header
                const resultsHeader = document.querySelector('.results-header');
                const resultsTitle = document.querySelector('.results-title');
                const hasHeader = resultsHeader && resultsTitle && resultsTitle.textContent.trim();
                
                const result = {
                    dataAttribute: hasDataAttribute,
                    resultsContainer: hasResults,
                    resultsHeader: hasHeader,
                    detected: hasDataAttribute || hasResults || hasHeader
                };
                
                showTestResult(
                    'Server Data Detection',
                    result.detected ? 'success' : 'warning',
                    result.detected ? 'Server data detected' : 'No server data detected',
                    `Data attribute: ${result.dataAttribute}, Results: ${result.resultsContainer}, Header: ${result.resultsHeader}`
                );
                
                logTest('Server data detection completed', 'success', result);
                
            } catch (error) {
                showTestResult('Server Data Detection', 'error', 'Test failed', error.message);
                logTest('Server data detection failed', 'error', error);
            }
        }

        function testReferrerDetection() {
            logTest('Testing referrer detection...', 'info');
            
            try {
                const referrer = document.referrer;
                const currentHost = window.location.hostname;
                let cameFromHomepage = false;
                
                if (referrer) {
                    const referrerUrl = new URL(referrer);
                    cameFromHomepage = referrerUrl.hostname === currentHost && 
                        (referrerUrl.pathname === '/thuenhadanang/' || 
                         referrerUrl.pathname === '/thuenhadanang/index.php' ||
                         referrerUrl.pathname === '/');
                }
                
                const result = {
                    referrer: referrer || 'No referrer',
                    currentHost,
                    cameFromHomepage
                };
                
                showTestResult(
                    'Referrer Detection',
                    'success',
                    cameFromHomepage ? 'Came from homepage' : 'Did not come from homepage',
                    `Referrer: ${result.referrer}`
                );
                
                logTest('Referrer detection completed', 'success', result);
                
            } catch (error) {
                showTestResult('Referrer Detection', 'error', 'Test failed', error.message);
                logTest('Referrer detection failed', 'error', error);
            }
        }

        function testHybridInitialization() {
            logTest('Testing hybrid initialization...', 'info');
            
            try {
                // Check if AjaxSearch class exists
                const ajaxSearchExists = typeof window.ajaxSearch !== 'undefined';
                
                if (ajaxSearchExists) {
                    const ajaxSearch = window.ajaxSearch;
                    const result = {
                        exists: true,
                        hasServerData: ajaxSearch.hasServerData || false,
                        isInitialPageLoad: ajaxSearch.isInitialPageLoad || false,
                        initialUrl: ajaxSearch.initialUrl || 'Not set'
                    };
                    
                    showTestResult(
                        'Hybrid Initialization',
                        'success',
                        'AJAX Search system initialized',
                        `Server data: ${result.hasServerData}, Initial load: ${result.isInitialPageLoad}`
                    );
                    
                    logTest('Hybrid initialization completed', 'success', result);
                } else {
                    showTestResult(
                        'Hybrid Initialization',
                        'warning',
                        'AJAX Search system not found',
                        'This is normal if not on a search page'
                    );
                    
                    logTest('AJAX Search system not found', 'warning');
                }
                
            } catch (error) {
                showTestResult('Hybrid Initialization', 'error', 'Test failed', error.message);
                logTest('Hybrid initialization failed', 'error', error);
            }
        }

        function testAjaxSearch() {
            logTest('Testing AJAX search functionality...', 'info');
            
            try {
                if (typeof window.ajaxSearch !== 'undefined') {
                    const ajaxSearch = window.ajaxSearch;
                    
                    // Check if search form exists
                    const searchForm = document.getElementById('searchPageForm');
                    const hasForm = !!searchForm;
                    
                    // Check if results container exists
                    const resultsContainer = document.querySelector('.properties-grid');
                    const hasContainer = !!resultsContainer;
                    
                    const result = {
                        ajaxSearchExists: true,
                        hasForm,
                        hasContainer,
                        canPerformSearch: hasForm && hasContainer
                    };
                    
                    showTestResult(
                        'AJAX Search Functionality',
                        result.canPerformSearch ? 'success' : 'warning',
                        result.canPerformSearch ? 'AJAX search ready' : 'AJAX search not ready',
                        `Form: ${hasForm}, Container: ${hasContainer}`
                    );
                    
                    logTest('AJAX search functionality test completed', 'success', result);
                } else {
                    showTestResult(
                        'AJAX Search Functionality',
                        'warning',
                        'AJAX Search not available',
                        'This is normal if not on a search page'
                    );
                    
                    logTest('AJAX Search not available', 'warning');
                }
                
            } catch (error) {
                showTestResult('AJAX Search Functionality', 'error', 'Test failed', error.message);
                logTest('AJAX search functionality test failed', 'error', error);
            }
        }

        function testHistoryManagement() {
            logTest('Testing history management...', 'info');
            
            try {
                const currentUrl = window.location.href;
                const hasHistoryAPI = !!(window.history && window.history.pushState);
                
                const result = {
                    currentUrl,
                    hasHistoryAPI,
                    canManageHistory: hasHistoryAPI
                };
                
                showTestResult(
                    'History Management',
                    hasHistoryAPI ? 'success' : 'error',
                    hasHistoryAPI ? 'History API available' : 'History API not available',
                    `Current URL: ${currentUrl}`
                );
                
                logTest('History management test completed', 'success', result);
                
            } catch (error) {
                showTestResult('History Management', 'error', 'Test failed', error.message);
                logTest('History management test failed', 'error', error);
            }
        }

        function testSkeletonBehavior() {
            logTest('Testing skeleton behavior...', 'info');
            
            try {
                // Check if skeleton elements exist
                const existingSkeleton = document.querySelector('.results-skeleton');
                const hasExistingSkeleton = !!existingSkeleton;
                
                // Check if real content is visible
                const resultsContainer = document.querySelector('.properties-grid');
                const resultsHeader = document.querySelector('.results-header');
                const contentVisible = !!(resultsContainer && resultsContainer.style.display !== 'none') &&
                                     !!(resultsHeader && resultsHeader.style.display !== 'none');
                
                const result = {
                    hasExistingSkeleton,
                    contentVisible,
                    properBehavior: !hasExistingSkeleton && contentVisible
                };
                
                showTestResult(
                    'Skeleton Behavior',
                    result.properBehavior ? 'success' : 'warning',
                    result.properBehavior ? 'Skeleton behavior correct' : 'Skeleton behavior needs attention',
                    `Skeleton exists: ${hasExistingSkeleton}, Content visible: ${contentVisible}`
                );
                
                logTest('Skeleton behavior test completed', 'success', result);
                
            } catch (error) {
                showTestResult('Skeleton Behavior', 'error', 'Test failed', error.message);
                logTest('Skeleton behavior test failed', 'error', error);
            }
        }

        function runAllTests() {
            logTest('Running all tests...', 'info');
            clearResults();
            
            setTimeout(() => testServerDataDetection(), 100);
            setTimeout(() => testReferrerDetection(), 200);
            setTimeout(() => testHybridInitialization(), 300);
            setTimeout(() => testAjaxSearch(), 400);
            setTimeout(() => testHistoryManagement(), 500);
            setTimeout(() => testSkeletonBehavior(), 600);
            
            setTimeout(() => {
                logTest('All tests completed', 'success');
                updateSystemStatus();
            }, 700);
        }

        function clearLogs() {
            testLogs = [];
            document.getElementById('consoleLogs').innerHTML = '';
            logTest('Logs cleared', 'info');
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        function exportLogs() {
            const logsText = testLogs.map(log => 
                `[${log.timestamp}] ${log.type.toUpperCase()}: ${log.message}${log.details ? '\nDetails: ' + JSON.stringify(log.details, null, 2) : ''}`
            ).join('\n\n');
            
            const blob = new Blob([logsText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `hybrid-search-test-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            logTest('Logs exported', 'success');
        }

        function updateSystemStatus() {
            const statusContainer = document.getElementById('systemStatus');
            const status = {
                ajaxSearch: typeof window.ajaxSearch !== 'undefined',
                mobileSearch: typeof window.mobileSearch !== 'undefined',
                bootstrap: typeof window.bootstrap !== 'undefined',
                jquery: typeof window.$ !== 'undefined'
            };
            
            statusContainer.innerHTML = Object.entries(status).map(([key, value]) => `
                <div class="col-md-3">
                    <div class="card ${value ? 'border-success' : 'border-warning'}">
                        <div class="card-body text-center">
                            <i class="bi ${value ? 'bi-check-circle-fill text-success' : 'bi-exclamation-triangle-fill text-warning'} fs-2"></i>
                            <h6 class="card-title mt-2">${key}</h6>
                            <p class="card-text">${value ? 'Available' : 'Not Available'}</p>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logTest('Hybrid Search Test Page loaded', 'success');
            updateSystemStatus();
            
            // Auto-run tests after a short delay
            setTimeout(() => {
                logTest('Auto-running initial tests...', 'info');
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>
