# API .htaccess for AJAX search endpoints

RewriteEngine On

# Enable CORS for AJAX requests
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, X-Requested-With"

# Route API requests
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Route /api/search to search.php
RewriteRule ^search/?$ search.php [L,QSA]

# Route /api/search/filters to search.php with action=filters
RewriteRule ^search/filters/?$ search.php?action=filters [L,QSA]

# Security: Deny access to sensitive files
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

# Set proper MIME types
AddType application/json .json
AddType text/javascript .js

# Enable compression for JSON responses
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE text/javascript
</IfModule>

# Cache control for API responses
<IfModule mod_expires.c>
    ExpiresActive On
    # Don't cache API responses
    ExpiresByType application/json "access plus 0 seconds"
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
