<?php

// Final verification test - simulate exact browser request
define('BASE_PATH', __DIR__);
define('APP_PATH', BASE_PATH . '/app');

// Load required files
require_once APP_PATH . '/libraries/Database.php';
require_once APP_PATH . '/libraries/UrlHandler.php';
require_once APP_PATH . '/models/PropertyType.php';
require_once APP_PATH . '/models/Ward.php';
require_once APP_PATH . '/models/Property.php';
require_once APP_PATH . '/controllers/BaseController.php';
require_once APP_PATH . '/controllers/SearchController.php';

echo "<h1>Final Verification Test</h1>\n";
echo "<p>Simulating exact browser request for: <strong>cho-thue-can-ho-tai-an-hai-bac/gia-tren-15-trieu-dt-50-70m2-1pn</strong></p>\n";

try {
    // Simulate the exact flow from index.php
    $url = 'cho-thue-can-ho-tai-an-hai-bac/gia-tren-15-trieu-dt-50-70m2-1pn';
    
    echo "<h2>1. URL Handler Processing</h2>\n";
    $urlHandler = new UrlHandler();
    $parsedParams = $urlHandler->parseUrl($url);
    
    echo "<p><strong>URL:</strong> $url</p>\n";
    echo "<p><strong>Matched:</strong> " . ($parsedParams['matched'] ? 'Yes' : 'No') . "</p>\n";
    echo "<p><strong>Controller:</strong> " . $parsedParams['controller'] . "</p>\n";
    echo "<p><strong>Action:</strong> " . $parsedParams['action'] . "</p>\n";
    
    if ($parsedParams['matched']) {
        echo "<h2>2. SearchController Execution</h2>\n";
        
        // Simulate what index.php does
        $controllerName = $parsedParams['controller'];
        $action = $parsedParams['action'];
        $typeSlug = $parsedParams['type'];
        $wardSlug = $parsedParams['ward'];
        
        echo "<p><strong>Creating SearchController instance...</strong></p>\n";
        $controller = new SearchController();
        
        echo "<p><strong>Calling method:</strong> $action('$typeSlug', '$wardSlug', ...)</p>\n";
        
        // Capture output
        ob_start();
        
        // Call the exact method that would be called
        $controller->$action($typeSlug, $wardSlug, $parsedParams);
        
        $output = ob_get_clean();
        
        echo "<h2>3. Controller Output Analysis</h2>\n";
        
        // Check if output contains "Không tìm thấy kết quả phù hợp"
        if (strpos($output, 'Không tìm thấy kết quả phù hợp') !== false) {
            echo "<p style='color: green;'>✅ <strong>CORRECT:</strong> Output shows 'No results found' message</p>\n";
        } else {
            echo "<p style='color: red;'>❌ <strong>UNEXPECTED:</strong> Output does not show 'No results found' message</p>\n";
        }
        
        // Check if output contains property cards
        if (strpos($output, 'property-card') !== false) {
            echo "<p style='color: red;'>❌ <strong>PROBLEM:</strong> Output contains property cards when it shouldn't</p>\n";
            
            // Count property cards
            $propertyCardCount = substr_count($output, 'property-card');
            echo "<p><strong>Number of property cards found:</strong> $propertyCardCount</p>\n";
            
            // Extract property titles
            preg_match_all('/<h3 class="property-title">.*?<a[^>]*>(.*?)<\/a>.*?<\/h3>/s', $output, $matches);
            if (!empty($matches[1])) {
                echo "<h3>Properties being displayed:</h3>\n";
                echo "<ul>\n";
                foreach ($matches[1] as $title) {
                    echo "<li>" . htmlspecialchars(trim(strip_tags($title))) . "</li>\n";
                }
                echo "</ul>\n";
            }
            
        } else {
            echo "<p style='color: green;'>✅ <strong>CORRECT:</strong> Output does not contain property cards</p>\n";
        }
        
        // Check for specific bedrooms/area info in output
        if (strpos($output, '2 PN') !== false) {
            echo "<p style='color: red;'>❌ <strong>PROBLEM:</strong> Output shows properties with 2 bedrooms</p>\n";
        }
        
        if (strpos($output, '76m²') !== false) {
            echo "<p style='color: red;'>❌ <strong>PROBLEM:</strong> Output shows properties with 76m² area</p>\n";
        }
        
        echo "<h2>4. Form Values Check</h2>\n";
        
        // Check if form fields are populated correctly
        if (strpos($output, 'value="1"') !== false && strpos($output, 'selected') !== false) {
            echo "<p style='color: green;'>✅ Form shows 1 bedroom selected</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Form does not show 1 bedroom selected</p>\n";
        }
        
        if (strpos($output, 'value="50-70"') !== false && strpos($output, 'selected') !== false) {
            echo "<p style='color: green;'>✅ Form shows 50-70m² area selected</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Form does not show 50-70m² area selected</p>\n";
        }
        
        if (strpos($output, 'value="15+"') !== false && strpos($output, 'selected') !== false) {
            echo "<p style='color: green;'>✅ Form shows 15+ price selected</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Form does not show 15+ price selected</p>\n";
        }
        
        echo "<h2>5. Debug Information</h2>\n";
        echo "<p><strong>Output length:</strong> " . strlen($output) . " characters</p>\n";
        
        // Show first 1000 characters of output for debugging
        echo "<h3>Output Preview (first 1000 chars):</h3>\n";
        echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars(substr($output, 0, 1000));
        echo "</pre>\n";
        
        // Check for JavaScript that might be loading additional content
        if (strpos($output, 'ajax') !== false || strpos($output, 'fetch') !== false) {
            echo "<p style='color: orange;'>⚠️ <strong>WARNING:</strong> Output contains AJAX/fetch calls that might load additional content</p>\n";
        }
        
        echo "<h2>6. Conclusion</h2>\n";
        
        $hasNoResults = strpos($output, 'Không tìm thấy kết quả phù hợp') !== false;
        $hasPropertyCards = strpos($output, 'property-card') !== false;
        
        if ($hasNoResults && !$hasPropertyCards) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
            echo "<h4 style='color: #155724;'>✅ SYSTEM IS WORKING CORRECTLY</h4>\n";
            echo "<p style='color: #155724;'>The system correctly shows 'No results found' and does not display any properties.</p>\n";
            echo "<p style='color: #155724;'><strong>If you're seeing properties on the website, it might be due to:</strong></p>\n";
            echo "<ul style='color: #155724;'>\n";
            echo "<li>Browser cache - try hard refresh (Ctrl+F5)</li>\n";
            echo "<li>Different URL being accessed</li>\n";
            echo "<li>JavaScript loading additional content</li>\n";
            echo "<li>Session/cookie data</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
        } else if (!$hasNoResults && $hasPropertyCards) {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
            echo "<h4 style='color: #721c24;'>❌ SYSTEM HAS ISSUES</h4>\n";
            echo "<p style='color: #721c24;'>The system is incorrectly showing properties when it should show 'No results found'.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
            echo "<h4 style='color: #856404;'>⚠️ MIXED RESULTS</h4>\n";
            echo "<p style='color: #856404;'>The output contains both 'No results' message and property cards, which is unusual.</p>\n";
            echo "</div>\n";
        }
        
    } else {
        echo "<p style='color: red;'>❌ URL was not matched by UrlHandler</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<h2>7. Recommendations</h2>\n";
echo "<ul>\n";
echo "<li>🔄 Clear browser cache and try again</li>\n";
echo "<li>🔍 Check if you're accessing the exact URL: <code>/thuenhadanang/cho-thue-can-ho-tai-an-hai-bac/gia-tren-15-trieu-dt-50-70m2-1pn</code></li>\n";
echo "<li>🛠️ Check browser developer tools for any AJAX requests</li>\n";
echo "<li>📱 Try accessing from incognito/private browsing mode</li>\n";
echo "<li>🔧 Check if there are any custom JavaScript files modifying the page content</li>\n";
echo "</ul>\n";

?>
