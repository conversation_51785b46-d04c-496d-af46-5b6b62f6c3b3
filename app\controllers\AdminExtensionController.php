<?php
require_once __DIR__ . '/../models/ExtensionRequest.php';

class AdminExtensionController {
    private $extensionRequestModel;

    public function __construct() {
        $this->extensionRequestModel = new ExtensionRequest();
    }

    // Hiển thị danh sách yêu cầu gia hạn
    public function index() {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập admin
        if (!isset($_SESSION['admin_id'])) {
            header('Location: /thuenhadanang/quantrivien/login.php');
            exit;
        }

        // Lấy filter status từ query string
        $status = $_GET['status'] ?? null;
        if ($status && !in_array($status, ['pending', 'approved', 'rejected'])) {
            $status = null;
        }

        // Lấy danh sách yêu cầu gia hạn
        $extensionRequests = $this->extensionRequestModel->getAllRequests($status);

        // Lấy thống kê
        $stats = $this->extensionRequestModel->getStats();

        // <PERSON>ể<PERSON> tra AJAX request
        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
            // Trả về partial view cho AJAX
            include __DIR__ . '/../views/admin/extension-requests/table.php';
            exit;
        }

        // Trả về full page
        include __DIR__ . '/../views/admin/extension-requests/index.php';
    }

    // Phê duyệt yêu cầu gia hạn
    public function approve($id) {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập admin
        if (!isset($_SESSION['admin_id'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            exit;
        }

        // Chỉ chấp nhận POST request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Phương thức không được phép']);
            exit;
        }

        // Lấy ghi chú từ POST data
        $adminNote = $_POST['admin_note'] ?? null;

        // Phê duyệt yêu cầu
        $result = $this->extensionRequestModel->approveRequest($id, $_SESSION['admin_id'], $adminNote);

        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }

    // Từ chối yêu cầu gia hạn
    public function reject($id) {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập admin
        if (!isset($_SESSION['admin_id'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            exit;
        }

        // Chỉ chấp nhận POST request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Phương thức không được phép']);
            exit;
        }

        // Lấy ghi chú từ POST data
        $adminNote = $_POST['admin_note'] ?? null;

        // Từ chối yêu cầu
        $result = $this->extensionRequestModel->rejectRequest($id, $_SESSION['admin_id'], $adminNote);

        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }
}
?>
