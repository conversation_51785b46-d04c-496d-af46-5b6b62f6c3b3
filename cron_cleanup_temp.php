<?php
/**
 * <PERSON>ript dọn dẹp file tạm tự động
 * Chạy qua cron job, ví dụ: 0 0 * * * php /path/to/cron_cleanup_temp.php
 * Hoặc có thể chạy thủ công: php cron_cleanup_temp.php
 */

// Định nghĩa thư mục tạm
$tempUploadDir = __DIR__ . '/public/uploads/temp/';

// Thiết lập log file
$logFile = __DIR__ . '/logs/cleanup_temp.log';
$logDir = dirname($logFile);

// Tạo thư mục logs nếu chưa tồn tại
if (!file_exists($logDir)) {
    if (!mkdir($logDir, 0777, true)) {
        die("Không thể tạo thư mục logs: $logDir");
    }
}

// Hàm ghi log
function writeLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($logFile, $logMessage, FILE_APPEND);
    echo $logMessage; // In ra console nếu chạy thủ công
}

// Kiểm tra xem thư mục tồn tại không
if (!file_exists($tempUploadDir)) {
    writeLog("Thư mục tạm không tồn tại: $tempUploadDir");
    exit(1);
}

try {
    // Lấy tất cả các file trong thư mục tạm
    $files = scandir($tempUploadDir);
    $tempFiles = [];
    
    // Lọc ra các file tạm thời
    foreach ($files as $file) {
        if ($file != '.' && $file != '..' && preg_match('/^temp_\d+_\d+\.jpg$/', $file)) {
            $tempFiles[] = $file;
        }
    }
    
    $totalCount = count($tempFiles);
    writeLog("Tìm thấy $totalCount file tạm thời.");
    
    // Xác định thời gian tối đa (mặc định: 24 giờ = 86400 giây)
    $maxAge = 86400; // 24 giờ
    $currentTime = time();
    $deletedCount = 0;
    
    // Xóa các file cũ
    foreach ($tempFiles as $file) {
        $filePath = $tempUploadDir . $file;
        $fileTime = filemtime($filePath);
        $fileAge = $currentTime - $fileTime;
        
        // Nếu file cũ hơn thời gian tối đa
        if ($fileAge > $maxAge) {
            if (unlink($filePath)) {
                $deletedCount++;
                writeLog("Đã xóa file: $file (tuổi: " . round($fileAge / 3600, 1) . " giờ)");
            } else {
                writeLog("Không thể xóa file: $file");
            }
        }
    }
    
    writeLog("Hoàn tất: Đã xóa $deletedCount/$totalCount file tạm thời.");
    exit(0);
} catch (Exception $e) {
    writeLog("Lỗi: " . $e->getMessage());
    exit(1);
}
