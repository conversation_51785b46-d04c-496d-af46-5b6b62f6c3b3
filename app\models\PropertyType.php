<?php
require_once __DIR__ . '/../libraries/Database.php';

class PropertyType {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    // Lấy tất cả loại hình
    public function getAllPropertyTypes() {
        $this->db->query('SELECT * FROM property_types ORDER BY name ASC');
        return $this->db->resultSet();
    }

    // Lấy loại hình theo ID
    public function getPropertyTypeById($id) {
        $this->db->query('SELECT * FROM property_types WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    // Lấy loại hình theo slug
    public function getPropertyTypeBySlug($slug) {
        error_log('Getting property type by slug: "' . $slug . '"');

        // Debug: List all property types
        $allTypesDb = new Database();
        $allTypesDb->query('SELECT id, name, slug FROM property_types ORDER BY name');
        $allTypes = $allTypesDb->resultSet();
        error_log('All property types in database:');
        foreach ($allTypes as $t) {
            error_log('  ID: ' . $t->id . ', Name: "' . $t->name . '", Slug: "' . $t->slug . '"');
        }

        $this->db->query('SELECT * FROM property_types WHERE LOWER(slug) = LOWER(:slug)');
        $this->db->bind(':slug', $slug);
        $result = $this->db->single();

        if ($result) {
            error_log('Found property type - ID: ' . $result->id . ', Name: "' . $result->name . '", Slug: "' . $result->slug . '"');
        } else {
            error_log('No property type found with slug: "' . $slug . '"');
        }

        return $result;
    }

    // Tìm loại hình tương tự
    public function getSimilarPropertyType($slug) {
        error_log('Looking for similar property type to: "' . $slug . '"');

        // Tìm kiếm theo slug tương tự
        $this->db->query('SELECT * FROM property_types WHERE LOWER(slug) LIKE LOWER(:slug_pattern) LIMIT 1');
        $this->db->bind(':slug_pattern', '%' . strtolower($slug) . '%');
        $result = $this->db->single();

        if ($result) {
            error_log('Found similar property type by slug - ID: ' . $result->id . ', Name: "' . $result->name . '", Slug: "' . $result->slug . '"');
            return $result;
        }

        // Nếu không tìm thấy theo slug, thử tìm theo tên
        // Chuyển đổi slug thành từ khóa tìm kiếm
        $searchTerm = str_replace('-', ' ', $slug);
        $this->db->query('SELECT * FROM property_types WHERE LOWER(name) LIKE LOWER(:name_pattern) LIMIT 1');
        $this->db->bind(':name_pattern', '%' . strtolower($searchTerm) . '%');
        $result = $this->db->single();

        if ($result) {
            error_log('Found similar property type by name - ID: ' . $result->id . ', Name: "' . $result->name . '", Slug: "' . $result->slug . '"');
            return $result;
        }

        // Nếu vẫn không tìm thấy, trả về null
        error_log('No similar property type found for: "' . $slug . '"');
        return null;
    }

    // Tìm kiếm loại hình
    public function searchPropertyTypes($searchTerm) {
        $this->db->query('SELECT * FROM property_types WHERE name LIKE :search_name OR description LIKE :search_desc ORDER BY name ASC');
        $this->db->bind(':search_name', '%' . $searchTerm . '%');
        $this->db->bind(':search_desc', '%' . $searchTerm . '%');
        return $this->db->resultSet();
    }

    // Thêm loại hình mới
    public function create($data) {
        $this->db->query('INSERT INTO property_types (name, slug, description, status) VALUES (:name, :slug, :description, :status)');

        // Bind values
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':slug', $data['slug']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':status', $data['status'] ?? 1);

        // Execute
        return $this->db->execute();
    }

    // Cập nhật loại hình
    public function update($data) {
        $this->db->query('UPDATE property_types SET name = :name, slug = :slug, description = :description, status = :status WHERE id = :id');

        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':slug', $data['slug']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':status', $data['status']);

        // Execute
        return $this->db->execute();
    }

    // Xóa loại hình
    public function delete($id) {
        $this->db->query('DELETE FROM property_types WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }

    // Kiểm tra xem slug đã tồn tại chưa (trừ ID hiện tại)
    public function isSlugExists($slug, $id = 0) {
        $this->db->query('SELECT COUNT(*) as count FROM property_types WHERE slug = :slug AND id != :id');
        $this->db->bind(':slug', $slug);
        $this->db->bind(':id', $id);
        $row = $this->db->single();
        return $row->count > 0;
    }

    // Tạo slug từ tên
    public function createSlug($name) {
        // Chuyển đổi tiếng Việt sang không dấu
        $slug = $this->convertToSlug($name);

        // Kiểm tra xem slug đã tồn tại chưa
        $originalSlug = $slug;
        $count = 1;

        while ($this->isSlugExists($slug)) {
            $slug = $originalSlug . '-' . $count;
            $count++;
        }

        return $slug;
    }

    // Hàm chuyển đổi tiếng Việt sang không dấu
    private function convertToSlug($string) {
        $string = trim($string);
        $string = preg_replace('/[^a-zA-Z0-9ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễếệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ\s]/', '', $string);
        $string = preg_replace('/[\s]+/', ' ', $string);
        $string = str_replace(' ', '-', $string);

        $string = strtolower($string);

        // Chuyển đổi tiếng Việt sang không dấu
        $vietnamese = array(
            'à', 'á', 'ạ', 'ả', 'ã', 'â', 'ầ', 'ấ', 'ậ', 'ẩ', 'ẫ', 'ă', 'ằ', 'ắ', 'ặ', 'ẳ', 'ẵ',
            'è', 'é', 'ẹ', 'ẻ', 'ẽ', 'ê', 'ề', 'ế', 'ệ', 'ể', 'ễ',
            'ì', 'í', 'ị', 'ỉ', 'ĩ',
            'ò', 'ó', 'ọ', 'ỏ', 'õ', 'ô', 'ồ', 'ố', 'ộ', 'ổ', 'ỗ', 'ơ', 'ờ', 'ớ', 'ợ', 'ở', 'ỡ',
            'ù', 'ú', 'ụ', 'ủ', 'ũ', 'ư', 'ừ', 'ứ', 'ự', 'ử', 'ữ',
            'ỳ', 'ý', 'ỵ', 'ỷ', 'ỹ',
            'đ',
            'À', 'Á', 'Ạ', 'Ả', 'Ã', 'Â', 'Ầ', 'Ấ', 'Ậ', 'Ẩ', 'Ẫ', 'Ă', 'Ằ', 'Ắ', 'Ặ', 'Ẳ', 'Ẵ',
            'È', 'É', 'Ẹ', 'Ẻ', 'Ẽ', 'Ê', 'Ề', 'Ế', 'Ệ', 'Ể', 'Ễ',
            'Ì', 'Í', 'Ị', 'Ỉ', 'Ĩ',
            'Ò', 'Ó', 'Ọ', 'Ỏ', 'Õ', 'Ô', 'Ồ', 'Ố', 'Ộ', 'Ổ', 'Ỗ', 'Ơ', 'Ờ', 'Ớ', 'Ợ', 'Ở', 'Ỡ',
            'Ù', 'Ú', 'Ụ', 'Ủ', 'Ũ', 'Ư', 'Ừ', 'Ứ', 'Ự', 'Ử', 'Ữ',
            'Ỳ', 'Ý', 'Ỵ', 'Ỷ', 'Ỹ',
            'Đ'
        );

        $latin = array(
            'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
            'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
            'i', 'i', 'i', 'i', 'i',
            'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
            'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
            'y', 'y', 'y', 'y', 'y',
            'd',
            'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
            'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
            'i', 'i', 'i', 'i', 'i',
            'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
            'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
            'y', 'y', 'y', 'y', 'y',
            'd'
        );

        $string = str_replace($vietnamese, $latin, $string);

        return $string;
    }
}
