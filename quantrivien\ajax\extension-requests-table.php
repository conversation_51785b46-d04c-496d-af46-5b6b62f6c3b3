<?php
// AJAX endpoint chỉ trả về table data
session_start();

// <PERSON><PERSON><PERSON> tra quyền admin
if (!isset($_SESSION['admin_id'])) {
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Include model
require_once __DIR__ . '/../../app/models/ExtensionRequest.php';
$extensionRequestModel = new ExtensionRequest();

// Lấy filter status từ query string
$status = $_GET['status'] ?? null;
if ($status && !in_array($status, ['pending', 'approved', 'rejected'])) {
    $status = null;
}

// L<PERSON>y danh sách yêu cầu gia hạn
$extensionRequests = $extensionRequestModel->getAllRequests($status);

// Chỉ include table content
include __DIR__ . '/../pages/extension-requests-table.php';
?>
