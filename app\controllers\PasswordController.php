<?php
require_once 'app/models/User.php';

class PasswordController {
    private $userModel;

    public function __construct() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $this->userModel = new User();
    }

    public function index() {
        if (!isset($_SESSION['user_id'])) {
            $_SESSION['error_message'] = 'Vui lòng đăng nhập để thực hiện thao tác này';
            header('Location: /thuenhadanang/login');
            exit;
        }

        // Lấy thông tin user từ database
        $user = $this->userModel->getUserById($_SESSION['user_id']);
        
        // Nếu không tìm thấy user, chuyển hướng về trang login
        if (!$user) {
            $_SESSION['error_message'] = 'Không tìm thấy thông tin người dùng';
            session_write_close();
            header('Location: /thuenhadanang/login');
            exit;
        }

        $data = [
            'title' => 'Đổi mật khẩu - Thu<PERSON> Nẵng',
            'user' => $user
        ];

        // Set view và data cho layout
        $view = 'password';
        extract($data);
        
        // Require layout thay vì require trực tiếp view
        require 'app/views/layout.php';
    }

    public function update() {
        if (!isset($_SESSION['user_id'])) {
            $_SESSION['error_message'] = 'Vui lòng đăng nhập để thực hiện thao tác này';
            session_write_close();
            header('Location: /thuenhadanang/login');
            exit;
        }

        $userId = $_SESSION['user_id'];
        $currentPassword = trim($_POST['current_password'] ?? '');
        $newPassword = trim($_POST['new_password'] ?? '');
        $confirmPassword = trim($_POST['confirm_password'] ?? '');

        // Validate input
        if (empty($currentPassword)) {
            $_SESSION['error_message'] = 'Vui lòng nhập mật khẩu hiện tại';
            session_write_close();
            header('Location: /thuenhadanang/dashboard/password');
            exit;
        }

        if (empty($newPassword)) {
            $_SESSION['error_message'] = 'Vui lòng nhập mật khẩu mới';
            session_write_close();
            header('Location: /thuenhadanang/dashboard/password');
            exit;
        }

        if (empty($confirmPassword)) {
            $_SESSION['error_message'] = 'Vui lòng xác nhận mật khẩu mới';
            session_write_close();
            header('Location: /thuenhadanang/dashboard/password');
            exit;
        }

        // Validate độ dài mật khẩu mới
        if (strlen($newPassword) < 6) {
            $_SESSION['error_message'] = 'Mật khẩu mới phải có ít nhất 6 ký tự';
            session_write_close();
            header('Location: /thuenhadanang/dashboard/password');
            exit;
        }

        // Kiểm tra mật khẩu mới không được giống mật khẩu cũ
        if ($newPassword === $currentPassword) {
            $_SESSION['error_message'] = 'Mật khẩu mới không được giống mật khẩu hiện tại';
            session_write_close();
            header('Location: /thuenhadanang/dashboard/password');
            exit;
        }

        // Get user from database
        $user = $this->userModel->getUserById($userId);
        if (!$user) {
            $_SESSION['error_message'] = 'Không tìm thấy thông tin người dùng';
            session_write_close();
            header('Location: /thuenhadanang/dashboard/password');
            exit;
        }

        // Verify current password
        if (!password_verify($currentPassword, $user->password)) {
            $_SESSION['error_message'] = 'Mật khẩu hiện tại không chính xác';
            session_write_close();
            header('Location: /thuenhadanang/dashboard/password');
            exit;
        }

        // Check if new passwords match
        if ($newPassword !== $confirmPassword) {
            $_SESSION['error_message'] = 'Mật khẩu mới và xác nhận mật khẩu không khớp';
            session_write_close();
            header('Location: /thuenhadanang/dashboard/password');
            exit;
        }

        // Update password
        try {
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            if ($this->userModel->updatePassword($userId, $hashedPassword)) {
                $_SESSION['success_message'] = 'Đổi mật khẩu thành công! Vui lòng sử dụng mật khẩu mới trong lần đăng nhập tới.';
            } else {
                throw new Exception('Không thể cập nhật mật khẩu');
            }
        } catch (Exception $e) {
            $_SESSION['error_message'] = 'Có lỗi xảy ra khi cập nhật mật khẩu. Vui lòng thử lại sau.';
        }

        session_write_close();
        header('Location: /thuenhadanang/dashboard/password');
        exit;
    }

    public function forgot() {
        $title = 'Quên mật khẩu - Thuê Nhà Đà Nẵng';
        $view = 'forgot-password';
        require 'app/views/layout.php';
    }

    public function sendResetLink() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $email = $_POST['email'] ?? '';
            header('Location: /thuenhadanang/login');
            exit;
        }
        header('Location: /thuenhadanang/forgot-password');
        exit;
    }
} 