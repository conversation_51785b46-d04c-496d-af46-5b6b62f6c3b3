<?php
// Khởi tạo session
session_start();

// Ki<PERSON>m tra quyền truy cập
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Định nghĩa thư mục tạm
$tempUploadDir = __DIR__ . '/../public/uploads/temp/';

// Biến lưu trữ kết quả
$result = [
    'success' => false,
    'message' => '',
    'deleted_count' => 0,
    'total_count' => 0,
    'error' => ''
];

// Kiểm tra xem thư mục tồn tại không
if (!file_exists($tempUploadDir)) {
    $result['message'] = 'Thư mục tạm không tồn tại: ' . $tempUploadDir;
} else {
    try {
        // Lấy tất cả các file trong thư mục tạm
        $files = scandir($tempUploadDir);
        $tempFiles = [];
        
        // Lọ<PERSON> ra các file tạm thời
        foreach ($files as $file) {
            if ($file != '.' && $file != '..' && preg_match('/^temp_\d+_\d+\.jpg$/', $file)) {
                $tempFiles[] = $file;
            }
        }
        
        $result['total_count'] = count($tempFiles);
        
        // Xác định thời gian tối đa (mặc định: 24 giờ = 86400 giây)
        $maxAge = isset($_POST['max_age']) ? intval($_POST['max_age']) : 86400;
        $currentTime = time();
        $deletedCount = 0;
        
        // Xóa các file cũ
        foreach ($tempFiles as $file) {
            $filePath = $tempUploadDir . $file;
            $fileTime = filemtime($filePath);
            
            // Nếu file cũ hơn thời gian tối đa
            if (($currentTime - $fileTime) > $maxAge) {
                if (unlink($filePath)) {
                    $deletedCount++;
                }
            }
        }
        
        $result['deleted_count'] = $deletedCount;
        $result['success'] = true;
        $result['message'] = "Đã xóa $deletedCount/$result[total_count] file tạm thời.";
    } catch (Exception $e) {
        $result['error'] = 'Lỗi: ' . $e->getMessage();
    }
}

// Kiểm tra nếu là AJAX request
if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    header('Content-Type: application/json');
    echo json_encode($result);
    exit;
}

// Nếu không phải AJAX request, hiển thị giao diện
include 'templates/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Dọn dẹp file tạm</h1>
    
    <?php if (!empty($result['message'])): ?>
    <div class="alert alert-<?php echo $result['success'] ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
        <?php echo $result['message']; ?>
        <?php if (!empty($result['error'])): ?>
            <br>Chi tiết lỗi: <?php echo $result['error']; ?>
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Dọn dẹp file tạm trong thư mục uploads/temp</h6>
        </div>
        <div class="card-body">
            <p>Công cụ này sẽ xóa các file tạm thời đã cũ trong thư mục <code>public/uploads/temp</code>.</p>
            <p>Các file tạm thời được tạo ra khi người dùng upload hình ảnh cho bất động sản nhưng chưa lưu form.</p>
            
            <form method="post" action="" id="cleanupForm">
                <div class="mb-3">
                    <label for="max_age" class="form-label">Thời gian tối đa (giây)</label>
                    <input type="number" class="form-control" id="max_age" name="max_age" value="86400" min="3600">
                    <div class="form-text">
                        File cũ hơn thời gian này sẽ bị xóa. Mặc định: 86400 giây (24 giờ)
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">Dọn dẹp ngay</button>
            </form>
            
            <?php if ($result['success'] && $result['total_count'] > 0): ?>
            <div class="mt-4">
                <h5>Kết quả:</h5>
                <ul>
                    <li>Tổng số file tạm: <?php echo $result['total_count']; ?></li>
                    <li>Số file đã xóa: <?php echo $result['deleted_count']; ?></li>
                </ul>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'templates/footer.php'; ?>
