<?php
require_once 'app/models/Package.php';

class PricingController extends BaseController {
    private $packageModel;
    
    public function __construct() {
        $this->packageModel = new Package();
    }

    public function index() {
        // L<PERSON><PERSON> danh sách packages đang hoạt động
        $packages = $this->packageModel->getActivePackages();

        // Thiết lập tiêu đề trang
        $title = 'Bảng giá dịch vụ - <PERSON><PERSON><PERSON> Nẵng';
        
        // Thiết lập view và data
        $view = 'pricing';
        $data = [
            'title' => $title,
            'packages' => $packages
        ];
        
        // Render layout với view
        require 'app/views/layout.php';
    }
}
