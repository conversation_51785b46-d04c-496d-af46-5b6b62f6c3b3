<!-- Desktop Search Section -->
<section class="modern-search-section bg-white py-2 d-none d-lg-block">
    <div class="container">
        <!-- Modern Search Form -->
        <form class="modern-search-form" action="/thuenhadanang/search" method="get" id="searchPageForm">
            <div class="search-bar-container">
                <!-- Main Search Bar -->
                <div class="main-search-bar">
                    <div class="search-input-group">
                        <!-- 1. T<PERSON> khóa (Keywords/Location) -->
                        <div class="search-field location-field">
                            <input type="text" class="form-control search-address" name="keyword"
                                   placeholder="Tìm kiếm địa điểm, tên đường..."
                                   value="<?php echo htmlspecialchars($keyword); ?>" id="searchKeyword">
                        </div>

                        <!-- 2. Lo<PERSON><PERSON> hình (Property Type) -->
                        <div class="search-field type-field">
                            <select class="form-select" name="type" id="searchType">
                                <option value="">Chọn loại hình</option>
                                <?php
                                // Debug info (hidden)
                                echo '<pre style="display:none;">Selected Type: "' . $selectedType . '"</pre>';
                                echo '<pre style="display:none;">All property types: ';
                                foreach ($propertyTypes as $type) {
                                    echo $type->slug . ', ';
                                }
                                echo '</pre>';

                                // Kiểm tra xem URL hiện tại có chứa loại hình không
                                $currentUrl = $_SERVER['REQUEST_URI'];
                                $containsCanHo = (strpos($currentUrl, 'can-ho') !== false);
                                $containsNhaNguyenCan = (strpos($currentUrl, 'nha-nguyen-can') !== false);
                                $containsPhongTro = (strpos($currentUrl, 'nha-tro-phong-tro') !== false);
                                $containsMatBang = (strpos($currentUrl, 'mat-bang') !== false);

                                echo '<pre style="display:none;">Current URL: ' . $currentUrl . '</pre>';
                                echo '<pre style="display:none;">Contains can-ho: ' . ($containsCanHo ? 'Yes' : 'No') . '</pre>';

                                $typeFound = false;
                                $isNhaDatUrl = (strpos($currentUrl, 'cho-thue-nha-dat') !== false);

                                foreach ($propertyTypes as $type):
                                    // Nếu URL là cho-thue-nha-dat, không chọn bất kỳ loại hình nào
                                    if ($isNhaDatUrl) {
                                        $isSelected = false;
                                    } else {
                                        // Kiểm tra theo URL
                                        $isSelectedByUrl = false;
                                        if ($containsCanHo && $type->slug == 'can-ho') {
                                            $isSelectedByUrl = true;
                                        } elseif ($containsNhaNguyenCan && $type->slug == 'nha-nguyen-can') {
                                            $isSelectedByUrl = true;
                                        } elseif ($containsPhongTro && $type->slug == 'nha-tro-phong-tro') {
                                            $isSelectedByUrl = true;
                                        } elseif ($containsMatBang && $type->slug == 'mat-bang') {
                                            $isSelectedByUrl = true;
                                        }

                                        // Kiểm tra theo $selectedType
                                        $isSelectedByType = (strcasecmp($selectedType, $type->slug) == 0);

                                        // Sử dụng một trong hai điều kiện
                                        $isSelected = $isSelectedByUrl || $isSelectedByType;
                                    }

                                    if ($isSelected) {
                                        $typeFound = true;
                                        echo '<pre style="display:none;">Match found for type: ' . $type->slug . '</pre>';
                                    }
                                ?>
                                <option value="<?php echo $type->slug; ?>" <?php echo $isSelected ? 'selected' : ''; ?>><?php echo htmlspecialchars($type->name); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- 3. Phường/Xã (Ward/Commune) -->
                        <div class="search-field ward-field">
                            <select class="form-select select2-ward" name="ward" id="searchWard">
                                <option value="">Chọn phường/xã</option>
                                <?php
                                echo '<pre style="display:none;">Selected Ward: "' . $selectedWard . '", Type found: ' . ($typeFound ? 'Yes' : 'No') . '</pre>';
                                $wardFound = false;
                                // Get current URL for pattern matching
                                $currentUrl = $_SERVER['REQUEST_URI'];
                                // Debug info (hidden)
                                echo '<pre style="display:none;">Current URL: ' . $currentUrl . '</pre>';
                                echo '<pre style="display:none;">Selected Ward from controller: "' . $selectedWard . '"</pre>';

                                foreach ($wards as $ward):
                                    // Check if ward slug matches selected ward
                                    $isSelectedByParam = (strcasecmp($selectedWard, $ward->slug) == 0);

                                    // Check if URL contains this ward's slug in specific patterns
                                    $wardSlug = $ward->slug;
                                    $containsWardSlug = false;

                                    // Check for patterns like cho-thue-can-ho-{ward} or cho-thue-nha-dat-{ward}
                                    if (preg_match('/cho-thue-[a-z0-9-]+-' . preg_quote($wardSlug) . '($|\/)/', $currentUrl)) {
                                        $containsWardSlug = true;
                                        echo '<pre style="display:none;">URL matches pattern cho-thue-type-' . $wardSlug . '</pre>';
                                    }
                                    // Check for patterns like cho-thue-nha-dat-tai-{ward}
                                    else if (preg_match('/cho-thue-nha-dat-tai-' . preg_quote($wardSlug) . '($|\/)/', $currentUrl)) {
                                        $containsWardSlug = true;
                                        echo '<pre style="display:none;">URL matches pattern cho-thue-nha-dat-tai-' . $wardSlug . '</pre>';
                                    }
                                    // Check for patterns like thue-nha-dat-{ward}
                                    else if (preg_match('/thue-nha-dat-' . preg_quote($wardSlug) . '($|\/)/', $currentUrl)) {
                                        $containsWardSlug = true;
                                        echo '<pre style="display:none;">URL matches pattern thue-nha-dat-' . $wardSlug . '</pre>';
                                    }
                                    // Check for the specific pattern in the user's example: cho-thue-can-ho-an-hai-bac/gia-5-7-trieu-dt-50-70m2-2pn
                                    else if (preg_match('/cho-thue-[a-z0-9-]+-' . preg_quote($wardSlug) . '\/gia-[0-9]+-[0-9]+-trieu/', $currentUrl)) {
                                        $containsWardSlug = true;
                                        echo '<pre style="display:none;">URL matches specific pattern with price and area: ' . $wardSlug . '</pre>';
                                    }
                                    // Special case for "an-hai-bac" in URL
                                    else if ($wardSlug === 'an-hai-bac' && strpos($currentUrl, 'an-hai-bac') !== false) {
                                        $containsWardSlug = true;
                                        echo '<pre style="display:none;">Special case: Found "an-hai-bac" in URL</pre>';
                                    }

                                    // If the ward slug is in the URL, we'll select it regardless of $selectedWard
                                    // This ensures that the ward in the URL takes precedence
                                    $isSelectedByUrl = $containsWardSlug;

                                    // Final selection logic
                                    $isSelected = $isSelectedByParam || $isSelectedByUrl;

                                    // Debug info if ward is found in URL (hidden)
                                    if ($containsWardSlug) {
                                        echo '<pre style="display:none;">URL contains ward: ' . $ward->name . ' (slug: ' . $ward->slug . ')</pre>';
                                    }

                                    if ($isSelected) {
                                        $wardFound = true;
                                        echo '<pre style="display:none;">Ward selected: ' . $ward->name . ' (slug: ' . $ward->slug . ')</pre>';
                                    }
                                ?>
                                <option value="<?php echo $ward->slug; ?>" <?php echo $isSelected ? 'selected' : ''; ?>><?php echo htmlspecialchars($ward->name); ?></option>
                                <?php endforeach; ?>
                                <?php echo '<pre style="display:none;">Ward found: ' . ($wardFound ? 'Yes' : 'No') . '</pre>'; ?>
                            </select>
                        </div>

                        <!-- 4. Mức giá (Price Range) -->
                        <div class="search-field price-field">
                            <select class="form-select" name="price" id="searchPrice">
                                <option value="">Chọn mức giá</option>
                                <option value="1-3" <?php echo (strcasecmp($selectedPrice, '1-3') == 0) ? 'selected' : ''; ?>>1-3 triệu</option>
                                <option value="3-5" <?php echo (strcasecmp($selectedPrice, '3-5') == 0) ? 'selected' : ''; ?>>3-5 triệu</option>
                                <option value="5-7" <?php echo (strcasecmp($selectedPrice, '5-7') == 0) ? 'selected' : ''; ?>>5-7 triệu</option>
                                <option value="7-10" <?php echo (strcasecmp($selectedPrice, '7-10') == 0) ? 'selected' : ''; ?>>7-10 triệu</option>
                                <option value="10-15" <?php echo (strcasecmp($selectedPrice, '10-15') == 0) ? 'selected' : ''; ?>>10-15 triệu</option>
                                <option value="15+" <?php echo (strcasecmp($selectedPrice, '15+') == 0) ? 'selected' : ''; ?>>Trên 15 triệu</option>
                            </select>
                        </div>

                        <!-- 5. Bộ lọc nâng cao (Advanced Filters Button) -->
                        <button type="button" class="advanced-filters-btn" id="advancedFiltersBtn">
                            <i class="bi bi-sliders"></i>
                            <span>Bộ lọc nâng cao</span>
                            <?php
                            $activeFiltersCount = count(array_filter([$selectedBedrooms, $selectedBathrooms, $selectedArea, $selectedDirection]));
                            if ($activeFiltersCount > 0):
                            ?>
                            <span class="badge bg-primary ms-1" id="advancedFiltersBadge"><?php echo $activeFiltersCount; ?></span>
                            <?php endif; ?>
                        </button>

                        <!-- 6. Tìm kiếm (Search Button) -->
                        <button type="submit" class="search-btn" id="searchPageBtn">
                            <i class="bi bi-search"></i>
                            <span>Tìm kiếm</span>
                        </button>
                    </div>
                </div>

                <!-- Hidden Advanced Filter Fields (for form submission) -->
                <input type="hidden" name="bedrooms" id="searchBedrooms" value="<?php echo htmlspecialchars($selectedBedrooms); ?>">
                <input type="hidden" name="bathrooms" id="searchBathrooms" value="<?php echo htmlspecialchars($selectedBathrooms); ?>">
                <input type="hidden" name="area" id="searchArea" value="<?php echo htmlspecialchars($selectedArea); ?>">
                <input type="hidden" name="direction" id="searchDirection" value="<?php echo htmlspecialchars($selectedDirection); ?>">
            </div>
        </form>


    </div>
</section>

<!-- Mobile/Tablet Search Section -->
<section class="mobile-search-section bg-white py-2 d-lg-none">
    <div class="container">
        <!-- Mobile Search Form -->
        <form class="mobile-search-form" action="/thuenhadanang/search" method="get" id="mobileSearchForm">
            <div class="mobile-search-container">
                <!-- Single Row Layout: Keyword + Filter Button + Search Button -->
                <div class="mobile-search-row">
                    <!-- 1. Keyword Field -->
                    <div class="mobile-search-field keyword-field">
                        <input type="text" class="form-control mobile-search-input" name="keyword"
                               placeholder="Tìm kiếm địa điểm, tên đường..."
                               value="<?php echo htmlspecialchars($keyword); ?>" id="mobileSearchKeyword">
                    </div>

                    <!-- 2. Filter Button -->
                    <button type="button" class="mobile-filter-btn" id="mobileFilterBtn">
                        <i class="bi bi-sliders"></i>
                        <span class="filter-text">Lọc</span>
                        <?php
                        $mobileActiveFiltersCount = count(array_filter([$selectedType, $selectedWard, $selectedPrice, $selectedBedrooms, $selectedBathrooms, $selectedArea, $selectedDirection]));
                        if ($mobileActiveFiltersCount > 0):
                        ?>
                        <span class="badge bg-primary ms-1" id="mobileFiltersBadge"><?php echo $mobileActiveFiltersCount; ?></span>
                        <?php endif; ?>
                    </button>

                    <!-- 3. Search Button -->
                    <button type="submit" class="mobile-search-btn" id="mobileSearchBtn">
                        <i class="bi bi-search"></i>
                    </button>
                </div>

                <!-- Hidden Fields for Mobile Form Submission -->
                <input type="hidden" name="type" id="mobileSearchType" value="<?php echo htmlspecialchars($selectedType); ?>">
                <input type="hidden" name="ward" id="mobileSearchWard" value="<?php echo htmlspecialchars($selectedWard); ?>">
                <input type="hidden" name="price" id="mobileSearchPrice" value="<?php echo htmlspecialchars($selectedPrice); ?>">
                <input type="hidden" name="bedrooms" id="mobileSearchBedrooms" value="<?php echo htmlspecialchars($selectedBedrooms); ?>">
                <input type="hidden" name="bathrooms" id="mobileSearchBathrooms" value="<?php echo htmlspecialchars($selectedBathrooms); ?>">
                <input type="hidden" name="area" id="mobileSearchArea" value="<?php echo htmlspecialchars($selectedArea); ?>">
                <input type="hidden" name="direction" id="mobileSearchDirection" value="<?php echo htmlspecialchars($selectedDirection); ?>">
            </div>
        </form>
    </div>
</section>

<!-- Main Content Section -->
<section class="main-content-section" data-server-rendered="true">
    <div class="container">
        <!-- Results Header -->
        <div class="results-header mb-4">
            <div class="results-info">
                <h1 class="results-title"><?php echo $dynamicTitle; ?></h1>
                <p class="results-count">Tìm thấy <?php echo count($properties); ?> bất động sản phù hợp</p>
            </div>
            <div class="results-sort">
                <span class="sort-label">Sắp xếp:</span>
                <select class="form-select sort-select" onchange="window.location.href=this.value">
                    <option value="<?php echo $this->buildSortUrl('default'); ?>" <?php echo ($selectedSort == 'default') ? 'selected' : ''; ?>>Phổ biến nhất</option>
                    <option value="<?php echo $this->buildSortUrl('price_asc'); ?>" <?php echo ($selectedSort == 'price_asc') ? 'selected' : ''; ?>>Giá thấp đến cao</option>
                    <option value="<?php echo $this->buildSortUrl('price_desc'); ?>" <?php echo ($selectedSort == 'price_desc') ? 'selected' : ''; ?>>Giá cao đến thấp</option>
                    <option value="<?php echo $this->buildSortUrl('area_asc'); ?>" <?php echo ($selectedSort == 'area_asc') ? 'selected' : ''; ?>>Diện tích nhỏ đến lớn</option>
                    <option value="<?php echo $this->buildSortUrl('area_desc'); ?>" <?php echo ($selectedSort == 'area_desc') ? 'selected' : ''; ?>>Diện tích lớn đến nhỏ</option>
                </select>
            </div>
        </div>

        <?php if (empty($properties)): ?>
            <div class="no-results-container">
                <div class="no-results-content">
                    <div class="no-results-icon">
                        <i class="bi bi-house-x"></i>
                    </div>
                    <h3 class="no-results-title">Không tìm thấy kết quả phù hợp</h3>
                    <p class="no-results-text">Không tìm thấy bất động sản phù hợp với tiêu chí tìm kiếm của bạn.</p>

                    <?php if (!empty($selectedType) && !empty($selectedWard)): ?>
                    <div class="suggestions">
                        <p>Bạn có thể thử:</p>
                        <ul>
                            <li><a href="/thuenhadanang/cho-thue-<?php echo $selectedType; ?>">Xem tất cả <?php echo strtolower($propertyTypeName); ?> cho thuê</a></li>
                            <li><a href="/thuenhadanang/cho-thue-nha-dat-tai-<?php echo $selectedWard; ?>">Xem tất cả bất động sản tại <?php echo $selectedWard; ?></a></li>
                            <li><a href="/thuenhadanang/cho-thue-nha-dat">Xem tất cả bất động sản cho thuê</a></li>
                        </ul>
                    </div>
                    <?php elseif (!empty($selectedWard)): ?>
                    <div class="suggestions">
                        <p>Hiện tại chưa có bất động sản nào tại khu vực này. Vui lòng thử tìm kiếm ở khu vực khác hoặc
                        <a href="/thuenhadanang/cho-thue-nha-dat">xem tất cả bất động sản</a>.</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <div class="properties-grid">
                <?php foreach ($properties as $property): ?>
                    <?php
                    // Xử lý hình ảnh
                    $images = json_decode($property->images, true);
                    $imagePath = !empty($images) ? '/thuenhadanang/public/uploads/properties/' . $images[0] : '/thuenhadanang/public/images/no-image.jpg';

                    // Xử lý giá
                    $pricePeriod = '';
                    switch ($property->price_period) {
                        case 'month':
                            $pricePeriod = 'tháng';
                            break;
                        case 'quarter':
                            $pricePeriod = 'quý';
                            break;
                        case 'year':
                            $pricePeriod = 'năm';
                            break;
                        default:
                            $pricePeriod = 'tháng';
                    }

                    // Format giá
                    $formattedPrice = number_format($property->price / 1000000, 1);
                    $formattedPrice = rtrim(rtrim($formattedPrice, '0'), '.'); // Loại bỏ số 0 và dấu . ở cuối
                    $priceDisplay = $formattedPrice . ' triệu/' . $pricePeriod;

                    ?>
                    <div class="property-card" data-featured="<?php echo $property->featured ? 'true' : 'false'; ?>"
                         data-new="<?php echo (strtotime($property->created_at) > strtotime('-7 days')) ? 'true' : 'false'; ?>">
                        <div class="property-image-container">
                            <a href="/thuenhadanang/<?php echo $property->slug . '-' . $property->id; ?>">
                                <img src="<?php echo $imagePath; ?>" class="property-image" alt="<?php echo htmlspecialchars($property->title); ?>">
                            </a>

                            <!-- Property Badges -->
                            <div class="property-badges">
                                <?php if ($property->featured): ?>
                                <span class="property-badge featured-badge">Nổi bật</span>
                                <?php endif; ?>
                            </div>

                            <!-- Wishlist Button -->
                            <button class="wishlist-btn" data-property-id="<?php echo $property->id; ?>">
                                <i class="bi bi-heart"></i>
                            </button>
                        </div>

                        <div class="property-content">
                            <div class="property-header">
                                <h3 class="property-title">
                                    <a href="/thuenhadanang/<?php echo $property->slug . '-' . $property->id; ?>">
                                        <?php echo htmlspecialchars($property->title); ?>
                                    </a>
                                </h3>
                                <div class="property-location">
                                    <i class="bi bi-geo-alt"></i>
                                    <span><?php echo htmlspecialchars($property->ward_name); ?></span>
                                </div>
                            </div>

                            <div class="property-features">
                                <span class="feature-item">
                                    <i class="bi bi-rulers"></i>
                                    <?php echo $property->area; ?>m²
                                </span>
                                <span class="feature-item">
                                    <i class="bi bi-door-closed"></i>
                                    <?php echo $property->bedrooms; ?> PN
                                </span>
                                <span class="feature-item">
                                    <i class="bi bi-droplet"></i>
                                    <?php echo $property->bathrooms; ?> WC
                                </span>
                            </div>

                            <div class="property-footer">
                                <div class="property-price">
                                    <span class="current-price"><?php echo $priceDisplay; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>


    </div>
</section>

<!-- Include Mobile Search CSS -->
<link rel="stylesheet" href="/thuenhadanang/public/css/mobile-search.css">

<!-- Include Modern Interface JavaScript -->
<script src="/thuenhadanang/public/js/modern-interface.js"></script>

<!-- Include Advanced Filters Modal JavaScript -->
<script src="/thuenhadanang/public/js/advanced-filters-modal.js"></script>

<!-- Include AJAX Search JavaScript -->
<script src="/thuenhadanang/public/js/ajax-search.js"></script>

<!-- Include Mobile Search JavaScript -->
<script src="/thuenhadanang/public/js/mobile-search.js"></script>

<!-- Include AJAX Debug Script (development only) -->
<?php if ($_SERVER['HTTP_HOST'] === 'localhost' || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false): ?>
<script src="/thuenhadanang/public/js/ajax-debug.js"></script>
<?php endif; ?>

<!-- Initialize Advanced Filters Modal -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize modal and AJAX search integration
    console.log('🎛️ Initializing Advanced Filters Modal integration...');

    // Wait a bit for all scripts to load
    setTimeout(() => {
        // Check if AJAX search is available
        if (window.ajaxSearch && window.ajaxSearch.advancedFiltersModal) {
            console.log('✅ Advanced Filters Modal integration complete');
        } else {
            console.warn('⚠️ AJAX Search or Modal not fully initialized');
        }
    }, 500);
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Đặt giá trị cho các select box dựa trên query parameters và URL path
    function setSelectBoxesFromQueryParams() {
        const urlParams = new URLSearchParams(window.location.search);
        const currentUrl = window.location.pathname;

        // Parse price từ URL path (gia-tren-15-trieu -> 15+)
        function parsePriceFromUrl(url) {
            // Tìm pattern gia-xxx trong URL với boundary để tránh match quá nhiều
            const priceMatch = url.match(/gia-((?:[a-z0-9]+-)*[a-z0-9]*trieu)(?:-|$|\/)/);
            if (priceMatch) {
                const priceValue = priceMatch[1];
                console.log('Found price in URL:', priceValue);

                // Chuyển đổi từ URL format sang dropdown value
                if (priceValue === '1-3-trieu') {
                    return '1-3';
                } else if (priceValue === '3-5-trieu') {
                    return '3-5';
                } else if (priceValue === '5-7-trieu') {
                    return '5-7';
                } else if (priceValue === '7-10-trieu') {
                    return '7-10';
                } else if (priceValue === '10-15-trieu') {
                    return '10-15';
                } else if (priceValue === 'tren-15-trieu') {
                    return '15+';
                } else if (priceValue.match(/([0-9]+)-([0-9]+)-trieu/)) {
                    const customMatch = priceValue.match(/([0-9]+)-([0-9]+)-trieu/);
                    return customMatch[1] + '-' + customMatch[2];
                }
            }
            return null;
        }

        // Parse area từ URL path (dt-tren-90m2 -> 90+)
        function parseAreaFromUrl(url) {
            // Tìm pattern dt-xxx trong URL với boundary để tránh match quá nhiều
            const areaMatch = url.match(/dt-((?:[a-z0-9]+-)*[a-z0-9]*m2)(?:-|$|\/)/);
            if (areaMatch) {
                const areaValue = areaMatch[1];
                console.log('Found area in URL:', areaValue);

                // Chuyển đổi từ URL format sang dropdown value
                if (areaValue === '0-20m2') {
                    return '0-20';
                } else if (areaValue === '20-30m2') {
                    return '20-30';
                } else if (areaValue === '30-50m2') {
                    return '30-50';
                } else if (areaValue === '50-70m2') {
                    return '50-70';
                } else if (areaValue === '70-90m2') {
                    return '70-90';
                } else if (areaValue === 'tren-90m2') {
                    return '90+';
                } else if (areaValue.match(/([0-9]+)-([0-9]+)m2/)) {
                    const customMatch = areaValue.match(/([0-9]+)-([0-9]+)m2/);
                    return customMatch[1] + '-' + customMatch[2];
                }
            }
            return null;
        }

        // Parse bedrooms từ URL path (3pn -> 3, 4pn-tro-len -> 4+)
        function parseBedroomsFromUrl(url) {
            // Tìm pattern Xpn hoặc Xpn-tro-len trong URL với boundary
            const bedroomsMatch = url.match(/([0-9]+)pn(?:-tro-len)?(?:-|$|\/)/);
            if (bedroomsMatch) {
                const bedroomsValue = bedroomsMatch[0].replace(/(?:-|$|\/)$/, ''); // Remove trailing delimiter
                console.log('Found bedrooms in URL:', bedroomsValue);

                if (bedroomsValue === '4pn-tro-len') {
                    return '4+';
                } else if (bedroomsMatch[1]) {
                    return bedroomsMatch[1];
                }
            }
            return null;
        }

        // Đặt giá trị cho select box mức giá từ URL path
        const priceFromUrl = parsePriceFromUrl(currentUrl);
        if (priceFromUrl) {
            const priceSelect = document.getElementById('searchPrice');
            if (priceSelect && priceSelect.options && priceSelect.options.length > 0) {
                console.log('Setting price dropdown to:', priceFromUrl);
                for (let i = 0; i < priceSelect.options.length; i++) {
                    if (priceSelect.options[i].value === priceFromUrl) {
                        priceSelect.selectedIndex = i;
                        console.log('Price dropdown set successfully to option:', priceSelect.options[i].text);
                        break;
                    }
                }
            }
        }

        // Đặt giá trị cho select box diện tích từ URL path
        const areaFromUrl = parseAreaFromUrl(currentUrl);
        if (areaFromUrl) {
            const areaSelect = document.getElementById('searchArea');
            if (areaSelect && areaSelect.options && areaSelect.options.length > 0) {
                console.log('Setting area dropdown to:', areaFromUrl);
                for (let i = 0; i < areaSelect.options.length; i++) {
                    if (areaSelect.options[i].value === areaFromUrl) {
                        areaSelect.selectedIndex = i;
                        console.log('Area dropdown set successfully to option:', areaSelect.options[i].text);
                        break;
                    }
                }
            }
        }

        // Đặt giá trị cho select box phòng ngủ từ URL path
        const bedroomsFromUrl = parseBedroomsFromUrl(currentUrl);
        if (bedroomsFromUrl) {
            const bedroomsSelect = document.getElementById('searchBedrooms');
            if (bedroomsSelect && bedroomsSelect.options && bedroomsSelect.options.length > 0) {
                console.log('Setting bedrooms dropdown to:', bedroomsFromUrl);
                for (let i = 0; i < bedroomsSelect.options.length; i++) {
                    if (bedroomsSelect.options[i].value === bedroomsFromUrl) {
                        bedroomsSelect.selectedIndex = i;
                        console.log('Bedrooms dropdown set successfully to option:', bedroomsSelect.options[i].text);
                        break;
                    }
                }
            }
        }

        // Đặt giá trị cho select box phòng tắm
        if (urlParams.has('bathrooms')) {
            const bathroomsValue = urlParams.get('bathrooms');
            const bathroomsSelect = document.getElementById('searchBathrooms');
            if (bathroomsSelect && bathroomsSelect.options && bathroomsSelect.options.length > 0) {
                for (let i = 0; i < bathroomsSelect.options.length; i++) {
                    if (bathroomsSelect.options[i].value === bathroomsValue) {
                        bathroomsSelect.selectedIndex = i;
                        break;
                    }
                }
            }
        }

        // Đặt giá trị cho select box hướng nhà
        if (urlParams.has('direction')) {
            const directionValue = urlParams.get('direction');
            const directionSelect = document.getElementById('searchDirection');
            if (directionSelect && directionSelect.options && directionSelect.options.length > 0) {
                for (let i = 0; i < directionSelect.options.length; i++) {
                    if (directionSelect.options[i].value === directionValue) {
                        directionSelect.selectedIndex = i;
                        break;
                    }
                }
            }
        }
    }

    // Gọi hàm để đặt giá trị cho các select box khi trang được tải
    // Delay để đảm bảo tất cả elements đã được load
    setTimeout(() => {
        setSelectBoxesFromQueryParams();
    }, 100);

    // Bỏ traditional search - chỉ dùng AJAX search system
    // Tất cả search logic được xử lý bởi ajax-search.js





    // Note: Search button được xử lý bởi AJAX Search system (ajax-search.js)
    // Không cần thêm event listener ở đây để tránh conflict
});
</script>

<!-- Mobile Filters Modal -->
<div class="modal fade" id="mobileFiltersModal" tabindex="-1" aria-labelledby="mobileFiltersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mobileFiltersModalLabel">
                    <i class="bi bi-sliders me-2"></i>Bộ lọc tìm kiếm
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="mobileFiltersForm">
                    <div class="row g-3">
                        <!-- Property Type -->
                        <div class="col-12">
                            <label for="mobileFilterType" class="form-label">
                                <i class="bi bi-house me-1"></i>Loại hình bất động sản
                            </label>
                            <select class="form-select" id="mobileFilterType" name="type">
                                <option value="">Tất cả loại hình</option>
                                <?php foreach ($propertyTypes as $type): ?>
                                <option value="<?php echo $type->slug; ?>" <?php echo ($selectedType == $type->slug) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($type->name); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Ward/Commune -->
                        <div class="col-12">
                            <label for="mobileFilterWard" class="form-label">
                                <i class="bi bi-geo-alt me-1"></i>Phường/Xã
                            </label>
                            <select class="form-select mobile-ward-select" id="mobileFilterWard" name="ward">
                                <option value="">Tất cả phường/xã</option>
                                <?php foreach ($wards as $ward): ?>
                                <option value="<?php echo $ward->slug; ?>" <?php echo ($selectedWard == $ward->slug) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($ward->name); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Price Range -->
                        <div class="col-12">
                            <label for="mobileFilterPrice" class="form-label">
                                <i class="bi bi-currency-dollar me-1"></i>Mức giá
                            </label>
                            <select class="form-select" id="mobileFilterPrice" name="price">
                                <option value="">Tất cả mức giá</option>
                                <option value="1-3" <?php echo ($selectedPrice == '1-3') ? 'selected' : ''; ?>>1-3 triệu</option>
                                <option value="3-5" <?php echo ($selectedPrice == '3-5') ? 'selected' : ''; ?>>3-5 triệu</option>
                                <option value="5-7" <?php echo ($selectedPrice == '5-7') ? 'selected' : ''; ?>>5-7 triệu</option>
                                <option value="7-10" <?php echo ($selectedPrice == '7-10') ? 'selected' : ''; ?>>7-10 triệu</option>
                                <option value="10-15" <?php echo ($selectedPrice == '10-15') ? 'selected' : ''; ?>>10-15 triệu</option>
                                <option value="15+" <?php echo ($selectedPrice == '15+') ? 'selected' : ''; ?>>Trên 15 triệu</option>
                            </select>
                        </div>

                        <!-- Advanced Filters Row -->
                        <div class="col-6">
                            <label for="mobileFilterBedrooms" class="form-label">
                                <i class="bi bi-door-closed me-1"></i>Phòng ngủ
                            </label>
                            <select class="form-select" id="mobileFilterBedrooms" name="bedrooms">
                                <option value="">Tất cả</option>
                                <option value="1" <?php echo ($selectedBedrooms == '1') ? 'selected' : ''; ?>>1 phòng</option>
                                <option value="2" <?php echo ($selectedBedrooms == '2') ? 'selected' : ''; ?>>2 phòng</option>
                                <option value="3" <?php echo ($selectedBedrooms == '3') ? 'selected' : ''; ?>>3 phòng</option>
                                <option value="4+" <?php echo ($selectedBedrooms == '4+') ? 'selected' : ''; ?>>4+ phòng</option>
                            </select>
                        </div>

                        <div class="col-6">
                            <label for="mobileFilterBathrooms" class="form-label">
                                <i class="bi bi-droplet me-1"></i>Phòng tắm
                            </label>
                            <select class="form-select" id="mobileFilterBathrooms" name="bathrooms">
                                <option value="">Tất cả</option>
                                <option value="1" <?php echo ($selectedBathrooms == '1') ? 'selected' : ''; ?>>1 phòng</option>
                                <option value="2" <?php echo ($selectedBathrooms == '2') ? 'selected' : ''; ?>>2 phòng</option>
                                <option value="3" <?php echo ($selectedBathrooms == '3') ? 'selected' : ''; ?>>3 phòng</option>
                                <option value="4+" <?php echo ($selectedBathrooms == '4+') ? 'selected' : ''; ?>>4+ phòng</option>
                            </select>
                        </div>

                        <!-- Area and Direction Row -->
                        <div class="col-6">
                            <label for="mobileFilterArea" class="form-label">
                                <i class="bi bi-rulers me-1"></i>Diện tích
                            </label>
                            <select class="form-select" id="mobileFilterArea" name="area">
                                <option value="">Tất cả</option>
                                <option value="0-20" <?php echo ($selectedArea == '0-20') ? 'selected' : ''; ?>>Dưới 20m²</option>
                                <option value="20-30" <?php echo ($selectedArea == '20-30') ? 'selected' : ''; ?>>20-30m²</option>
                                <option value="30-50" <?php echo ($selectedArea == '30-50') ? 'selected' : ''; ?>>30-50m²</option>
                                <option value="50-70" <?php echo ($selectedArea == '50-70') ? 'selected' : ''; ?>>50-70m²</option>
                                <option value="70-90" <?php echo ($selectedArea == '70-90') ? 'selected' : ''; ?>>70-90m²</option>
                                <option value="90+" <?php echo ($selectedArea == '90+') ? 'selected' : ''; ?>>Trên 90m²</option>
                            </select>
                        </div>

                        <div class="col-6">
                            <label for="mobileFilterDirection" class="form-label">
                                <i class="bi bi-compass me-1"></i>Hướng nhà
                            </label>
                            <select class="form-select" id="mobileFilterDirection" name="direction">
                                <option value="">Tất cả hướng</option>
                                <?php foreach ($directions as $slug => $name): ?>
                                <option value="<?php echo $slug; ?>" <?php echo ($selectedDirection == $slug) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($name); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" id="mobileFiltersClear">
                    <i class="bi bi-arrow-clockwise me-1"></i>Xóa bộ lọc
                </button>
                <button type="button" class="btn btn-primary" id="mobileFiltersApply">
                    <i class="bi bi-check-lg me-1"></i>Áp dụng
                </button>
            </div>
        </div>
    </div>
</div>

