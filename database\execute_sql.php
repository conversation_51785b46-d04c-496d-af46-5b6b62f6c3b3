<?php
// <PERSON><PERSON><PERSON> to execute SQL file

// Include database connection
require_once __DIR__ . '/../app/libraries/Database.php';

// Read SQL file
$sqlFile = __DIR__ . '/update_properties_status.sql';
$sql = file_get_contents($sqlFile);

// Split SQL statements
$statements = explode(';', $sql);

// Initialize database connection
$db = new Database();

// Execute each statement
$success = true;
$errors = [];

foreach ($statements as $statement) {
    $statement = trim($statement);

    if (!empty($statement)) {
        try {
            $db->query($statement);
            $result = $db->execute();

            if (!$result) {
                $success = false;
                $errors[] = "Error executing: $statement";
            }
        } catch (Exception $e) {
            $success = false;
            $errors[] = "Exception: " . $e->getMessage() . " in statement: $statement";
        }
    }
}

// Output results
if ($success) {
    echo "SQL executed successfully!\n";
} else {
    echo "Errors occurred:\n";
    foreach ($errors as $error) {
        echo "- $error\n";
    }
}
