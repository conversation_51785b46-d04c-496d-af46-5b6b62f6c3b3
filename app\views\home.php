<section class="text-white py-5 hp_top_search">
    <div class="container">
        <h1 class="mb-5">T<PERSON><PERSON> kiếm cho thuê nhà tại Đ<PERSON> Nẵng cập nhật tháng <?php echo date('m/Y'); ?></h1>
        <!-- Desktop Search Form -->
        <form class="search-form d-none d-md-block" action="/thuenhadanang/search" method="get" id="desktopSearchForm">
            <div class="search-container">
                <input type="text" class="form-control search-address" name="keyword" placeholder="Nhập tên đường...">
                <select class="form-select" name="type" id="desktopType">
                    <option value="">Lo<PERSON><PERSON> hình</option>
                    <?php foreach ($propertyTypes as $type): ?>
                    <option value="<?php echo $type->slug; ?>"><?php echo htmlspecialchars($type->name); ?></option>
                    <?php endforeach; ?>
                </select>
                <select class="form-select select2-ward" name="ward" id="desktopWard">
                    <option value="">Phường/Xã</option>
                    <?php foreach ($wards as $ward): ?>
                    <option value="<?php echo $ward->slug; ?>"><?php echo htmlspecialchars($ward->name); ?></option>
                    <?php endforeach; ?>
                </select>
                <select class="form-select" name="price" id="desktopPrice">
                    <option value="">Mức giá</option>
                    <option value="1-3">1-3 triệu</option>
                    <option value="3-5">3-5 triệu</option>
                    <option value="5-7">5-7 triệu</option>
                    <option value="7-10">7-10 triệu</option>
                    <option value="10-15">10-15 triệu</option>
                    <option value="15+">Trên 15 triệu</option>
                </select>
                <button type="submit" class="btn btn-warning" id="desktopSearchBtn">Tìm kiếm</button>
            </div>
        </form>

        <!-- Mobile Search Form -->
        <form class="mobile-search-form d-md-none" action="/thuenhadanang/search" method="get" id="mobileSearchForm">
            <!-- Main Search Row -->
            <div class="mobile-search-main">
                <!-- Tên đường -->
                <div class="search-input-wrapper">
                    <input type="text" class="form-control search-address" name="keyword" placeholder="Nhập tên đường..." id="mobileKeyword">
                </div>

                <!-- Loại hình -->
                <div class="mobile-search-row">
                    <select class="form-select" name="type" id="mobileType">
                        <option value="">Loại hình</option>
                        <?php foreach ($propertyTypes as $type): ?>
                        <option value="<?php echo $type->slug; ?>"><?php echo htmlspecialchars($type->name); ?></option>
                        <?php endforeach; ?>
                    </select>
                    <button type="button" class="btn btn-filter" data-bs-toggle="collapse" data-bs-target="#mobileFilter">
                        <i class="bi bi-sliders"></i>
                    </button>
                </div>
            </div>

            <!-- Collapsible Filter Section -->
            <div class="collapse" id="mobileFilter">
                <div class="mobile-filter-section">
                    <!-- Phường/xã -->
                    <div class="filter-row mb-2">
                        <select class="form-select select2-ward" name="ward" id="mobileWard">
                            <option value="">Phường/Xã</option>
                            <?php foreach ($wards as $ward): ?>
                            <option value="<?php echo $ward->slug; ?>"><?php echo htmlspecialchars($ward->name); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <!-- Mức giá -->
                    <div class="filter-row">
                        <select class="form-select" name="price" id="mobilePrice">
                            <option value="">Mức giá</option>
                            <option value="1-3">1-3 triệu</option>
                            <option value="3-5">3-5 triệu</option>
                            <option value="5-7">5-7 triệu</option>
                            <option value="7-10">7-10 triệu</option>
                            <option value="10-15">10-15 triệu</option>
                            <option value="15+">Trên 15 triệu</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Search Button -->
            <button type="submit" class="btn btn-warning w-100 mt-3" id="mobileSearchBtn">Tìm kiếm</button>
        </form>
    </div>
</section>

<section class="container my-3">
    <div class="property-tabs-container">
        <!-- Tab Navigation -->
        <ul class="nav nav-tabs property-tabs mb-4" id="propertyTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="featured-tab" data-bs-toggle="tab" data-bs-target="#featured-tab-pane"
                        type="button" role="tab" aria-controls="featured-tab-pane" aria-selected="true"
                        data-type="featured">Bất động sản nổi bật</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="apartment-tab" data-bs-toggle="tab" data-bs-target="#apartment-tab-pane"
                        type="button" role="tab" aria-controls="apartment-tab-pane" aria-selected="false"
                        data-type="can-ho">Căn hộ</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="room-tab" data-bs-toggle="tab" data-bs-target="#room-tab-pane"
                        type="button" role="tab" aria-controls="room-tab-pane" aria-selected="false"
                        data-type="nha-tro-phong-tro">Phòng trọ</button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="propertyTabsContent">
            <!-- Featured Properties Tab -->
            <div class="tab-pane fade show active" id="featured-tab-pane" role="tabpanel" aria-labelledby="featured-tab" tabindex="0">
                <div class="row property-list" id="featured-properties">
                    <div class="col-12 text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Đang tải...</span>
                        </div>
                        <p class="mt-2">Đang tải dữ liệu...</p>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <a href="/thuenhadanang/cho-thue-nha-dat" class="btn btn-outline-primary">Xem tất cả</a>
                </div>
            </div>

            <!-- Apartments Tab -->
            <div class="tab-pane fade" id="apartment-tab-pane" role="tabpanel" aria-labelledby="apartment-tab" tabindex="0">
                <div class="row property-list" id="apartment-properties">
                    <!-- Content will be loaded via AJAX -->
                </div>
                <div class="text-center mt-3">
                    <a href="/thuenhadanang/cho-thue-can-ho" class="btn btn-outline-primary">Xem tất cả</a>
                </div>
            </div>

            <!-- Rooms Tab -->
            <div class="tab-pane fade" id="room-tab-pane" role="tabpanel" aria-labelledby="room-tab" tabindex="0">
                <div class="row property-list" id="room-properties">
                    <!-- Content will be loaded via AJAX -->
                </div>
                <div class="text-center mt-3">
                    <a href="/thuenhadanang/cho-thue-nha-tro-phong-tro" class="btn btn-outline-primary">Xem tất cả</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Property Card Template (Hidden) -->
<template id="property-card-template">
    <div class="col-md-3 mb-4">
        <div class="card h-100">
            <a href="#" class="property-link text-decoration-none">
                <div class="card-img-wrapper">
                    <img src="" class="card-img-top property-image" alt="">
                    <div class="property-favorite">
                        <i class="bi bi-heart"></i>
                    </div>
                    <div class="property-badge featured" style="display: none;">
                        <i class="bi bi-star-fill me-1"></i> Nổi bật
                    </div>
                </div>
            </a>
            <div class="card-body">
                <h5 class="card-title">
                    <a href="#" class="property-link property-title text-decoration-none">Tiêu đề bất động sản</a>
                </h5>
                <div class="property-location">
                    <i class="bi bi-geo-alt-fill"></i>
                    <span class="property-ward">Phường</span>
                </div>
                <div class="property-features">
                    <div class="feature-item">
                        <i class="bi bi-rulers"></i>
                        <span class="property-area">50m²</span>
                    </div>
                    <div class="feature-item">
                        <i class="bi bi-door-closed"></i>
                        <span class="property-bedrooms">2 PN</span>
                    </div>
                    <div class="feature-item">
                        <i class="bi bi-water"></i>
                        <span class="property-bathrooms">2 WC</span>
                    </div>
                </div>
                <div class="property-price">
                    7.5 triệu/tháng
                </div>
            </div>
        </div>
    </div>
</template>

<script>
// Mã JavaScript cho trang chủ đã được di chuyển vào file home.js
// File này sẽ được tải sau khi jQuery đã sẵn sàng
</script>
