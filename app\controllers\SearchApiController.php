<?php
require_once BASE_PATH . '/app/models/Property.php';
require_once BASE_PATH . '/app/models/PropertyType.php';
require_once BASE_PATH . '/app/models/Ward.php';

class SearchApiController extends BaseController {
    private $propertyModel;
    private $propertyTypeModel;
    private $wardModel;
    private $urlHandler;

    public function __construct() {
        $this->propertyModel = new Property();
        $this->propertyTypeModel = new PropertyType();
        $this->wardModel = new Ward();

        // Load UrlHandler for centralized URL processing
        require_once BASE_PATH . '/app/libraries/UrlHandler.php';
        $this->urlHandler = new UrlHandler();
    }

    /**
     * AJAX endpoint for search functionality
     * Returns JSON response with properties and metadata
     */
    public function ajaxSearch() {
        // Set JSON header
        header('Content-Type: application/json');

        try {
            // Get search parameters from request
            $params = $this->getSearchParams();

            // Log the search request
            error_log('AJAX Search Request: ' . json_encode($params));

            // Perform search using existing logic
            $searchResult = $this->performSearch($params);

            // Format response
            $response = [
                'success' => true,
                'data' => [
                    'properties' => $this->formatProperties($searchResult['properties']),
                    'count' => count($searchResult['properties']),
                    'metadata' => [
                        'title' => $searchResult['title'],
                        'selectedFilters' => $params,
                        'url' => $this->buildSearchUrl($params)
                    ]
                ],
                'timestamp' => time()
            ];

            echo json_encode($response);

        } catch (Exception $e) {
            error_log('AJAX Search Error: ' . $e->getMessage());

            $response = [
                'success' => false,
                'error' => [
                    'message' => 'Có lỗi xảy ra khi tìm kiếm',
                    'details' => $e->getMessage()
                ]
            ];

            echo json_encode($response);
        }
    }

    /**
     * Get and validate search parameters from request
     */
    private function getSearchParams() {
        return [
            'keyword' => isset($_GET['keyword']) ? trim($_GET['keyword']) : '',
            'type' => isset($_GET['type']) ? trim($_GET['type']) : '',
            'ward' => isset($_GET['ward']) ? trim($_GET['ward']) : '',
            'price' => isset($_GET['price']) ? trim($_GET['price']) : '',
            'area' => isset($_GET['area']) ? trim($_GET['area']) : '',
            'bedrooms' => isset($_GET['bedrooms']) ? trim($_GET['bedrooms']) : '',
            'bathrooms' => isset($_GET['bathrooms']) ? trim($_GET['bathrooms']) : '',
            'direction' => isset($_GET['direction']) ? trim($_GET['direction']) : '',
            'sort' => isset($_GET['sort']) ? trim($_GET['sort']) : 'default',
            'page' => isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1
        ];
    }

    /**
     * Perform search using existing SearchController logic
     */
    private function performSearch($params) {
        // Extract parameters
        $keyword = $params['keyword'];
        $type = $params['type'];
        $ward = $params['ward'];
        $price = $params['price'];
        $area = $params['area'];
        $bedrooms = $params['bedrooms'];
        $bathrooms = $params['bathrooms'];
        $direction = $params['direction'];
        $sort = $params['sort'];

        // Handle special case for "nha-dat"
        if ($type === 'nha-dat') {
            $type = '';
        }

        // Get property type name for title
        $propertyTypeName = 'nhà đất';
        if (!empty($type)) {
            $propertyType = $this->propertyTypeModel->getPropertyTypeBySlug($type);
            if ($propertyType) {
                $propertyTypeName = mb_strtolower($propertyType->name, 'UTF-8');
            }
        }

        // Get ward name for title
        $wardName = '';
        if (!empty($ward)) {
            $wardObj = $this->wardModel->getWardBySlug($ward);
            if ($wardObj) {
                $wardName = $wardObj->name;
            }
        }

        // Create dynamic title based on parameters
        $currentMonth = date('m');
        $currentYear = date('Y');

        if (!empty($type) && !empty($ward)) {
            $dynamicTitle = "Cho thuê {$propertyTypeName} tại {$wardName} Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        } else if (!empty($type)) {
            $dynamicTitle = "Cho thuê {$propertyTypeName} tại Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        } else if (!empty($ward)) {
            $dynamicTitle = "Cho thuê nhà đất tại {$wardName} Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        } else {
            $dynamicTitle = "Cho thuê nhà đất tại Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        }

        // Perform search using existing model methods
        if (!empty($keyword)) {
            $properties = $this->propertyModel->searchProperties(
                $keyword, $type, $ward, $price, $sort,
                $bedrooms, $bathrooms, $direction, $area
            );
        } else {
            $properties = $this->propertyModel->getPropertiesByTypeAndWard(
                $type, $ward, $price, $area, $bedrooms,
                $sort, $bathrooms, $direction
            );
        }

        return [
            'properties' => $properties,
            'title' => $dynamicTitle,
            'propertyTypeName' => $propertyTypeName
        ];
    }

    /**
     * Format properties for JSON response
     */
    private function formatProperties($properties) {
        $formattedProperties = [];

        foreach ($properties as $property) {
            // Process images
            $images = json_decode($property->images, true);
            $imagePath = !empty($images)
                ? '/thuenhadanang/public/uploads/properties/' . $images[0]
                : '/thuenhadanang/public/images/no-image.jpg';

            // Process price
            $pricePeriod = '';
            switch ($property->price_period) {
                case 'month':
                    $pricePeriod = 'tháng';
                    break;
                case 'quarter':
                    $pricePeriod = 'quý';
                    break;
                case 'year':
                    $pricePeriod = 'năm';
                    break;
                default:
                    $pricePeriod = 'tháng';
            }

            // Format price
            $formattedPrice = number_format($property->price / 1000000, 1);
            $formattedPrice = rtrim(rtrim($formattedPrice, '0'), '.');
            $priceDisplay = $formattedPrice . ' triệu/' . $pricePeriod;

            // Check if property is new (within 7 days)
            $isNew = (strtotime($property->created_at) > strtotime('-7 days'));

            $formattedProperties[] = [
                'id' => $property->id,
                'title' => $property->title,
                'slug' => $property->slug,
                'price' => $property->price,
                'priceDisplay' => $priceDisplay,
                'area' => $property->area,
                'bedrooms' => $property->bedrooms,
                'bathrooms' => $property->bathrooms,
                'wardName' => $property->ward_name,
                'typeName' => $property->type_name,
                'imagePath' => $imagePath,
                'featured' => (bool)$property->featured,
                'isNew' => $isNew,
                'url' => '/thuenhadanang/' . $property->slug . '-' . $property->id,
                'createdAt' => $property->created_at
            ];
        }

        return $formattedProperties;
    }

    /**
     * Build search URL for the current parameters
     */
    private function buildSearchUrl($params) {
        // Use existing UrlHandler logic
        $urlParams = [
            'type' => $params['type'],
            'ward' => $params['ward'],
            'price' => $params['price'],
            'area' => $params['area'],
            'bedrooms' => $params['bedrooms'],
            'bathrooms' => $params['bathrooms'],
            'direction' => $params['direction']
        ];

        // Add additional params
        $additionalParams = [];
        if (!empty($params['keyword'])) {
            $additionalParams['keyword'] = $params['keyword'];
        }
        if (!empty($params['sort']) && $params['sort'] !== 'default') {
            $additionalParams['sort'] = $params['sort'];
        }

        if (!empty($additionalParams)) {
            $urlParams['additional_params'] = $additionalParams;
        }

        return $this->urlHandler->buildUrl($urlParams);
    }

    /**
     * Get filter options for dropdowns
     */
    public function getFilterOptions() {
        header('Content-Type: application/json');

        try {
            $propertyTypes = $this->propertyTypeModel->getAllPropertyTypes();
            $wards = $this->wardModel->getWardsByStatus(1);

            $response = [
                'success' => true,
                'data' => [
                    'propertyTypes' => array_map(function($type) {
                        return [
                            'id' => $type->id,
                            'name' => $type->name,
                            'slug' => $type->slug
                        ];
                    }, $propertyTypes),
                    'wards' => array_map(function($ward) {
                        return [
                            'id' => $ward->id,
                            'name' => $ward->name,
                            'slug' => $ward->slug
                        ];
                    }, $wards),
                    'directions' => [
                        'dong' => 'Đông',
                        'tay' => 'Tây',
                        'nam' => 'Nam',
                        'bac' => 'Bắc',
                        'dong-bac' => 'Đông Bắc',
                        'dong-nam' => 'Đông Nam',
                        'tay-bac' => 'Tây Bắc',
                        'tay-nam' => 'Tây Nam'
                    ],
                    'areas' => [
                        '0-20' => 'Dưới 20m²',
                        '20-30' => '20 - 30m²',
                        '30-50' => '30 - 50m²',
                        '50-70' => '50 - 70m²',
                        '70-90' => '70 - 90m²',
                        '90+' => 'Trên 90m²'
                    ]
                ]
            ];

            echo json_encode($response);

        } catch (Exception $e) {
            error_log('Get Filter Options Error: ' . $e->getMessage());

            $response = [
                'success' => false,
                'error' => [
                    'message' => 'Có lỗi xảy ra khi tải dữ liệu',
                    'details' => $e->getMessage()
                ]
            ];

            echo json_encode($response);
        }
    }
}
?>
