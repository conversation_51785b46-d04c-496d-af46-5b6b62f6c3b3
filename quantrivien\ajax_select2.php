<?php
// File xử lý AJAX cho Select2
// Đ<PERSON>m bảo không có output nào trước header
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Bắt đầu output buffering để kiểm soát output
ob_start();

// Kết nối database và models
require_once __DIR__ . '/../app/libraries/Database.php';
require_once __DIR__ . '/../app/models/User.php';
require_once __DIR__ . '/../app/models/Ward.php';

// Lấy action
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Xử lý các action
switch ($action) {
    case 'search_users':
        searchUsers();
        break;
    case 'search_wards':
        searchWards();
        break;
    default:
        // Trả về lỗi nếu không có action hợp lệ
        sendJsonResponse(['error' => 'Invalid action']);
        break;
}

/**
 * Tìm kiếm người dùng cho Select2
 */
function searchUsers() {
    // Lấy từ khóa tìm kiếm
    $keyword = isset($_GET['q']) ? trim($_GET['q']) : '';

    // Kết quả mặc định
    $results = [];

    // Nếu có từ khóa tìm kiếm, thực hiện tìm kiếm
    if (!empty($keyword) && strlen($keyword) >= 2) {
        try {
            // Khởi tạo model User
            $userModel = new User();

            // Tìm kiếm người dùng
            $users = $userModel->searchUsersForSelect2($keyword);

            // Thêm các người dùng vào kết quả
            foreach ($users as $user) {
                // Bỏ qua user ID 1 (Guest) vì sẽ được thêm tự động khi tick vào checkbox
                if ($user->id != 1) {
                    $results[] = [
                        'id' => $user->id,
                        'text' => $user->fullname . ' (' . $user->email . ')'
                    ];
                }
            }
        } catch (Exception $e) {
            // Ghi log lỗi
            error_log('Error in searchUsers: ' . $e->getMessage());
        }
    }

    // Trả về kết quả
    sendJsonResponse($results);
}

/**
 * Tìm kiếm phường/xã cho Select2
 */
function searchWards() {
    // Lấy từ khóa tìm kiếm
    $keyword = isset($_GET['q']) ? trim($_GET['q']) : '';

    // Kết quả mặc định
    $results = [];

    try {
        // Khởi tạo model Ward
        $wardModel = new Ward();

        // Tìm kiếm phường/xã
        if (!empty($keyword)) {
            $wards = $wardModel->searchWards($keyword);

            foreach ($wards as $ward) {
                $results[] = [
                    'id' => $ward->id,
                    'text' => $ward->name
                ];
            }
        } else {
            // Nếu không có từ khóa, lấy tất cả phường/xã
            $wards = $wardModel->getAllWards();

            foreach ($wards as $ward) {
                $results[] = [
                    'id' => $ward->id,
                    'text' => $ward->name
                ];
            }
        }
    } catch (Exception $e) {
        // Ghi log lỗi
        error_log('Error in searchWards: ' . $e->getMessage());
    }

    // Trả về kết quả
    sendJsonResponse($results);
}

/**
 * Gửi phản hồi JSON
 */
function sendJsonResponse($data) {
    // Xóa bất kỳ output nào trước khi gửi header
    ob_clean();

    // Trả về kết quả dạng JSON
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST');
    header('Access-Control-Allow-Headers: Content-Type');
    echo json_encode($data);
    exit;
}
