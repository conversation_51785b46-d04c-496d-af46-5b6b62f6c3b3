<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Advanced Filters Modal - <PERSON><PERSON><PERSON> Nẵng</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            padding: 2rem 0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .test-header {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-search-bar {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            margin-bottom: 2rem;
        }
        .demo-search-form {
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }
        .demo-search-field {
            flex: 1;
            min-width: 150px;
            padding: 12px 16px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
        }
        .demo-advanced-filters-btn {
            background: white;
            color: #6c757d;
            border: 1px solid #e9ecef;
            padding: 12px 16px;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            transition: all 0.2s ease;
            cursor: pointer;
            position: relative;
        }
        .demo-advanced-filters-btn:hover {
            background: #f8f9fa;
            border-color: #dee2e6;
            color: #495057;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .demo-search-btn {
            background: #228B22;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 20px;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .demo-search-btn:hover {
            background: #1e7b1e;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(34, 139, 34, 0.3);
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .btn-test {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .hidden-fields {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="mb-3">🎛️ Advanced Filters Modal Test</h1>
            <p class="mb-0">
                Test modal popup cho bộ lọc nâng cao theo phong cách Airbnb.
                Kiểm tra tất cả tính năng: mở modal, chọn filters, apply, clear all.
            </p>
        </div>

        <!-- Demo Search Bar -->
        <div class="demo-search-bar">
            <h3 class="mb-3">🔍 Demo Search Interface</h3>
            <form id="demoSearchForm" class="demo-search-form">
                <input type="text" class="demo-search-field" placeholder="Từ khóa tìm kiếm..." id="demoKeyword">
                <select class="demo-search-field" id="demoType">
                    <option value="">Loại nhà đất</option>
                    <option value="can-ho">Căn hộ</option>
                    <option value="nha-rieng">Nhà riêng</option>
                    <option value="phong-tro">Phòng trọ</option>
                </select>
                <select class="demo-search-field" id="demoWard">
                    <option value="">Phường/Xã</option>
                    <option value="hai-chau-1">Hải Châu 1</option>
                    <option value="thanh-khe">Thanh Khê</option>
                    <option value="son-tra">Sơn Trà</option>
                </select>
                <select class="demo-search-field" id="demoPrice">
                    <option value="">Mức giá</option>
                    <option value="0-5">Dưới 5 triệu</option>
                    <option value="5-7">5-7 triệu</option>
                    <option value="7-10">7-10 triệu</option>
                    <option value="15+">Trên 15 triệu</option>
                </select>
                
                <button type="button" class="demo-advanced-filters-btn" id="demoAdvancedFiltersBtn">
                    <i class="bi bi-sliders"></i>
                    <span>Bộ lọc nâng cao</span>
                    <span class="badge bg-primary ms-1" id="demoAdvancedFiltersBadge" style="display: none;">0</span>
                </button>
                
                <button type="button" class="demo-search-btn" id="demoSearchBtn">
                    <i class="bi bi-search"></i>
                    <span>Tìm kiếm</span>
                </button>

                <!-- Hidden Advanced Filter Fields -->
                <div class="hidden-fields">
                    <input type="hidden" name="bedrooms" id="demoBedrooms">
                    <input type="hidden" name="bathrooms" id="demoBathrooms">
                    <input type="hidden" name="area" id="demoArea">
                    <input type="hidden" name="direction" id="demoDirection">
                </div>
            </form>
        </div>

        <!-- Modal Functionality Tests -->
        <div class="test-section">
            <h3>🎛️ Modal Functionality Tests</h3>
            <p>Test các chức năng của Advanced Filters Modal</p>
            
            <div class="mb-3">
                <button class="btn btn-primary btn-test" onclick="testOpenModal()">
                    Test Open Modal
                </button>
                <button class="btn btn-primary btn-test" onclick="testFilterSelection()">
                    Test Filter Selection
                </button>
                <button class="btn btn-primary btn-test" onclick="testClearAllFilters()">
                    Test Clear All
                </button>
                <button class="btn btn-primary btn-test" onclick="testApplyFilters()">
                    Test Apply Filters
                </button>
                <button class="btn btn-secondary btn-test" onclick="testCloseModal()">
                    Test Close Modal
                </button>
            </div>

            <div id="modalTestResults"></div>
        </div>

        <!-- User Experience Tests -->
        <div class="test-section">
            <h3>👤 User Experience Tests</h3>
            <p>Test trải nghiệm người dùng với modal</p>
            
            <div class="mb-3">
                <button class="btn btn-success btn-test" onclick="testModalAnimations()">
                    Test Animations
                </button>
                <button class="btn btn-success btn-test" onclick="testMobileResponsive()">
                    Test Mobile Responsive
                </button>
                <button class="btn btn-success btn-test" onclick="testKeyboardNavigation()">
                    Test Keyboard Navigation
                </button>
                <button class="btn btn-success btn-test" onclick="testFilterBadgeUpdate()">
                    Test Filter Badge
                </button>
            </div>

            <div id="uxTestResults"></div>
        </div>

        <!-- Integration Tests -->
        <div class="test-section">
            <h3>🔧 Integration Tests</h3>
            <p>Test tích hợp với AJAX search</p>
            
            <div class="mb-3">
                <button class="btn btn-info btn-test" onclick="testAjaxIntegration()">
                    Test AJAX Integration
                </button>
                <button class="btn btn-info btn-test" onclick="testFormFieldSync()">
                    Test Form Field Sync
                </button>
                <button class="btn btn-info btn-test" onclick="testUrlUpdate()">
                    Test URL Update
                </button>
                <button class="btn btn-info btn-test" onclick="testSearchTrigger()">
                    Test Search Trigger
                </button>
            </div>

            <div id="integrationTestResults"></div>
        </div>

        <!-- Current Filter State -->
        <div class="test-section">
            <h3>📊 Current Filter State</h3>
            <div id="currentFilterState">
                <div class="alert alert-info">
                    <h5>🎯 Filter Values</h5>
                    <ul id="filterValuesList">
                        <li>Bedrooms: <span id="currentBedrooms">-</span></li>
                        <li>Bathrooms: <span id="currentBathrooms">-</span></li>
                        <li>Area: <span id="currentArea">-</span></li>
                        <li>Direction: <span id="currentDirection">-</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Modal JavaScript -->
    <script src="/thuenhadanang/public/js/advanced-filters-modal.js"></script>

    <script>
        // Global variables
        let demoModal = null;

        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            initializeDemo();
        });

        function initializeDemo() {
            // Create modal instance
            demoModal = new AdvancedFiltersModal();
            
            // Setup demo button
            const demoBtn = document.getElementById('demoAdvancedFiltersBtn');
            demoBtn.addEventListener('click', () => {
                demoModal.open();
            });

            // Setup apply callback
            demoModal.setOnApplyCallback((filters) => {
                updateDemoFormFields(filters);
                updateFilterBadge();
                updateCurrentFilterState();
                showResult('modalTestResults', 'Filters Applied', 'success', filters);
            });

            // Setup search button
            const searchBtn = document.getElementById('demoSearchBtn');
            searchBtn.addEventListener('click', () => {
                const allFilters = getAllDemoFilters();
                showResult('integrationTestResults', 'Search Triggered', 'success', allFilters);
            });

            console.log('🎛️ Demo initialized successfully');
        }

        // Utility functions
        function showResult(containerId, title, status, data, timing = null) {
            const container = document.getElementById(containerId);
            const statusClass = status === 'success' ? 'status-success' : 
                               status === 'error' ? 'status-error' : 'status-warning';
            
            const timingInfo = timing ? ` (${timing}ms)` : '';
            
            const resultHtml = `
                <div class="test-result">
                    <div class="mb-2">
                        <span class="status-indicator ${statusClass}"></span>
                        <strong>${title}</strong>${timingInfo}
                    </div>
                    <pre>${typeof data === 'object' ? JSON.stringify(data, null, 2) : data}</pre>
                </div>
            `;
            
            container.innerHTML += resultHtml;
            container.scrollTop = container.scrollHeight;
        }

        function updateDemoFormFields(filters) {
            document.getElementById('demoBedrooms').value = filters.bedrooms || '';
            document.getElementById('demoBathrooms').value = filters.bathrooms || '';
            document.getElementById('demoArea').value = filters.area || '';
            document.getElementById('demoDirection').value = filters.direction || '';
        }

        function updateFilterBadge() {
            const badge = document.getElementById('demoAdvancedFiltersBadge');
            const filters = getDemoAdvancedFilters();
            const activeCount = Object.values(filters).filter(v => v !== '').length;

            if (activeCount > 0) {
                badge.textContent = activeCount;
                badge.style.display = 'inline-flex';
            } else {
                badge.style.display = 'none';
            }
        }

        function getDemoAdvancedFilters() {
            return {
                bedrooms: document.getElementById('demoBedrooms').value,
                bathrooms: document.getElementById('demoBathrooms').value,
                area: document.getElementById('demoArea').value,
                direction: document.getElementById('demoDirection').value
            };
        }

        function getAllDemoFilters() {
            return {
                keyword: document.getElementById('demoKeyword').value,
                type: document.getElementById('demoType').value,
                ward: document.getElementById('demoWard').value,
                price: document.getElementById('demoPrice').value,
                ...getDemoAdvancedFilters()
            };
        }

        function updateCurrentFilterState() {
            const filters = getDemoAdvancedFilters();
            document.getElementById('currentBedrooms').textContent = filters.bedrooms || '-';
            document.getElementById('currentBathrooms').textContent = filters.bathrooms || '-';
            document.getElementById('currentArea').textContent = filters.area || '-';
            document.getElementById('currentDirection').textContent = filters.direction || '-';
        }

        // Test functions
        function testOpenModal() {
            if (demoModal) {
                demoModal.open();
                showResult('modalTestResults', 'Open Modal Test', 'success', {
                    message: 'Modal opened successfully',
                    isOpen: demoModal.isOpen
                });
            } else {
                showResult('modalTestResults', 'Open Modal Test', 'error', {
                    message: 'Modal not initialized'
                });
            }
        }

        function testCloseModal() {
            if (demoModal) {
                demoModal.close();
                showResult('modalTestResults', 'Close Modal Test', 'success', {
                    message: 'Modal closed successfully',
                    isOpen: demoModal.isOpen
                });
            }
        }

        function testFilterSelection() {
            if (demoModal) {
                // Simulate filter selection
                demoModal.setFilters({
                    bedrooms: '2',
                    bathrooms: '1',
                    area: '30-50',
                    direction: 'dong'
                });
                
                showResult('modalTestResults', 'Filter Selection Test', 'success', {
                    message: 'Filters set programmatically',
                    filters: demoModal.getFilters()
                });
            }
        }

        function testClearAllFilters() {
            if (demoModal) {
                demoModal.clearAllFilters();
                showResult('modalTestResults', 'Clear All Filters Test', 'success', {
                    message: 'All filters cleared',
                    filters: demoModal.getFilters()
                });
            }
        }

        function testApplyFilters() {
            if (demoModal) {
                // Set some filters first
                demoModal.setFilters({
                    bedrooms: '3',
                    bathrooms: '2',
                    area: '50-70'
                });
                
                // Apply filters
                demoModal.applyFilters();
                
                showResult('modalTestResults', 'Apply Filters Test', 'success', {
                    message: 'Filters applied and modal closed',
                    appliedFilters: getDemoAdvancedFilters()
                });
            }
        }

        function testModalAnimations() {
            showResult('uxTestResults', 'Modal Animations Test', 'warning', {
                message: 'Manual test required',
                instructions: [
                    '1. Click "Bộ lọc nâng cao" button',
                    '2. Observe smooth fade-in animation',
                    '3. Check backdrop blur effect',
                    '4. Verify modal scale animation',
                    '5. Test close animation'
                ]
            });
        }

        function testMobileResponsive() {
            showResult('uxTestResults', 'Mobile Responsive Test', 'warning', {
                message: 'Manual test required',
                instructions: [
                    '1. Open browser DevTools',
                    '2. Switch to mobile view (375px width)',
                    '3. Open modal and check layout',
                    '4. Verify buttons are accessible',
                    '5. Test touch interactions'
                ]
            });
        }

        function testKeyboardNavigation() {
            showResult('uxTestResults', 'Keyboard Navigation Test', 'warning', {
                message: 'Manual test required',
                instructions: [
                    '1. Open modal',
                    '2. Press ESC key to close',
                    '3. Use TAB to navigate between buttons',
                    '4. Use ENTER/SPACE to select filters',
                    '5. Verify focus indicators'
                ]
            });
        }

        function testFilterBadgeUpdate() {
            // Test badge update
            const testFilters = [
                { bedrooms: '2' },
                { bedrooms: '2', bathrooms: '1' },
                { bedrooms: '2', bathrooms: '1', area: '30-50' },
                {}
            ];

            testFilters.forEach((filters, index) => {
                setTimeout(() => {
                    updateDemoFormFields(filters);
                    updateFilterBadge();
                    
                    const activeCount = Object.values(filters).filter(v => v !== '').length;
                    showResult('uxTestResults', `Filter Badge Test ${index + 1}`, 'success', {
                        filters,
                        activeCount,
                        badgeVisible: activeCount > 0
                    });
                }, index * 1000);
            });
        }

        function testAjaxIntegration() {
            showResult('integrationTestResults', 'AJAX Integration Test', 'success', {
                message: 'Modal ready for AJAX integration',
                features: [
                    'Modal opens/closes correctly',
                    'Filters are applied to hidden form fields',
                    'Callback function triggers search',
                    'Form state is synchronized'
                ]
            });
        }

        function testFormFieldSync() {
            const testFilters = {
                bedrooms: '3',
                bathrooms: '2',
                area: '70-90',
                direction: 'nam'
            };

            updateDemoFormFields(testFilters);
            const syncedFilters = getDemoAdvancedFilters();

            showResult('integrationTestResults', 'Form Field Sync Test', 'success', {
                original: testFilters,
                synced: syncedFilters,
                isMatch: JSON.stringify(testFilters) === JSON.stringify(syncedFilters)
            });
        }

        function testUrlUpdate() {
            showResult('integrationTestResults', 'URL Update Test', 'success', {
                message: 'URL update functionality ready',
                note: 'URL will be updated when integrated with AJAX search',
                currentUrl: window.location.href
            });
        }

        function testSearchTrigger() {
            const allFilters = getAllDemoFilters();
            showResult('integrationTestResults', 'Search Trigger Test', 'success', {
                message: 'Search would be triggered with these filters',
                filters: allFilters,
                apiUrl: '/thuenhadanang/api/search?' + new URLSearchParams(allFilters).toString()
            });
        }

        // Auto-update filter state display
        setInterval(updateCurrentFilterState, 1000);
    </script>
</body>
</html>
