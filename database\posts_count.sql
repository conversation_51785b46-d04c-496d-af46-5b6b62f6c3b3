-- <PERSON><PERSON><PERSON> bảng cũ nếu tồn tại
DROP TABLE IF EXISTS `posts_count`;

-- T<PERSON><PERSON> bảng posts_count (theo dõi số lượng tin đã đăng)
CREATE TABLE `posts_count` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'ID người dùng',
  `user_package_id` int(11) NOT NULL COMMENT 'ID gói dịch vụ của người dùng',
  `property_id` int(11) NOT NULL COMMENT 'ID bất động sản',
  `posted_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'Thời gian đăng tin',
  `status` enum('active','deleted','expired') NOT NULL DEFAULT 'active' COMMENT 'Trạng thái tin đăng',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `user_package_id` (`user_package_id`),
  KEY `property_id` (`property_id`),
  CONSTRAINT `posts_count_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `posts_count_ibfk_2` FOREIGN KEY (`user_package_id`) REFERENCES `user_packages` (`id`) ON DELETE CASCADE,
  CONSTRAINT `posts_count_ibfk_3` FOREIGN KEY (`property_id`) REFERENCES `properties` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
