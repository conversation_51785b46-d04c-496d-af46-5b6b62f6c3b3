# Hướng dẫn hệ thống gói dịch vụ đăng tin

## Tổng quan

Hệ thống gói dịch vụ đăng tin cho phép quản lý số lượng tin đăng của người dùng thông qua các gói dịch vụ khác nhau. Hệ thống bao gồm:

- **<PERSON><PERSON><PERSON> bản**: <PERSON><PERSON><PERSON>h<PERSON>, 5 tin đăng, **VĨNH VIỄN** (không có thời hạn)
- **Gói Chuyên nghiệp**: 500,000 VNĐ, 20 tin đăng/tháng

## Cài đặt

### 1. Chạy migration database

```sql
-- Chạy file SQL để tạo các bảng cần thiết
mysql -u username -p database_name < database/install_package_system.sql
```

### 2. Cấu trúc database

Hệ thống tạo ra 3 bảng mới:

- `packages`: <PERSON><PERSON><PERSON> thông tin các gói dịch vụ
- `user_packages`: <PERSON><PERSON><PERSON> thông tin gói dịch vụ của từng người dùng
- `posts_count`: <PERSON>õ<PERSON> số lượng tin đã đăng

## Chức năng chính

### 1. Quản lý gói dịch vụ (Admin)

**Truy cập**: `/quantrivien/index.php?page=packages`

- Thêm/sửa/xóa gói dịch vụ
- Thiết lập gói mặc định
- Xem thống kê sử dụng

### 2. Quản lý gói của người dùng (Admin)

**Truy cập**: `/quantrivien/index.php?page=user-packages`

- Gán gói cho người dùng
- Cập nhật trạng thái gói
- Xem thống kê sử dụng

### 3. Trang bảng giá (Public)

**Truy cập**: `/thuenhadanang/pricing`

- Hiển thị so sánh các gói dịch vụ
- Thông tin liên hệ nâng cấp
- FAQ về gói dịch vụ

### 4. Dashboard người dùng

**Truy cập**: `/thuenhadanang/dashboard`

- Hiển thị thông tin gói hiện tại
- Số tin đã dùng/còn lại
- Ngày hết hạn
- Cảnh báo khi sắp hết lượt

## Quy trình hoạt động

### 1. Đăng ký người dùng mới

- Tự động gán gói Cơ bản (5 tin, **vĩnh viễn**)
- Gói được kích hoạt ngay lập tức
- Gói miễn phí không bao giờ hết hạn

### 2. Đăng tin bất động sản

- Kiểm tra quyền đăng tin trước khi cho phép
- Trừ 1 tin từ gói hiện tại khi đăng thành công
- Ghi nhận vào bảng `posts_count`

### 3. Hết hạn gói

- **Gói miễn phí**: Không bao giờ hết hạn, chỉ giới hạn số lượng tin
- **Gói trả phí**: Tự động cập nhật trạng thái `expired` khi hết hạn
- Không cho phép đăng tin mới khi hết lượt hoặc hết hạn
- Hiển thị thông báo phù hợp cho từng loại gói

## API và Methods

### UserPackage Model

```php
// Kiểm tra quyền đăng tin
$userPackageModel->canUserPost($userId);

// Lấy thông tin gói hiện tại
$userPackageModel->getUserPackageByUserId($userId);

// Cập nhật số tin đã sử dụng
$userPackageModel->updatePostsUsed($userPackageId, 1);

// Gán gói mặc định
$userPackageModel->assignDefaultPackageToUser($userId);
```

### Package Model

```php
// Lấy gói mặc định
$packageModel->getDefaultPackage();

// Lấy tất cả gói đang hoạt động
$packageModel->getActivePackages();

// Thống kê gói
$packageModel->getPackageStats();
```

## Cấu hình

### Thông tin liên hệ

Cập nhật thông tin Zalo trong các file:
- `app/views/pricing.php`
- `app/views/dashboard.php`

```php
// Thay đổi số Zalo
<a href="https://zalo.me/0944170391" target="_blank">
```

### Gói mặc định

Chỉ có thể có 1 gói mặc định tại một thời điểm. Khi tạo gói mới với `is_default = 1`, các gói khác sẽ tự động bỏ cờ mặc định.

### Logic gói miễn phí

- **Thời hạn**: Gói có `price = 0` sẽ được gán thời hạn `2099-12-31 23:59:59` (vĩnh viễn)
- **Kiểm tra hết hạn**: Hệ thống chỉ kiểm tra hết hạn cho gói có `price > 0`
- **Hiển thị**: Gói miễn phí hiển thị "Vĩnh viễn" thay vì ngày hết hạn
- **Cảnh báo**: Chỉ cảnh báo hết lượt tin, không cảnh báo hết hạn

## Bảo mật

### Kiểm tra quyền

- Tất cả chức năng admin yêu cầu đăng nhập admin
- Người dùng chỉ xem được gói của mình
- Kiểm tra quyền sở hữu khi đăng tin

### Validation

- Kiểm tra số lượng tin còn lại
- Kiểm tra thời hạn gói
- Validate dữ liệu đầu vào

## Troubleshooting

### Lỗi thường gặp

1. **Người dùng không thể đăng tin**
   - Kiểm tra gói dịch vụ trong database
   - Xem số tin còn lại
   - Kiểm tra ngày hết hạn

2. **Gói không được gán tự động**
   - Kiểm tra gói mặc định có tồn tại
   - Xem log lỗi trong User model

3. **Thống kê không chính xác**
   - Chạy lại script cập nhật gói hết hạn
   - Kiểm tra dữ liệu trong `posts_count`

### Debug

```php
// Kiểm tra gói của user
$userPackage = $userPackageModel->getUserPackageByUserId($userId);
var_dump($userPackage);

// Kiểm tra quyền đăng tin
$canPost = $userPackageModel->canUserPost($userId);
var_dump($canPost);
```

## Nâng cấp tương lai

### Tính năng có thể thêm

1. **Thanh toán online**
   - Tích hợp VNPay/MoMo
   - Tự động kích hoạt gói sau thanh toán

2. **Gói linh hoạt**
   - Gói theo tuần/quý/năm
   - Gói không giới hạn tin

3. **Tính năng premium**
   - Tin nổi bật
   - Ưu tiên hiển thị
   - Thống kê chi tiết

4. **Hệ thống điểm**
   - Tích điểm khi sử dụng
   - Đổi điểm lấy tin đăng

## Liên hệ hỗ trợ

- **Zalo**: 0944170391
- **Email**: <EMAIL>
