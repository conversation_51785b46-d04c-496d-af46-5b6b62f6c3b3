<?php
// Trang dashboard cho admin
require_once '../app/libraries/Database.php';

// Khởi tạo kết nối cơ sở dữ liệu
$db = new Database();

// Lấy số lượng bất động sản
$db->query("SELECT COUNT(*) as total_properties FROM properties");
$propertyCount = $db->single()->total_properties ?? 0;

// Lấy số lượng người dùng
$db->query("SELECT COUNT(*) as total_users FROM users WHERE role = 'user'");
$userCount = $db->single()->total_users ?? 0;

// Lấy danh sách bất động sản mới nhất
$db->query("SELECT p.*, pt.name as type_name
            FROM properties p
            LEFT JOIN property_types pt ON p.type_id = pt.id
            ORDER BY p.created_at DESC LIMIT 5");
$latestProperties = $db->resultSet();
?>

<div class="container-fluid px-4">
    <h1 class="mt-2 mb-4">Tổng quan</h1>

    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $propertyCount; ?></h4>
                            <div>Bất động sản</div>
                        </div>
                        <i class="bi bi-house-door fs-1"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="index.php?page=properties">Xem chi tiết</a>
                    <div class="small text-white"><i class="bi bi-chevron-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $userCount; ?></h4>
                            <div>Người dùng</div>
                        </div>
                        <i class="bi bi-people fs-1"></i>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="index.php?page=users">Xem chi tiết</a>
                    <div class="small text-white"><i class="bi bi-chevron-right"></i></div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-table me-1"></i>
            Bất động sản mới nhất
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Tiêu đề</th>
                            <th>Loại</th>
                            <th>Địa chỉ</th>
                            <th>Giá</th>
                            <th>Ngày tạo</th>
                            <th>Hành động</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($latestProperties)): ?>
                            <tr>
                                <td colspan="7" class="text-center">Không có dữ liệu</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($latestProperties as $property): ?>
                                <tr>
                                    <td><?php echo $property->id; ?></td>
                                    <td><?php echo $property->title; ?></td>
                                    <td><?php echo $property->type_name; ?></td>
                                    <td><?php echo $property->address; ?></td>
                                    <td><?php echo number_format($property->price); ?> VNĐ</td>
                                    <td><?php echo date('d/m/Y', strtotime($property->created_at)); ?></td>
                                    <td>
                                        <a href="index.php?page=properties&action=edit&id=<?php echo $property->id; ?>" class="btn btn-sm btn-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="index.php?page=properties&action=delete&id=<?php echo $property->id; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Bạn có chắc chắn muốn xóa?');">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>