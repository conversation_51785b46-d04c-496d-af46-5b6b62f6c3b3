<?php
require_once __DIR__ . '/../libraries/Database.php';

class Package {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    // Lấy tất cả packages
    public function getAllPackages() {
        $this->db->query('SELECT * FROM packages ORDER BY is_default DESC, price ASC');
        return $this->db->resultSet();
    }

    // Lấy packages đang hoạt động
    public function getActivePackages() {
        $this->db->query('SELECT * FROM packages WHERE status = 1 ORDER BY is_default DESC, price ASC');
        return $this->db->resultSet();
    }

    // Lấy package theo ID
    public function getPackageById($id) {
        $this->db->query('SELECT * FROM packages WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    // Lấy package mặc định
    public function getDefaultPackage() {
        $this->db->query('SELECT * FROM packages WHERE is_default = 1 AND status = 1 LIMIT 1');
        return $this->db->single();
    }

    // Thêm package mới
    public function create($data) {
        // Nếu đây là package mặc định, cập nhật các package khác
        if ($data['is_default'] == 1) {
            $this->db->query('UPDATE packages SET is_default = 0');
            $this->db->execute();
        }

        $this->db->query('INSERT INTO packages (name, description, post_limit, price, duration_days, features, is_default, status)
                         VALUES (:name, :description, :post_limit, :price, :duration_days, :features, :is_default, :status)');

        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description'] ?? null);
        $this->db->bind(':post_limit', $data['post_limit']);
        $this->db->bind(':price', $data['price']);
        $this->db->bind(':duration_days', $data['duration_days']);
        $this->db->bind(':features', $data['features'] ?? null);
        $this->db->bind(':is_default', $data['is_default'] ?? 0);
        $this->db->bind(':status', $data['status'] ?? 1);

        return $this->db->execute();
    }

    // Cập nhật package
    public function update($data) {
        // Nếu đây là package mặc định, cập nhật các package khác
        if ($data['is_default'] == 1) {
            $this->db->query('UPDATE packages SET is_default = 0 WHERE id != :id');
            $this->db->bind(':id', $data['id']);
            $this->db->execute();
        }

        $this->db->query('UPDATE packages SET
                         name = :name,
                         description = :description,
                         post_limit = :post_limit,
                         price = :price,
                         duration_days = :duration_days,
                         features = :features,
                         is_default = :is_default,
                         status = :status
                         WHERE id = :id');

        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description'] ?? null);
        $this->db->bind(':post_limit', $data['post_limit']);
        $this->db->bind(':price', $data['price']);
        $this->db->bind(':duration_days', $data['duration_days']);
        $this->db->bind(':features', $data['features'] ?? null);
        $this->db->bind(':is_default', $data['is_default'] ?? 0);
        $this->db->bind(':status', $data['status'] ?? 1);

        return $this->db->execute();
    }

    // Xóa package
    public function delete($id) {
        // Không cho phép xóa package mặc định
        $package = $this->getPackageById($id);
        if ($package && $package->is_default == 1) {
            return false;
        }

        $this->db->query('DELETE FROM packages WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }

    // Kiểm tra package có đang được sử dụng không
    public function isPackageInUse($id) {
        $this->db->query('SELECT COUNT(*) as count FROM user_packages WHERE package_id = :id');
        $this->db->bind(':id', $id);
        $result = $this->db->single();
        return $result->count > 0;
    }

    // Lấy thống kê package
    public function getPackageStats() {
        $this->db->query('SELECT p.*, 
                         COUNT(up.id) as user_count,
                         COUNT(CASE WHEN up.status = "active" THEN 1 END) as active_users
                         FROM packages p
                         LEFT JOIN user_packages up ON p.id = up.package_id
                         GROUP BY p.id
                         ORDER BY p.is_default DESC, p.price ASC');
        return $this->db->resultSet();
    }
}
