/**
 * Image Compressor - <PERSON><PERSON> lý nén ảnh trư<PERSON><PERSON> khi upload
 * Sử dụng thư viện Compressor.js để tối ưu hóa hình ảnh trước khi gửi lên server
 */
document.addEventListener('DOMContentLoaded', function() {
    // Các biến toàn cục
    let compressedFiles = [];
    let totalFiles = 0;
    let failedFiles = 0;
    let isUploading = false;

    // Các phần tử DOM
    const propertyImagesInput = document.getElementById('property_images');
    const uploadButton = document.querySelector('button[name="upload_images"]');
    const uploadForm = propertyImagesInput ? propertyImagesInput.closest('form') : null;
    const progressContainer = document.createElement('div');
    progressContainer.className = 'mt-3 mb-3 d-none';
    progressContainer.innerHTML = `
        <div class="progress">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
        </div>
        <div class="text-center mt-2 small text-muted compression-status"></div>
    `;

    // Thêm container hiển thị tiến trình vào form
    if (uploadForm) {
        uploadForm.querySelector('.mb-3').appendChild(progressContainer);
    }

    // Khởi tạo sự kiện cho input file
    if (propertyImagesInput) {
        propertyImagesInput.addEventListener('change', function(e) {
            // Reset trạng thái
            compressedFiles = [];
            failedFiles = 0;

            const files = e.target.files;
            totalFiles = files.length;

            if (totalFiles === 0) return;

            // Hiển thị progress bar
            progressContainer.classList.remove('d-none');
            updateProgressStatus('Đang chuẩn bị nén ' + totalFiles + ' hình ảnh...');
            updateProgressBar(0);

            // Vô hiệu hóa nút upload trong quá trình nén
            if (uploadButton) {
                uploadButton.disabled = true;
                uploadButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang nén...';
            }

            // Xử lý từng file
            Array.from(files).forEach((file, index) => {
                compressImage(file, index);
            });

            // Ghi log để debug
            console.log('Started compressing ' + totalFiles + ' images');
        });
    }

    // Xử lý sự kiện submit form
    if (uploadForm) {
        uploadForm.addEventListener('submit', function(e) {
            // Luôn ngăn chặn hành vi mặc định của form
            e.preventDefault();

            // Nếu không có file nào được chọn, hiển thị thông báo
            if (totalFiles === 0) {
                alert('Vui lòng chọn ít nhất một hình ảnh để upload.');
                return;
            }

            // Nếu chưa nén xong tất cả các file, hiển thị thông báo
            if (compressedFiles.length + failedFiles < totalFiles) {
                alert('Vui lòng đợi quá trình nén hình ảnh hoàn tất.');
                return;
            }

            // Nếu không có file nào được nén thành công, hiển thị thông báo
            if (compressedFiles.length === 0) {
                alert('Không có hình ảnh nào được nén thành công. Vui lòng thử lại.');
                return;
            }

            // Nếu đang upload, không làm gì cả
            if (isUploading) return;

            // Đánh dấu đang upload
            isUploading = true;

            // Hiển thị trạng thái
            updateProgressStatus('Đang upload hình ảnh...');
            updateProgressBar(0);

            // Vô hiệu hóa nút upload
            if (uploadButton) {
                uploadButton.disabled = true;
                uploadButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang upload...';
            }

            // Tạo FormData mới thay vì sử dụng form
            const formData = new FormData();

            // Thêm các file đã nén vào FormData
            compressedFiles.forEach((fileData, index) => {
                formData.append('property_images[]', fileData.file);
            });

            // Thêm các trường cần thiết
            formData.append('upload_images', '1');

            // Ghi log để debug
            console.log('Uploading ' + compressedFiles.length + ' compressed images');

            // Upload file bằng AJAX
            $.ajax({
                url: uploadForm.action,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function() {
                    // Hiển thị thông báo thành công
                    updateProgressStatus('Upload thành công!');
                    updateProgressBar(100);

                    // Chuyển hướng đến trang chỉnh sửa sau 1 giây
                    setTimeout(function() {
                        const propertyId = new URLSearchParams(window.location.search).get('id');
                        window.location.href = 'index.php?page=properties&action=edit&id=' + propertyId;
                    }, 1000);
                },
                error: function(_, status, error) {
                    // Hiển thị lỗi
                    console.error('Upload error:', status, error);
                    updateProgressStatus('Lỗi khi upload: ' + error);

                    // Kích hoạt lại nút upload
                    if (uploadButton) {
                        uploadButton.disabled = false;
                        uploadButton.innerHTML = '<i class="bi bi-upload me-1"></i> Upload';
                    }

                    // Đặt lại trạng thái upload
                    isUploading = false;
                }
            });
        });
    }

    // Hàm nén hình ảnh
    function compressImage(file, index) {
        // Kiểm tra loại file
        if (!file.type.match('image.*')) {
            failedFiles++;
            updateCompressionProgress();
            updateProgressStatus('File #' + (index + 1) + ' không phải là hình ảnh hợp lệ.');
            return;
        }

        // Kiểm tra kích thước file (tối đa 10MB trước khi nén)
        if (file.size > 10 * 1024 * 1024) {
            failedFiles++;
            updateCompressionProgress();
            updateProgressStatus('File #' + (index + 1) + ' vượt quá kích thước cho phép (tối đa 10MB).');
            return;
        }

        // Ghi log thông tin file gốc
        console.log('Original file:', file.name, 'Size:', file.size, 'Type:', file.type);

        // Sử dụng Compressor.js để nén hình ảnh
        new Compressor(file, {
            quality: 0.8, // Chất lượng 80%
            maxWidth: 1000, // Giới hạn chiều rộng tối đa 1000px
            maxHeight: 1000, // Giới hạn chiều cao tối đa (tự động điều chỉnh theo tỷ lệ)
            minWidth: 300, // Chiều rộng tối thiểu
            minHeight: 300, // Chiều cao tối thiểu
            convertSize: 1000000, // Chuyển đổi sang JPEG nếu kích thước > 1MB
            checkOrientation: true, // Kiểm tra và sửa hướng ảnh
            strict: true, // Áp dụng nghiêm ngặt các giới hạn kích thước
            success(result) {
                // Ghi log thông tin file đã nén
                console.log('Compressed result:', result.size, 'Type:', result.type);

                // Tạo tên file mới
                const fileName = 'compressed_' + Date.now() + '_' + index + '.jpg';

                // Tạo File mới từ Blob kết quả
                const compressedFile = new File([result], fileName, {
                    type: 'image/jpeg',
                    lastModified: new Date().getTime()
                });

                // Ghi log thông tin file mới
                console.log('New file created:', fileName, 'Size:', compressedFile.size);

                // Thêm vào mảng file đã nén
                compressedFiles.push({
                    originalFile: file,
                    file: compressedFile,
                    originalSize: file.size,
                    compressedSize: compressedFile.size
                });

                // Cập nhật tiến trình
                updateCompressionProgress();

                // Hiển thị thông tin nén
                const savingsPercent = Math.round((1 - (compressedFile.size / file.size)) * 100);
                const originalSize = formatBytes(file.size);
                const newSize = formatBytes(compressedFile.size);
                updateProgressStatus(
                    'Đã nén ' + (compressedFiles.length) + '/' + totalFiles + ' hình ảnh. ' +
                    'Kích thước gốc: ' + originalSize + ' → Sau khi nén: ' + newSize + '. ' +
                    'Tiết kiệm: ' + formatBytes(file.size - compressedFile.size) +
                    ' (' + savingsPercent + '%)'
                );
            },
            error(err) {
                console.error('Compression error:', err.message);
                failedFiles++;
                updateCompressionProgress();
                updateProgressStatus('Lỗi khi nén file #' + (index + 1) + ': ' + err.message);
            }
        });
    }

    // Hàm cập nhật tiến trình nén
    function updateCompressionProgress() {
        const progress = ((compressedFiles.length + failedFiles) / totalFiles) * 100;
        updateProgressBar(progress);

        // Nếu đã nén xong tất cả các file
        if (compressedFiles.length + failedFiles === totalFiles) {
            // Tính toán tổng kích thước đã tiết kiệm
            let totalOriginalSize = 0;
            let totalCompressedSize = 0;

            compressedFiles.forEach(fileData => {
                totalOriginalSize += fileData.originalSize;
                totalCompressedSize += fileData.compressedSize;
            });

            const totalSaved = totalOriginalSize - totalCompressedSize;
            const savingsPercent = Math.round((1 - (totalCompressedSize / totalOriginalSize)) * 100);

            // Hiển thị thông tin tổng hợp
            updateProgressStatus(
                'Đã nén xong ' + compressedFiles.length + '/' + totalFiles + ' hình ảnh. ' +
                'Tổng kích thước gốc: ' + formatBytes(totalOriginalSize) + ' → Sau khi nén: ' + formatBytes(totalCompressedSize) + '. ' +
                'Tổng tiết kiệm: ' + formatBytes(totalSaved) +
                ' (' + savingsPercent + '%)'
            );

            // Kích hoạt lại nút upload
            if (uploadButton) {
                uploadButton.disabled = false;
                uploadButton.innerHTML = '<i class="bi bi-upload me-1"></i> Upload';
            }
        }
    }



    // Hàm cập nhật progress bar
    function updateProgressBar(percent) {
        const progressBar = progressContainer.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = percent + '%';
            progressBar.setAttribute('aria-valuenow', percent);
        }
    }

    // Hàm cập nhật trạng thái
    function updateProgressStatus(message) {
        const statusElement = progressContainer.querySelector('.compression-status');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }

    // Hàm định dạng kích thước file
    function formatBytes(bytes, decimals = 2) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }
});
