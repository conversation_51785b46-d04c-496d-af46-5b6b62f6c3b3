<?php

// Test URL generation for price-only scenarios
define('BASE_PATH', __DIR__);
define('APP_PATH', BASE_PATH . '/app');

// Load required files
require_once APP_PATH . '/libraries/Database.php';
require_once APP_PATH . '/libraries/UrlHandler.php';
require_once APP_PATH . '/models/PropertyType.php';
require_once APP_PATH . '/models/Ward.php';

echo "<h1>Test Price-Only URL Generation</h1>\n";

try {
    $urlHandler = new UrlHandler();
    
    // Test cases for price-only scenarios
    $testCases = [
        [
            'description' => 'Only price 15+',
            'params' => [
                'type' => '',
                'ward' => '',
                'price' => '15+',
                'area' => '',
                'bedrooms' => ''
            ],
            'expected' => '/thuenhadanang/cho-thue-nha-dat/gia-tren-15-trieu'
        ],
        [
            'description' => 'Only price 5-7',
            'params' => [
                'type' => '',
                'ward' => '',
                'price' => '5-7',
                'area' => '',
                'bedrooms' => ''
            ],
            'expected' => '/thuenhadanang/cho-thue-nha-dat/gia-5-7-trieu'
        ],
        [
            'description' => 'Only price 1-3',
            'params' => [
                'type' => '',
                'ward' => '',
                'price' => '1-3',
                'area' => '',
                'bedrooms' => ''
            ],
            'expected' => '/thuenhadanang/cho-thue-nha-dat/gia-1-3-trieu'
        ],
        [
            'description' => 'Price + area',
            'params' => [
                'type' => '',
                'ward' => '',
                'price' => '15+',
                'area' => '50-70',
                'bedrooms' => ''
            ],
            'expected' => '/thuenhadanang/cho-thue-nha-dat/gia-tren-15-trieu-dt-50-70m2'
        ],
        [
            'description' => 'Price + bedrooms',
            'params' => [
                'type' => '',
                'ward' => '',
                'price' => '15+',
                'area' => '',
                'bedrooms' => '2'
            ],
            'expected' => '/thuenhadanang/cho-thue-nha-dat/gia-tren-15-trieu-2pn'
        ]
    ];
    
    echo "<h2>UrlHandler Tests</h2>\n";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Test Case</th><th>Generated URL</th><th>Expected URL</th><th>Result</th></tr>\n";
    
    $passCount = 0;
    $totalCount = count($testCases);
    
    foreach ($testCases as $test) {
        $generatedUrl = $urlHandler->buildUrl($test['params']);
        $passed = ($generatedUrl === $test['expected']);
        
        if ($passed) {
            $passCount++;
        }
        
        $resultIcon = $passed ? '✅' : '❌';
        $rowColor = $passed ? '#e8f5e8' : '#ffe8e8';
        
        echo "<tr style='background-color: $rowColor;'>\n";
        echo "<td>{$test['description']}</td>\n";
        echo "<td><code>$generatedUrl</code></td>\n";
        echo "<td><code>{$test['expected']}</code></td>\n";
        echo "<td>$resultIcon</td>\n";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    $successRate = round(($passCount / $totalCount) * 100, 2);
    echo "<p><strong>UrlHandler Success Rate:</strong> $passCount/$totalCount ($successRate%)</p>\n";
    
    // Test parsing the problematic URL
    echo "<h2>Parsing Test for Problematic URL</h2>\n";
    
    $problematicUrls = [
        'cho-thue-nha-dat-gia-tren-15-trieu',  // Wrong format (no slash)
        'cho-thue-nha-dat/gia-tren-15-trieu'   // Correct format (with slash)
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>URL</th><th>Parsed Successfully</th><th>Type</th><th>Ward</th><th>Price</th><th>Notes</th></tr>\n";
    
    foreach ($problematicUrls as $url) {
        $result = $urlHandler->parseUrl($url);
        $success = $result['matched'];
        
        $successIcon = $success ? '✅' : '❌';
        $rowColor = $success ? '#e8f5e8' : '#ffe8e8';
        $notes = '';
        
        if ($url === 'cho-thue-nha-dat-gia-tren-15-trieu') {
            $notes = $success ? 'Should NOT parse (wrong format)' : 'Correctly rejected (wrong format)';
            // Flip the color logic for this case
            $rowColor = $success ? '#ffe8e8' : '#e8f5e8';
            $successIcon = $success ? '❌ (Should fail)' : '✅ (Correctly failed)';
        } else {
            $notes = $success ? 'Correctly parsed' : 'Should parse (correct format)';
        }
        
        echo "<tr style='background-color: $rowColor;'>\n";
        echo "<td><code>$url</code></td>\n";
        echo "<td>$successIcon</td>\n";
        echo "<td>" . ($result['type'] ?: '-') . "</td>\n";
        echo "<td>" . ($result['ward'] ?: '-') . "</td>\n";
        echo "<td>" . ($result['price'] ?: '-') . "</td>\n";
        echo "<td>$notes</td>\n";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    // Test JavaScript equivalent logic
    echo "<h2>JavaScript Logic Simulation</h2>\n";
    echo "<p>Simulating the JavaScript logic that was causing the problem:</p>\n";
    
    // Simulate the old (wrong) JavaScript logic
    function simulateOldJavaScript($type, $ward, $price) {
        $url = '/thuenhadanang';
        if (!$type && !$ward && $price) {
            // Old wrong logic
            $url .= '/cho-thue-nha-dat-gia-' . formatPriceForUrlJS($price);
        }
        return $url;
    }
    
    // Simulate the new (correct) JavaScript logic
    function simulateNewJavaScript($type, $ward, $price) {
        $url = '/thuenhadanang';
        if (!$type && !$ward && $price) {
            // New correct logic
            $url .= '/cho-thue-nha-dat/gia-' . formatPriceForUrlJS($price);
        }
        return $url;
    }
    
    function formatPriceForUrlJS($price) {
        switch ($price) {
            case '15+':
                return 'tren-15-trieu';
            case '5-7':
                return '5-7-trieu';
            default:
                return $price;
        }
    }
    
    $jsTestCases = [
        ['type' => '', 'ward' => '', 'price' => '15+'],
        ['type' => '', 'ward' => '', 'price' => '5-7']
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Input</th><th>Old JS Logic (Wrong)</th><th>New JS Logic (Fixed)</th><th>UrlHandler</th></tr>\n";
    
    foreach ($jsTestCases as $jsTest) {
        $oldResult = simulateOldJavaScript($jsTest['type'], $jsTest['ward'], $jsTest['price']);
        $newResult = simulateNewJavaScript($jsTest['type'], $jsTest['ward'], $jsTest['price']);
        $urlHandlerResult = $urlHandler->buildUrl($jsTest);
        
        echo "<tr>\n";
        echo "<td>Price: {$jsTest['price']}</td>\n";
        echo "<td style='background-color: #ffe8e8;'><code>$oldResult</code></td>\n";
        echo "<td style='background-color: #e8f5e8;'><code>$newResult</code></td>\n";
        echo "<td style='background-color: #e8f5e8;'><code>$urlHandlerResult</code></td>\n";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    echo "<h2>Summary</h2>\n";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<h4 style='color: #155724;'>✅ ISSUE FIXED</h4>\n";
    echo "<p style='color: #155724;'><strong>Problem:</strong> JavaScript in search.js was generating URLs like <code>cho-thue-nha-dat-gia-tren-15-trieu</code> (missing slash)</p>\n";
    echo "<p style='color: #155724;'><strong>Solution:</strong> Fixed line 103 in search.js to include the slash: <code>/cho-thue-nha-dat/gia-...</code></p>\n";
    echo "<p style='color: #155724;'><strong>Result:</strong> Now generates correct URLs that match UrlHandler patterns</p>\n";
    echo "</div>\n";
    
    echo "<h2>Testing Instructions</h2>\n";
    echo "<ol>\n";
    echo "<li>Clear browser cache (Ctrl+F5)</li>\n";
    echo "<li>Go to search page</li>\n";
    echo "<li>Select only price (e.g., 'Trên 15 triệu') without selecting property type</li>\n";
    echo "<li>Click search</li>\n";
    echo "<li>Check URL should be: <code>/thuenhadanang/cho-thue-nha-dat/gia-tren-15-trieu</code></li>\n";
    echo "<li>Verify the page loads correctly and shows search results</li>\n";
    echo "</ol>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

?>
