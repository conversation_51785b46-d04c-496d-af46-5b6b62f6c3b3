/**
 * Modern Interface JavaScript
 * Handles interactions for the modern property search interface
 */

document.addEventListener('DOMContentLoaded', function() {

    // Initialize modern interface components
    initializeWishlistButtons();
    initializeAdvancedFilters();
    initializeSearchForm();



    /**
     * Initialize wishlist buttons
     */
    function initializeWishlistButtons() {
        const wishlistBtns = document.querySelectorAll('.wishlist-btn');

        wishlistBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const propertyId = this.dataset.propertyId;
                toggleWishlist(this, propertyId);
            });
        });
    }

    /**
     * Toggle wishlist status
     */
    function toggleWishlist(button, propertyId) {
        const isActive = button.classList.contains('active');

        if (isActive) {
            button.classList.remove('active');
            button.innerHTML = '<i class="bi bi-heart"></i>';
            updateWishlistCount(-1);
        } else {
            button.classList.add('active');
            button.innerHTML = '<i class="bi bi-heart-fill"></i>';
            updateWishlistCount(1);
        }

        // Add animation
        button.style.transform = 'scale(1.2)';
        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 150);

        // Here you would typically make an AJAX call to save the wishlist status
        // saveWishlistStatus(propertyId, !isActive);
    }

    /**
     * Update wishlist count in header
     */
    function updateWishlistCount(change) {
        const wishlistCount = document.querySelector('.wishlist-count');
        if (!wishlistCount) return;

        let currentCount = parseInt(wishlistCount.textContent) || 0;
        currentCount += change;

        if (currentCount < 0) currentCount = 0;

        wishlistCount.textContent = currentCount;

        // Hide/show count badge
        if (currentCount === 0) {
            wishlistCount.style.display = 'none';
        } else {
            wishlistCount.style.display = 'block';
        }
    }



    /**
     * Initialize advanced filters
     */
    function initializeAdvancedFilters() {
        const advancedFiltersBtn = document.getElementById('advancedFiltersBtn');
        const advancedFilters = document.getElementById('advancedFilters');
        const applyFiltersBtn = document.getElementById('applyFiltersBtn');

        if (advancedFiltersBtn && advancedFilters) {
            advancedFiltersBtn.addEventListener('click', function(e) {
                e.preventDefault();
                const isVisible = advancedFilters.style.display !== 'none';

                if (isVisible) {
                    // Hide filters
                    advancedFilters.style.display = 'none';
                    this.classList.remove('active');
                } else {
                    // Show filters
                    advancedFilters.style.display = 'block';
                    advancedFilters.style.animation = 'slideDown 0.3s ease';
                    this.classList.add('active');
                }
            });
        }

        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', function() {
                applyAdvancedFilters();
            });
        }
    }

    /**
     * Apply advanced filters
     */
    function applyAdvancedFilters() {
        // Get filter values
        const bedrooms = document.getElementById('searchBedrooms')?.value;
        const bathrooms = document.getElementById('searchBathrooms')?.value;
        const area = document.getElementById('searchArea')?.value;
        const direction = document.getElementById('searchDirection')?.value;

        // Build URL with filters
        const currentUrl = new URL(window.location);

        if (bedrooms) {
            currentUrl.searchParams.set('bedrooms', bedrooms);
        } else {
            currentUrl.searchParams.delete('bedrooms');
        }

        if (bathrooms) {
            currentUrl.searchParams.set('bathrooms', bathrooms);
        } else {
            currentUrl.searchParams.delete('bathrooms');
        }

        if (area) {
            currentUrl.searchParams.set('area', area);
        } else {
            currentUrl.searchParams.delete('area');
        }

        if (direction) {
            currentUrl.searchParams.set('direction', direction);
        } else {
            currentUrl.searchParams.delete('direction');
        }

        // Redirect to filtered results
        window.location.href = currentUrl.toString();
    }

    /**
     * Initialize search form enhancements
     */
    function initializeSearchForm() {
        const searchForm = document.getElementById('searchPageForm');
        if (!searchForm) return;

        // DISABLED: Search form handler - now using AJAX Search system
        // All search functionality is handled by ajax-search.js
        // Keeping this function for potential future enhancements

        // Enhanced search field interactions
        const searchFields = document.querySelectorAll('.search-field');
        searchFields.forEach(field => {
            const input = field.querySelector('input, select');
            if (!input) return;

            // Add focus/blur animations
            input.addEventListener('focus', function() {
                field.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                field.classList.remove('focused');
            });

            // Add value change detection
            input.addEventListener('input', function() {
                if (this.value) {
                    field.classList.add('has-value');
                } else {
                    field.classList.remove('has-value');
                }
            });

            // Initialize has-value state
            if (input.value) {
                field.classList.add('has-value');
            }
        });

        // Bỏ auto-submit - chỉ submit khi user bấm nút tìm kiếm
        // Không có auto-submit cho bất kỳ select nào

        // Add search suggestions (placeholder for future enhancement)
        initializeSearchSuggestions();
    }

    /**
     * Initialize search suggestions (placeholder)
     */
    function initializeSearchSuggestions() {
        const locationInput = document.querySelector('.location-field input');
        if (!locationInput) return;

        // This would integrate with a search API for location suggestions
        locationInput.addEventListener('input', function() {
            const query = this.value;
            if (query.length > 2) {
                // Debounce search suggestions
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    // fetchLocationSuggestions(query);
                }, 300);
            }
        });
    }

    /**
     * Add fade-in animation keyframes
     */
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    `;
    document.head.appendChild(style);
});
