/* ===================
   Thuê <PERSON> Nẵng UI
   Clean & Modern CSS
   =================== */

/* Import font Inter */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

.fs-14 {
    font-size: 14px !important;
}

.form-control:focus {
    border: unset !important;
    box-shadow: unset !important;
}

.card {
    border-color: #e2e2e266;
}

/* Reset font family cho toàn bộ website */
body {
    background: #f7f9fb;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    color: #222;
    margin: 0;
    min-height: 100vh;
}

/* <PERSON><PERSON><PERSON> bả<PERSON> các heading cũng sử dụng font Inter */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* <PERSON><PERSON><PERSON> bả<PERSON> c<PERSON>c input, select và button cũng sử dụng font Inter */
input, select, button, .btn {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

/* Header */
header {
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}
header .navbar-brand {
    letter-spacing: 1px;
    color: var(--primary-color) !important;
    font-size: 2rem;
    font-weight: 700;
    text-decoration: none;
    transition: color .2s;
}
header .navbar-brand:hover {
    color: var(--primary-hover) !important;
}
header nav .nav {
    display: flex;
    gap: 0.5rem;
}
header .nav-link {
    color: #555 !important;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    transition: background .2s, color .2s;
}
header .nav-link:hover, header .nav-link.active {
    background: var(--primary-light);
    color: var(--primary-color) !important;
}
header .btn-primary {
    background: var(--primary-color);
    border: none;
    border-radius: 8px;
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    color: #fff;
    transition: background .2s;
}
header .btn-primary:hover {
    background: var(--primary-hover);
    color: #fff;
}
header .btn-login {
    padding: 7px 20px;
    border: solid 1px #ff7515;
    font-weight: 600;
    border-radius: 8px;
}
header .btn-login:hover {
    background: #ff7515;
    color: #fff;
}
header .btn-login i {
    transition: color 0.2s;
}
header .btn-login:hover i {
    color: #fff;
}
header .bi-person-circle {
    font-size: 14px;
    color: var(--primary-color);
    margin-right: 8px;
}

/* Hero search section */
section.hp_top_search {
    /* background: linear-gradient(90deg, var(--primary-color) 60%, #ff8533 100%);
    border-radius: 0 0 24px 24px;
    box-shadow: 0 4px 24px rgba(255,107,0,0.08);
    padding: 48px 0 32px 0; */
}
section.hp_top_search h1 {
    font-size: 2rem;
    font-weight: 600;
    color: #000;
    text-align: center;
}
form.row {
    background: rgba(255,255,255,0.09);
    border-radius: 14px;
    padding: 18px 12px 10px 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.04);
    margin-bottom: 0;
}
form .form-label {
    color: #fff;
    font-weight: 500;
}
form .form-control, form .form-select {
    border-radius: 8px;
    border: none;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
    min-height: 40px;
    font-size: 1rem;
}
form .btn-warning {
    background: #ffc107;
    color: #222;
    border-radius: 8px;
    font-weight: 600;
    border: none;
    box-shadow: 0 2px 8px rgba(255,193,7,0.10);
    transition: background .2s;
}
form .btn-warning:hover {
    background: #ffb300;
}

/* Property Tabs */
.property-tabs-container {
    margin-top: 2rem;
}

.property-tabs {
    display: flex;
    margin-bottom: 1.5rem;
    border-bottom: unset !important;
}

.property-tabs .nav-link {
    color: #333;
    font-weight: 500;
    border: none;
    padding: 0.75rem 1.5rem;
    margin-right: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    position: relative;
    background-color: #fff;
    border: solid 1px transparent;
}

.property-tabs .nav-link:hover {
    color: var(--primary-color);
    background-color: rgba(255, 117, 21, 0.05);
    border-color: transparent;
}

.property-tabs .nav-link.active {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

/* .property-tabs .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px 3px 0 0;
} */

.tab-content {
    padding: 0.5rem 0;
    min-height: 300px;
}

/* Đã tắt hiệu ứng loading cho tab content */
.tab-pane {
    transition: none;
}

.tab-pane.loading {
    opacity: 1;
}

/* Property card grid */
.property-list {
    margin-top: 1.5rem;
}

.property-list .card,
#property-list .card {
    border: none;
    border-radius: 0.375rem;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    transition: all 0.3s ease;
    background: #fff;
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
}

.property-list .card:hover,
#property-list .card:hover {
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    /* Đã tắt hiệu ứng transform */
}

/* Ribbon cho bất động sản nổi bật đã được thay thế bằng badge */

.property-list .card-img-wrapper,
#property-list .card-img-wrapper {
    position: relative;
    width: 100%;
    height: 200px; /* Chiều cao cố định cho ảnh */
    overflow: hidden;
}

.property-list .card-img-top,
#property-list .card-img-top {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    background-color: #f8f9fa; /* Màu nền khi ảnh chưa load xong */
    transition: transform 0.5s ease;
}

.property-list .card:hover .card-img-top,
#property-list .card:hover .card-img-top {
    /* Đã tắt hiệu ứng scale */
}

/* Gradient overlay cho ảnh */
.property-list .card-img-wrapper::after,
#property-list .card-img-wrapper::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to top, rgba(0,0,0,0.3), transparent);
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.property-list .card:hover .card-img-wrapper::after,
#property-list .card:hover .card-img-wrapper::after {
    /* Đã tắt hiệu ứng gradient overlay */
    opacity: 0;
}

/* Thêm placeholder cho ảnh khi chưa load được */
.property-list .card-img-top:not([src]),
.property-list .card-img-top[src=""],
.property-list .card-img-top[src="undefined"],
.property-list .card-img-top[src="null"],
#property-list .card-img-top:not([src]),
#property-list .card-img-top[src=""],
#property-list .card-img-top[src="undefined"],
#property-list .card-img-top[src="null"] {
    visibility: hidden;
}

.property-list .card-img-wrapper::before,
#property-list .card-img-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f0f2f5;
    z-index: -1;
}

/* Styles cho trạng thái tin đăng */
.property-basic-info.border-warning {
    border: 2px solid #ffc107 !important;
    position: relative;
}

.property-basic-info.border-secondary {
    border: 2px solid #6c757d !important;
    position: relative;
}

.pending-badge, .expired-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}

.alert-warning {
    border-left: 4px solid #ffc107;
}

.alert-danger {
    border-left: 4px solid #dc3545;
}

.alert-secondary {
    border-left: 4px solid #6c757d;
}

.property-basic-info .property-price{
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

.property-list .property-price,
#property-list .property-price {
    margin-top: auto;
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
    padding-top: 12px;
}

.property-list .property-favorite,
#property-list .property-favorite {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(255,255,255,0.9);
    padding: 8px;
    border-radius: 50%;
    font-size: 16px;
    color: #ccc;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Badge cho bất động sản nổi bật */
.property-badge.featured {
    position: absolute;
    bottom: 0;
    left: 0;
    background: var(--primary-color);
    color: white;
    padding: 5px 10px;
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
    border-radius: 0 4px 0 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* Styles cho trang search */
.list-group-item {
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    border-left: 3px solid var(--primary-color);
}

.list-group-item a {
    display: block;
    color: #333;
    transition: color 0.2s ease;
}

.list-group-item a:hover {
    color: var(--primary-color);
}

.card-header {
    font-weight: 600;
}

.dropdown-item.active {
    background-color: var(--primary-color);
    color: white;
}

.dropdown-item:hover:not(.active) {
    background-color: rgba(255, 117, 21, 0.1);
    color: var(--primary-color);
}

/* Styles cho bộ lọc */
.form-label.fw-bold {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.btn-outline-primary {
    border-color: #ddd;
    color: #666;
}

.btn-outline-primary:hover {
    background-color: rgba(255, 117, 21, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #e56b17;
    border-color: #e56b17;
}

.card-header {
    padding: 0.75rem 1rem;
}

.card-header h5 {
    font-size: 1rem;
}

.form-select-sm {
    font-size: 0.875rem;
}

/* Badge cho bộ lọc đã chọn */
.filter-badge {
    display: inline-block;
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
    border-radius: 30px;
    padding: 0.25rem 0.75rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
}

.filter-badge .badge-remove {
    margin-left: 0.5rem;
    cursor: pointer;
    color: #999;
}

.filter-badge .badge-remove:hover {
    color: #dc3545;
}

/* Styles cho advanced filters trong search bar */
.advanced-filters {
    padding: 15px;
    margin-top: 15px;
}

.advanced-filters label {
    font-weight: 500;
    margin-bottom: 5px;
}

.advanced-filters .form-select {
    height: 38px;
    background-color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 0.9rem;
}

.advanced-filters .btn-warning {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    height: 38px;
    font-weight: 500;
}

.advanced-filters .btn-warning:hover {
    background-color: #e56b17;
    border-color: #e56b17;
}

.advanced-filters .btn-outline-light {
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    font-size: 0.85rem;
}

.advanced-filters .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: white;
}

/* Đồng nhất giao diện form tìm kiếm */
.search-container .form-control,
.search-container .form-select,
.search-container .btn {
    height: 45px;
    font-size: 0.95rem;
}

.search-container .btn-warning {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
}

.search-container .btn-warning:hover {
    background-color: #e56b17;
    border-color: #e56b17;
}

/* Styles cho dropdown bộ lọc nâng cao */
.more-filters-dropdown {
    width: 500px;
    max-width: 90vw;
    padding: 20px !important;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.more-filters-dropdown .dropdown-header {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    padding: 0 0 10px 0;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.more-filters-dropdown .form-label {
    font-size: 13px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.more-filters-dropdown .form-select {
    font-size: 14px;
    height: 38px;
}

.more-filters-dropdown .btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.more-filters-dropdown .btn-primary:hover {
    background-color: #e56b17;
    border-color: #e56b17;
}

.search-container .dropdown-toggle {
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    color: var(--bs-body-color);
}

.search-container .dropdown-toggle .badge {
    font-size: 11px;
    padding: 3px 6px;
    border-radius: 10px;
}

/* Styles cho nút sắp xếp */
.sort-select-wrapper {
    position: relative;
    display: inline-block;
}

.sort-icon-left {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 14px;
    pointer-events: none;
}

.sort-icon-right {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 12px;
    pointer-events: none;
}

.sort-select-wrapper .form-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 25px;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #ced4da;
    font-weight: 500;
    color: #333;
    padding-right: 10px;
}

.property-list .property-favorite:hover,
#property-list .property-favorite:hover {
    background: #fff;
    color: #ff3366;
    transform: scale(1.1);
}

.property-list .property-favorite.active,
#property-list .property-favorite.active {
    color: #ff3366;
}

.property-list .card-body,
#property-list .card-body {
    padding: 16px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.property-list .card-title,
#property-list .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 12px;
    line-height: 1.5;
}

.property-list .property-title,
#property-list .property-title {
    color: #000;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 2.9rem;
}

.property-list .property-title:hover,
#property-list .property-title:hover {
    color: var(--primary-color);
}

.property-list .property-location,
#property-list .property-location {
    color: #777;
    font-size: 14px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.property-list .property-location i,
#property-list .property-location i {
    color: #999;
    font-size: 14px;
}

.property-list .property-features,
#property-list .property-features {
    display: flex;
    justify-content: space-between;
    color: #666;
    font-size: 13px;
    padding: 8px 0px 15px 0px;
    margin-top: auto;
    margin-bottom: 0px;
    border-bottom: solid 1px #ededed;
}

.property-features .feature-item {
    padding: 3px 9px;
    border-radius: 5px;
    font-size: 13px;
    border: solid 1px #e8e8e8;
}

.property-list .feature-item,
#property-list .feature-item {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #666;
}

.property-list .feature-item i,
#property-list .feature-item i {
    color: #999;
    font-size: 14px;
    padding-right: 3px;
}

.property-list .property-info,
#property-list .property-info {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
    color: #666;
    font-size: 14px;
}

.property-list .property-info i,
#property-list .property-info i {
    color: #999;
    margin-right: 4px;
}

.property-list .card-text,
#property-list .card-text {
    font-size: 0.95rem;
    color: #555;
    margin-bottom: 0.8rem;
}

.property-list .btn-outline-primary,
#property-list .btn-outline-primary {
    border-radius: 6px;
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    font-size: 0.95rem;
    border-width: 1px;
    transition: all 0.3s ease;
}

/* Modern Footer */
.modern-footer {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    margin-top: 4rem;
    position: relative;
    overflow: hidden;
}

.modern-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.footer-main {
    padding: 4rem 0 2rem;
    position: relative;
    z-index: 1;
}

.footer-section {
    height: 100%;
}

/* Footer Brand */
.footer-brand {
    position: relative;
}

.footer-logo-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--modern-primary), var(--modern-secondary));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 8px 25px rgba(255, 107, 0, 0.3);
}

.footer-brand-text {
    color: #ffffff;
    font-weight: 700;
    font-size: 1.5rem;
    background: linear-gradient(135deg, #ffffff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-description {
    color: #b8c5d6;
    line-height: 1.7;
    font-size: 0.95rem;
    margin-bottom: 0;
}

/* Footer Titles */
.footer-title {
    color: #ffffff;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, var(--modern-primary), var(--modern-secondary));
    border-radius: 1px;
}

/* Footer Links */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: #b8c5d6;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    padding-left: 1rem;
}

.footer-links a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 4px;
    background: var(--modern-primary);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: #ffffff;
    padding-left: 1.25rem;
}

.footer-links a:hover::before {
    background: var(--modern-secondary);
    transform: translateY(-50%) scale(1.5);
}

/* Social Links */
.footer-social-title {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.95rem;
}

.social-links {
    display: flex;
    gap: 0.75rem;
}

.social-link {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    transition: all 0.3s ease;
    transform: scale(0);
}

.social-link:hover::before {
    transform: scale(1);
}

.social-link.facebook {
    background: rgba(24, 119, 242, 0.2);
    color: #1877f2;
    border: 1px solid rgba(24, 119, 242, 0.3);
}

.social-link.facebook:hover {
    background: #1877f2;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(24, 119, 242, 0.4);
}

.social-link.zalo {
    background: rgba(0, 150, 255, 0.2);
    color: #0096ff;
    border: 1px solid rgba(0, 150, 255, 0.3);
}

.social-link.zalo:hover {
    background: #0096ff;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 150, 255, 0.4);
}

.social-link.youtube {
    background: rgba(255, 0, 0, 0.2);
    color: #ff0000;
    border: 1px solid rgba(255, 0, 0, 0.3);
}

.social-link.youtube:hover {
    background: #ff0000;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 0, 0, 0.4);
}

.social-link.instagram {
    background: rgba(225, 48, 108, 0.2);
    color: #e1306c;
    border: 1px solid rgba(225, 48, 108, 0.3);
}

.social-link.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(225, 48, 108, 0.4);
}

/* Footer Contact */
.footer-contact {
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateX(5px);
}

.contact-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--modern-primary), var(--modern-secondary));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
    font-size: 1rem;
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 0, 0.3);
}

.contact-info {
    flex: 1;
    color: #b8c5d6;
    font-size: 0.9rem;
    line-height: 1.5;
}

.contact-info a {
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.contact-info a:hover {
    color: var(--modern-primary);
}

/* Newsletter */
.footer-newsletter {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.newsletter-title {
    color: #ffffff;
    font-weight: 600;
    font-size: 1rem;
}

.newsletter-description {
    color: #b8c5d6;
    font-size: 0.85rem;
    line-height: 1.5;
}

.newsletter-form .input-group {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.newsletter-input {
    border: none;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    font-size: 0.9rem;
    padding: 0.75rem 1rem;
    border-radius: 8px 0 0 8px;
}

.newsletter-input:focus {
    background: #ffffff;
    box-shadow: none;
    border: none;
    outline: none;
}

.newsletter-input::placeholder {
    color: #999;
    font-size: 0.85rem;
}

.newsletter-btn {
    background: linear-gradient(135deg, var(--modern-primary), var(--modern-secondary));
    border: none;
    color: white;
    padding: 0.75rem 1.25rem;
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.newsletter-btn:hover {
    background: linear-gradient(135deg, var(--modern-secondary), var(--modern-primary));
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(255, 107, 0, 0.4);
    color: white;
}

/* Footer Bottom */
.footer-bottom {
    background: rgba(0, 0, 0, 0.3);
    padding: 1.5rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 1;
}

.footer-copyright p {
    color: #b8c5d6;
    font-size: 0.9rem;
    margin: 0;
}

.footer-copyright strong {
    color: #ffffff;
}

.footer-legal {
    text-align: right;
}

.legal-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: flex-end;
    gap: 1.5rem;
}

.legal-links a {
    color: #b8c5d6;
    text-decoration: none;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    position: relative;
}

.legal-links a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: var(--modern-primary);
    transition: width 0.3s ease;
}

.legal-links a:hover {
    color: #ffffff;
}

.legal-links a:hover::after {
    width: 100%;
}

/* Utilities & Responsive */
.gap-3 { gap: 1rem !important; }
.d-flex { display: flex !important; }
.align-items-center { align-items: center !important; }
.justify-content-between { justify-content: space-between !important; }
.d-grid { display: grid !important; }

@media (max-width: 992px) {
    .container { max-width: 98vw; }
    section.bg-primary h1 { font-size: 1.5rem; }
    .property-list .card-img-wrapper,
    #property-list .card-img-wrapper {
        height: 180px; /* Chiều cao nhỏ hơn trên tablet */
    }

    /* Điều chỉnh ribbon cho bất động sản nổi bật */
    .property-list .card.featured::before,
    #property-list .card.featured::before {
        top: 15px;
        left: -35px;
        padding: 4px 30px;
        font-size: 11px;
    }
}

@media (max-width: 768px) {
    .property-list .card-img-wrapper,
    #property-list .card-img-wrapper {
        height: 200px; /* Chiều cao lớn hơn trên mobile để ảnh rõ hơn */
    }

    .property-list .property-features,
    #property-list .property-features {
        padding: 8px 0;
    }

    .property-list .feature-item,
    #property-list .feature-item {
        font-size: 13px;
    }

    .property-list .property-price,
    #property-list .property-price {
        font-size: 18px;
    }
}

@media (max-width: 600px) {
    .container { padding: 0 8px; }
    section.bg-primary { padding: 28px 0 18px 0; border-radius: 0 0 14px 14px; }
    form.row { padding: 10px 4px 6px 4px; }

    .property-list .card,
    #property-list .card {
        border-radius: 12px;
    }

    .property-list .card-body,
    #property-list .card-body {
        padding: 14px;
    }

    .property-list .card-title,
    #property-list .card-title {
        font-size: 1rem;
        height: 2.6rem;
    }
}

/* Search Form Styles */
.search-form {
    background: #fff;
    padding: 20px;
    border-radius: 12px;
}

.search-container {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: nowrap;
}

/* Input và Select styles */
.search-container .form-control,
.search-container .form-select,
.search-container .select2-container .select2-selection--single {
    height: 45px !important;
    border-radius: 8px !important;
    border: none !important;
    padding: 8px 15px !important;
    font-size: 14px !important;
    background: #fff !important;
    line-height: 30px !important;
}

/* Custom Select Styles - cho select thông thường */
.search-container .form-select:not(.select2-ward) {
    appearance: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath fill='%23444' d='M8 10.5l-4-4h8l-4 4z'/%3E%3C/svg%3E") !important;
    background-repeat: no-repeat !important;
    background-position: right 15px center !important;
    padding-right: 40px !important;
    cursor: pointer !important;
}
.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #212529;
    font-size: 14px;
}

/* Hover và Focus states cho select thông thường */
.search-container .form-select:not(.select2-ward):hover {
    background-color: #f8f9fa !important;
}

.search-container .form-select:not(.select2-ward):focus {
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(42,122,228,0.2) !important;
}

/* Select2 specific styles - chỉ áp dụng cho select2-ward */
.search-container .select2-ward + .select2-container .select2-selection--single {
    border: none !important;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04) !important;
    height: 45px !important;
    border-radius: 8px !important;
}

.search-container .select2-ward + .select2-container .select2-selection__arrow {
    height: 45px !important;
}
.select2-results__option {
    font-size: 14px;
}

/* Điều chỉnh độ rộng cho từng trường */
.search-container .form-control {
    flex: 2; /* Ô nhập địa chỉ rộng hơn */
    min-width: 200px;
}

.search-container .form-select {
    flex: 1; /* Các select box bằng nhau */
    min-width: 150px;
}

/* Chỉ áp dụng Select2 cho phường/xã */
.search-container .select2-container {
    flex: 1;
    min-width: 150px;
}

/* Đảm bảo các select thường có chiều cao giống Select2 */
.search-container .form-select {
    height: 45px !important;
    border-radius: 8px !important;
    border: none !important;
    padding: 8px 15px !important;
    font-size: 14px !important;
    background-color: #fff !important;
    line-height: 30px !important;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

/* Hover và Focus states */
.search-container .form-select:hover,
.search-container .form-control:hover {
    background-color: #f8f9fa !important;
}

.search-container .form-select:focus,
.search-container .form-control:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(42,122,228,0.2);
}

/* Button style */
.search-container .btn-warning {
    height: 45px;
    padding: 0 25px;
    font-weight: 600;
    border-radius: 8px;
    white-space: nowrap;
    min-width: 120px;
}

/* Select2 custom styles */
.select2-container .select2-selection--single {
    border: none !important;
}

.select2-container .select2-selection--single .select2-selection__rendered {
    /* line-height: 30px !important;
    padding-left: 0px !important;
    padding-right: 0px !important; */
    font-size: 14px;
}
.select2-container--default .select2-selection--single .select2-selection__clear {
    position: relative;
    margin-right: 25px;
}

.select2-dropdown {
    border: none !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-radius: 8px !important;
}

.search-field.ward-field span.select2-selection__arrow {
    width: 26px !important;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .search-container {
        flex-wrap: wrap;
    }

    .search-container .form-control {
        flex: 1 1 100%;
    }

    .search-container .form-select,
    .search-container .select2-container,
    .search-container .btn-warning {
        flex: 1 1 calc(25% - 9px);
        min-width: 120px;
    }
}

@media (max-width: 768px) {
    .search-container .form-select,
    .search-container .select2-container,
    .search-container .btn-warning {
        flex: 1 1 calc(50% - 6px);
    }
}

@media (max-width: 576px) {
    .search-container > * {
        flex: 1 1 100% !important;
    }
}

/* Placeholder Animation Styles */
.search-container .search-address::placeholder {
    transition: opacity 0.3s ease;
}

.search-container .search-address:focus::placeholder {
    opacity: 0;
}

/* Tùy chỉnh màu và style của placeholder */
.search-container .search-address::placeholder {
    color: #999;
    font-style: italic;
}

:root {
    --primary-color: #ff6b00;
    --primary-hover: #ff8533;
    --primary-light: #fff0e6;
    --modern-primary: #2563eb;
    --modern-primary-hover: #1d4ed8;
    --modern-secondary: #64748b;
    --modern-success: #10b981;
    --modern-warning: #f59e0b;
    --modern-danger: #ef4444;
    --modern-gray-50: #f8fafc;
    --modern-gray-100: #f1f5f9;
    --modern-gray-200: #e2e8f0;
    --modern-gray-300: #cbd5e1;
    --modern-gray-400: #94a3b8;
    --modern-gray-500: #64748b;
    --modern-gray-600: #475569;
    --modern-gray-700: #334155;
    --modern-gray-800: #1e293b;
    --modern-gray-900: #0f172a;
    --border-radius: 6px;
    --border-radius-sm: 6px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Thêm icons cho property info */
.bi-rulers {
    font-size: 14px;
}
.bi-geo-alt-fill {
    font-size: 14px;
}

/* Mobile Header Styles */
.navbar-toggler {
    border: none;
    padding: 8px;
    background: transparent;
    color: var(--primary-color);
    font-size: 24px;
    line-height: 1;
    cursor: pointer;
}

.navbar-toggler:focus {
    outline: none;
    box-shadow: none;
}

/* Điều chỉnh logo size trên mobile */
@media (max-width: 768px) {
    header .navbar-brand {
        font-size: 1.5rem;
    }

    header .btn-primary {
        padding: 0.4rem 1rem;
        font-size: 14px;
    }
}

/* Offcanvas Menu Styles */
.offcanvas {
    max-width: 280px;
}

.offcanvas-header {
    padding: 1rem;
    background: var(--primary-light);
}

.offcanvas-title {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.2rem;
}

.offcanvas .nav-link {
    padding: 0.8rem 1rem;
    color: #444 !important;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 8px;
}

.offcanvas .nav-link:hover {
    background: var(--primary-light);
    color: var(--primary-color) !important;
}

.offcanvas .nav-link i {
    font-size: 1.2rem;
}

/* Điều chỉnh container padding trên mobile */
@media (max-width: 576px) {
    .container {
        padding: 0 12px;
    }

    header {
        padding: 8px 0;
    }
}

/* Mobile Search Styles */
.mobile-search-form {
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 12px;
}

.mobile-search-main {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.search-input-wrapper {
    width: 100%;
}

.search-input-wrapper .form-control {
    width: 100%;
    height: 45px;
}

.mobile-search-row {
    display: flex;
    gap: 8px;
    width: 100%;
}

.mobile-search-row .form-select {
    flex: 1;
}

/* Select2 Mobile Styles */
.mobile-filter-section .select2-container .select2-selection--single {
    height: 45px !important;
    border: none !important;
    border-radius: 8px !important;
    background-color: #fff !important;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.mobile-filter-section .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 45px !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
}

.mobile-filter-section .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 45px !important;
    right: 8px !important;
}

.mobile-filter-section .select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #6c757d;
}

/* Filter Section Styles */
.mobile-filter-section {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.filter-row {
    display: flex;
    gap: 8px;
    width: 100%;
}

.filter-row .form-select,
.filter-row .select2-container {
    flex: 1;
    width: 100% !important;
}

/* Button Styles */
.btn-filter {
    width: 45px;
    height: 45px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border: none;
    border-radius: 8px;
    color: var(--primary-color);
    font-size: 18px;
    flex-shrink: 0;
}

/* Responsive Adjustments */
@media (max-width: 400px) {
    .mobile-search-form {
        padding: 12px;
    }

    .mobile-search-row {
        gap: 6px;
    }

    .btn-filter {
        width: 40px;
    }
}

/* ===========================
   MODERN HEADER STYLES
   =========================== */

.modern-header {
    background: white;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    padding: 0.75rem 0;
}

.header-left {
    display: flex;
    align-items: center;
}

.logo-icon {
    width: 32px;
    height: 32px;
    background: var(--modern-primary);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.logo-text {
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--modern-gray-900);
    text-decoration: none;
}

.header-nav {
    display: flex;
    gap: 2rem;
    margin-left: 2rem;
}

.header-nav .nav-link {
    color: var(--modern-gray-600);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 0;
    transition: color 0.2s ease;
    position: relative;
}

.header-nav .nav-link:hover {
    color: var(--modern-primary);
}

.header-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -0.75rem;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--modern-primary);
    border-radius: 1px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.btn-wishlist {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid var(--modern-gray-300);
    border-radius: var(--border-radius);
    background: white;
    color: var(--modern-gray-600);
    transition: all 0.2s ease;
    position: relative;
    text-decoration: none;
    font-weight: 500;
}

.btn-wishlist:hover {
    background: var(--modern-gray-50);
    border-color: var(--modern-gray-400);
    color: var(--modern-gray-700);
}

.wishlist-text {
    font-size: 0.875rem;
}

.wishlist-count {
    background: var(--modern-danger);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--border-radius);
    min-width: 18px;
    text-align: center;
    margin-left: 0.25rem;
}

.btn-profile {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: 1px solid var(--modern-gray-300);
    border-radius: var(--border-radius);
    background: white;
    color: var(--modern-gray-700);
    text-decoration: none;
    transition: all 0.2s ease;
}

.btn-profile:hover {
    background: var(--modern-gray-50);
    border-color: var(--modern-gray-400);
}

.profile-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    object-fit: cover;
}

.profile-avatar-placeholder {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: var(--modern-gray-300);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--modern-gray-600);
    font-size: 14px;
}

/* Modern Mobile Menu */
.modern-mobile-menu .offcanvas-header {
    background: var(--modern-gray-50);
    border-bottom: 1px solid var(--modern-gray-200);
}

.modern-mobile-menu .logo-icon {
    width: 24px;
    height: 24px;
    font-size: 14px;
}

.mobile-user-section {
    padding: 1rem 0;
    border-bottom: 1px solid var(--modern-gray-200);
}

.mobile-nav-menu .nav-link {
    padding: 1rem 0;
    color: var(--modern-gray-700);
    border-bottom: 1px solid var(--modern-gray-100);
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: color 0.2s ease;
}

.mobile-nav-menu .nav-link:hover {
    color: var(--modern-primary);
}

.nav-section-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--modern-gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.5rem 0;
}

/* ===========================
   MODERN SEARCH SECTION STYLES - REDESIGNED
   =========================== */

.modern-search-section {
    background: white;
    padding: 0;
    margin: 0;
    border: none;
    border-radius: 0;
    box-shadow: none;
    border-bottom: 1px solid #e9ecef;
}

.modern-search-form {
    background: white;
    border: none;
    border-radius: 0;
    padding: 16px 0;
    box-shadow: none;
    margin: 0;
    max-width: none;
}

.search-bar-container {
    width: 100%;
}

.main-search-bar {
    width: 100%;
}

.search-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 0;
    overflow: visible;
    background: transparent;
    border: none;
}

.search-field {
    background: white;
    padding: 0px 0px 0px 25px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 45px;
    position: relative;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
}
.search-field:focus-within {
    z-index: 2;
}

/* Specific field widths */
.location-field {
    /* flex: 2;
    min-width: 200px; */
}

.type-field,
.ward-field,
.price-field {
    flex: 1;
    min-width: 150px;
}

/* Field Icons using Bootstrap Icons with Font Awesome fallback */
.search-field::before {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-family: "bootstrap-icons", "Font Awesome 5 Free", "FontAwesome";
    font-size: 14px;
    /* font-weight: 900; */
    font-style: normal;
    /* opacity: 0.6; */
    z-index: 1;
    color: var(--modern-gray-600);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Icon for Keywords/Location field */
.location-field::before {
    content: "\f52a"; /* bi-search */
    /* Fallback to Font Awesome if Bootstrap Icons not available */
    font-family: "bootstrap-icons", "Font Awesome 5 Free";
}

/* Icon for Property Type field */
.type-field::before {
    content: "\f425"; /* bi-house */
    /* Fallback to Font Awesome if Bootstrap Icons not available */
    font-family: "bootstrap-icons", "Font Awesome 5 Free";
}

/* Icon for Ward/Commune field */
.ward-field::before {
    content: "\f3e5"; /* bi-geo-alt */
    /* Fallback to Font Awesome if Bootstrap Icons not available */
    font-family: "bootstrap-icons", "Font Awesome 5 Free";
}

/* Icon for Price field */
.price-field::before {
    content: "\f636"; /* bi-currency-dollar */
    /* Fallback to Font Awesome if Bootstrap Icons not available */
    font-family: "bootstrap-icons", "Font Awesome 5 Free";
}

/* Font Awesome fallback icons (if Bootstrap Icons fail to load) */
@supports not (font-family: "bootstrap-icons") {
    .location-field::before {
        content: "\f002"; /* fa-search */
        font-family: "Font Awesome 5 Free", "FontAwesome";
    }

    .type-field::before {
        content: "\f015"; /* fa-home */
        font-family: "Font Awesome 5 Free", "FontAwesome";
    }

    .ward-field::before {
        content: "\f3c5"; /* fa-map-marker-alt */
        font-family: "Font Awesome 5 Free", "FontAwesome";
    }

    .price-field::before {
        content: "\f155"; /* fa-dollar-sign */
        font-family: "Font Awesome 5 Free", "FontAwesome";
    }
}

/* Hide labels - they are replaced by icons */
.search-label {
    display: none;
}

.search-field .form-control,
.search-field .form-select {
    border: none;
    background: transparent;
    padding: 0;
    font-size: 14px;
    font-weight: 500;
    color: var(--modern-gray-900);
    outline: none;
    box-shadow: none;
    line-height: 1.2;
    width: 100%;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    margin-left: 0;
}

/* This styling is now handled by the simple dropdown styling below */

.search-field .form-control::placeholder {
    color: var(--modern-gray-500);
    font-weight: 400;
    font-size: 14px;
}

/* Enhanced placeholder text for better UX */
.location-field .form-control::placeholder {
    content: "Tìm kiếm địa điểm...";
}

.type-field .form-select option:first-child {
    color: var(--modern-gray-500);
}

.ward-field .form-select option:first-child {
    color: var(--modern-gray-500);
}

.price-field .form-select option:first-child {
    color: var(--modern-gray-500);
}

/* Simple Search Field Dropdown Styling - Only Padding */
.search-field .form-select {
    /* Only padding - no visual styling */
    padding: 0 32px 0 12px !important;
}

/* Simple dropdown options styling - Only Padding */
.search-field .form-select option {
    padding: 12px 16px;
}

.search-field .form-select:invalid {
    color: var(--modern-gray-400);
}

/* Advanced Filters Button */
.advanced-filters-btn {
    background: white;
    color: var(--modern-gray-700);
    border: 1px solid #e9ecef;
    padding: 12px 16px;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
}

.advanced-filters-btn:hover {
    background: #f8f9fa;
    border-color: #dee2e6;
    color: #495057;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.advanced-filters-btn:active {
    transform: translateY(0);
}

.advanced-filters-btn .badge {
    background: #007bff !important;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    margin-left: 4px;
    animation: bounceIn 0.3s ease;
}

@keyframes bounceIn {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.search-btn {
    background: #228B22;
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    min-height: 45px;
    padding: 0px 20px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.search-btn:hover {
    background: #1e7b1e;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 139, 34, 0.3);
}

.search-btn:active {
    transform: translateY(0);
}

/* Loading state for search button */
.search-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.search-btn.loading::after {
    content: "";
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* No transitions for search field elements */

.advanced-filters {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: var(--border-radius);
    border: 1px solid #e9ecef;
    animation: slideDown 0.3s ease;
}

.advanced-filters-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--modern-gray-700);
    margin-bottom: 0.5rem;
}

.filter-group .form-select {
    /* Styling handled by enhanced dropdown CSS */
}

.filter-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* Results Header with Sort */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
    margin-bottom: 2rem;
}

.results-info {
    flex: 1;
}

.results-sort {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
}

.sort-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--modern-gray-600);
    white-space: nowrap;
}

.sort-select {
    min-width: 180px;
    /* Other styling handled by enhanced dropdown CSS */
}

/* ===========================
   MODERN PROPERTY CARDS
   =========================== */

.main-content-section {
    padding: 2rem 0;
    background: var(--modern-gray-50);
}

.results-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--modern-gray-900);
    margin-bottom: 0.5rem;
}

.results-count {
    color: var(--modern-gray-600);
    font-size: 0.875rem;
    margin: 0;
}

.properties-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Ensure 4 columns on large screens */
@media (min-width: 1200px) {
    .properties-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
    }
}

.property-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    cursor: pointer;
    border: solid 1px #efefef;
}

/* Tắt tất cả hiệu ứng hover cho property-card */

.property-image-container {
    position: relative;
    width: 100%;
    height: 240px;
    overflow: hidden;
}

.property-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Tắt hiệu ứng hover cho property-image */

.property-badges {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.property-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.discount-badge {
    background: var(--modern-success);
    color: white;
}

.featured-badge {
    background: var(--modern-warning);
    color: white;
}

.wishlist-btn {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--modern-gray-600);
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
}

/* Tắt hiệu ứng hover cho wishlist button */
.wishlist-btn:hover {
    background: rgba(255, 255, 255, 0.9);
    color: var(--modern-gray-600);
}

.wishlist-btn.active {
    background: var(--modern-danger);
    color: white;
}

.property-content {
    padding: 1.25rem;
}

.property-header {
    margin-bottom: 1rem;
}

.property-title {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
    /* Hiển thị 1 dòng với ellipsis */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.property-title a {
    color: var(--modern-gray-900);
    text-decoration: none;
    /* Tắt hiệu ứng hover */
}

/* Tắt hiệu ứng hover cho property-title */
.property-title a:hover {
    color: var(--modern-gray-900);
}

.property-location {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--modern-gray-600);
    font-size: 0.875rem;
}

.property-location i {
    font-size: 0.75rem;
}

.property-features {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--modern-gray-200);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--modern-gray-600);
    font-size: 0.875rem;
}

.feature-item i {
    font-size: 0.75rem;
}

.property-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.property-price {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.original-price {
    font-size: 0.75rem;
    color: var(--modern-gray-500);
    text-decoration: line-through;
}

.current-price {
    font-size: 1rem;
    font-weight: 700;
    color: var(--modern-gray-900);
}

.property-rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--modern-warning);
    font-size: 0.875rem;
    font-weight: 600;
}

.property-rating i {
    font-size: 0.75rem;
}

/* No Results */
.no-results-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    text-align: center;
}

.no-results-content {
    max-width: 400px;
}

.no-results-icon {
    font-size: 4rem;
    color: var(--modern-gray-400);
    margin-bottom: 1rem;
}

.no-results-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--modern-gray-900);
    margin-bottom: 0.5rem;
}

.no-results-text {
    color: var(--modern-gray-600);
    margin-bottom: 1.5rem;
}

.suggestions {
    text-align: left;
    background: var(--modern-gray-50);
    padding: 1rem;
    border-radius: var(--border-radius-sm);
}

.suggestions ul {
    margin: 0.5rem 0 0 0;
    padding-left: 1.5rem;
}

.suggestions a {
    color: var(--modern-primary);
    text-decoration: none;
}

.suggestions a:hover {
    text-decoration: underline;
}



/* ===========================
   RESPONSIVE STYLES
   =========================== */

/* Tablet Styles */
@media (max-width: 1024px) {
    .modern-search-form {
        padding: 12px 0;
    }

    .search-input-group {
        flex-wrap: wrap;
        gap: 8px;
    }

    .search-field {
        min-height: 52px;
        padding: 10px 14px 10px 40px;
        border: 1px solid #e9ecef;
        border-radius: var(--border-radius);
        flex: 1 1 calc(33.333% - 6px);
        min-width: 140px;
    }

    .search-field::before {
        left: 14px;
        font-size: 14px;
        font-family: "bootstrap-icons", "Font Awesome 5 Free", "FontAwesome";
    }

    .location-field {
        flex: 1 1 100%;
        min-width: auto;
    }

    .advanced-filters-btn,
    .search-btn {
        flex: 1 1 calc(50% - 4px);
        margin: 0;
        height: 52px;
    }

    .properties-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    /* Footer Responsive - Tablet */
    .footer-main {
        padding: 3rem 0 1.5rem;
    }

    .footer-legal {
        text-align: center;
        margin-top: 1rem;
    }

    .legal-links {
        justify-content: center;
        gap: 1rem;
    }

    .results-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .results-sort {
        justify-content: flex-start;
    }

    .sort-select {
        min-width: 160px;
    }

    /* Tablet search field dropdown padding */
    .search-field .form-select {
        padding: 0 30px 0 12px !important;
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    .modern-header .header-content {
        padding: 0.5rem 0;
    }

    .header-nav {
        display: none;
    }

    .logo-text {
        font-size: 1rem;
    }

    .logo-icon {
        width: 28px;
        height: 28px;
        font-size: 16px;
    }

    /* Ẩn nút yêu thích và nút đăng tin trên mobile */
    .btn-wishlist,
    .header-right .btn-primary {
        display: none !important;
    }

    .wishlist-text {
        display: none !important;
    }

    .modern-search-section {
        padding: 12px;
        background: white;
    }

    .modern-search-form {
        padding: 8px 0;
    }

    .search-input-group {
        flex-direction: column;
        gap: 8px;
        background: transparent;
    }

    .search-field {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: var(--border-radius);
        min-height: 48px;
        padding: 10px 12px 10px 36px;
        margin: 0;
        flex: none;
    }

    .search-field::before {
        left: 12px;
        font-size: 14px;
        font-family: "bootstrap-icons", "Font Awesome 5 Free", "FontAwesome";
    }

    .location-field,
    .type-field,
    .ward-field,
    .price-field {
        flex: none;
        min-width: auto;
    }

    .search-field .form-control,
    .search-field .form-select {
        font-size: 16px;
        padding: 0;
    }

    .search-field .form-select {
        padding: 0 28px 0 10px !important;
    }

    .advanced-filters-btn,
    .search-btn {
        height: 48px;
        margin: 0;
        font-size: 14px;
        padding: 10px 16px;
        min-width: auto;
    }

    .results-header {
        flex-direction: column;
        gap: 1rem;
        margin: 0 1rem 1.5rem 1rem;
    }

    .results-sort {
        justify-content: space-between;
    }

    .sort-select {
        min-width: 140px;
        font-size: 0.875rem;
    }

    .properties-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin: 0 1rem;
    }

    /* Footer Responsive - Mobile */
    .footer-main {
        padding: 2.5rem 0 1rem;
    }

    .footer-brand-text {
        font-size: 1.25rem;
    }

    .footer-logo-icon {
        width: 45px;
        height: 45px;
        font-size: 1.25rem;
    }

    .social-links {
        gap: 0.5rem;
    }

    .social-link {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .contact-icon {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .footer-newsletter {
        padding: 1rem;
    }

    .newsletter-form .input-group {
        flex-direction: column;
    }

    .newsletter-input {
        border-radius: 8px;
        margin-bottom: 0.5rem;
    }

    .newsletter-btn {
        border-radius: 8px;
        width: 100%;
    }

    .property-image-container {
        height: 200px;
    }

    .property-content {
        padding: 1rem;
    }

    .property-features {
        gap: 0.75rem;
    }

    .feature-item {
        font-size: 0.8rem;
    }



    .main-content-section {
        padding: 1rem 0;
    }

    .results-title {
        font-size: 1.25rem;
    }

    .no-results-container {
        margin: 0 1rem;
        min-height: 300px;
    }

    .no-results-icon {
        font-size: 3rem;
    }

    .no-results-title {
        font-size: 1.25rem;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 0 0.75rem;
    }

    .modern-search-form {
        margin: 0 0.5rem;
        padding: 0.75rem;
    }

    .search-field {
        min-height: 50px;
        padding: 0.5rem;
    }

    .search-label {
        font-size: 0.7rem;
    }

    .search-field .form-control,
    .search-field .form-select {
        font-size: 0.8rem;
    }



    .properties-grid {
        grid-template-columns: 1fr;
        margin: 0 0.5rem;
    }

    /* Footer Responsive - Small Mobile */
    .footer-main {
        padding: 2rem 0 1rem;
    }

    .footer-brand {
        text-align: center;
        margin-bottom: 2rem;
    }

    .footer-social {
        text-align: center;
    }

    .footer-brand-text {
        font-size: 1.1rem;
    }

    .footer-logo-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .footer-title {
        text-align: center;
        font-size: 1rem;
    }

    .footer-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-links {
        text-align: center;
    }

    .footer-links a {
        padding-left: 0;
    }

    .footer-links a::before {
        display: none;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .contact-icon {
        margin: 0 auto 0.5rem;
    }

    .legal-links {
        flex-direction: column;
        gap: 0.75rem;
    }

    .property-card {
        border-radius: var(--border-radius-sm);
    }

    .property-image-container {
        height: 180px;
    }

    .property-content {
        padding: 0.75rem;
    }

    .property-title {
        font-size: 0.9rem;
    }

    .property-features {
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .feature-item {
        font-size: 0.75rem;
    }

    .current-price {
        font-size: 0.9rem;
    }



    .results-header {
        margin: 0 0.5rem 1rem 0.5rem;
    }

    .no-results-container {
        margin: 0 0.5rem;
    }
}

/* Property Detail Styles */
.property-gallery {
    border-radius: 12px;
    overflow: hidden;
}

.gallery-main {
    position: relative;
    width: 100%;
    height: 500px;
    overflow: hidden;
}

.gallery-main .main-image {
    display: block;
    width: 100%;
    height: 100%;
}

.gallery-main img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-thumbs {
    padding: 10px;
    background: #fff;
}

.gallery-thumbs .swiper-slide {
    width: 120px;
    height: 80px;
    opacity: 0.6;
    cursor: pointer;
    transition: opacity 0.3s;
}

.gallery-thumbs .swiper-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
}

.gallery-thumbs  .swiper-slide-thumb-active {
    opacity: 1;
}

.gallery-thumbs .swiper-slide:hover {
    opacity: 1;
}

.gallery-thumbs .swiper-button-next,
.gallery-thumbs .swiper-button-prev {
    color: var(--primary-color);
    width: 20px;
    height: 40px;
}

.gallery-thumbs .swiper-button-next:after,
.gallery-thumbs .swiper-button-prev:after {
    font-size: 16px;
    font-weight: 700;
}

/* Fancybox Customization */
.fancybox__backdrop {
    background: rgba(0, 0, 0, 0.9);
}

/* Responsive */
@media (max-width: 992px) {
    .gallery-main {
        height: 400px;
    }

    .gallery-thumbs .swiper-slide {
        width: 100px;
        height: 70px;
    }
}

@media (max-width: 768px) {
    .gallery-main {
        height: 300px;
    }

    .gallery-thumbs .swiper-slide {
        width: 80px;
        height: 60px;
    }
}

.property-basic-info .property-title {
    font-size: 23px;
    line-height: 23px;
    font-weight: 700;
    color: #222;
}

.property-address {
    color: #666;
    font-size: 15px;
}

.info-item {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.info-item .label {
    font-size: 13px;
    margin-bottom: 4px;
}

.info-item .value {
    font-size: 15px;
}

.description {
    color: #444;
    line-height: 1.6;
}

.description ul {
    padding-left: 20px;
}

.avatar-lg {
    width: 64px;
    height: 64px;
    object-fit: cover;
}

.note-item {
    display: flex;
    gap: 8px;
    align-items: flex-start;
    font-size: 14px;
    color: #666;
}

.note-item i {
    font-size: 16px;
    margin-top: 2px;
}

/* Responsive */
@media (max-width: 992px) {
    .property-gallery .carousel-item {
        height: 400px;
    }
}

@media (max-width: 768px) {
    .property-gallery .carousel-item {
        height: 300px;
    }

    .info-item {
        padding: 8px;
    }
}

.owner-info .card-body {
    padding: 1.25rem;
}

.owner-info h3 {
    margin-bottom: 0.25rem;
}

.owner-info .text-muted {
    font-size: 0.875rem;
}

/* Gallery Navigation Buttons */
.gallery-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    z-index: 10;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.gallery-nav:hover {
    background: #fff;
}

.gallery-nav i {
    font-size: 20px;
    color: var(--primary-color);
}

.gallery-prev {
    left: 20px;
}

.gallery-next {
    right: 20px;
}

/* Ẩn nút khi hover vào ảnh */
.gallery-main {
    position: relative;
}

.gallery-main .gallery-nav {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-main:hover .gallery-nav {
    opacity: 1;
}

/* Responsive */
@media (max-width: 768px) {
    .gallery-nav {
        width: 32px;
        height: 32px;
    }

    .gallery-nav i {
        font-size: 16px;
    }

    .gallery-prev {
        left: 10px;
    }

    .gallery-next {
        right: 10px;
    }
}

/* Similar Properties Styles */
.similar-properties {
    margin-top: 2rem;
}

.similar-properties h2 {
    color: #333;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

/* Similar properties sử dụng cấu trúc properties-grid giống trang tìm kiếm */
.similar-properties .properties-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-top: 1rem;
}

/* Ensure 4 columns on large screens for similar properties */
@media (min-width: 1200px) {
    .similar-properties .properties-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
    }
}

/* Responsive adjustments for similar properties */
@media (max-width: 1024px) {
    .similar-properties .properties-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .similar-properties .properties-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .similar-properties .properties-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Breadcrumb Styles */
.breadcrumb {
    padding: 0;
    margin: 0;
    background: transparent;
    font-size: 14px;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "\F285";
    font-family: "bootstrap-icons";
    color: #999;
    font-size: 12px;
    line-height: 1;
    margin-top: 5px;
}

.breadcrumb-item a {
    color: #666;
    transition: color 0.2s;
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: 500;
}

@media (max-width: 768px) {
    .breadcrumb {
        font-size: 13px;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        font-size: 11px;
    }
}

/* Login Page Styles */

.input-group-text {
    color: #6c757d;
    border-radius: 8px;
}

.input-group .form-control {
    border-radius: 8px;
}

.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-left: -1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.input-group > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #dee2e6;
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

@media (max-width: 576px) {
    .card-body {
        padding: 1.5rem !important;
    }
}

/* Login Form Styles */
.form-floating {
    position: relative;
}

.form-floating > .form-control {
    height: calc(3.5rem + 2px);
    padding: 1rem 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity .1s ease-in-out, transform .1s ease-in-out;
    color: #6c757d;
}

.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
    padding-top: 2rem;
    padding-bottom: 0.625rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: .65;
    transform: scale(.85) translateY(-0.5rem);
    color: var(--primary-color);
}

.form-floating > .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 117, 21, 0.25);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-secondary {
    border-color: #dee2e6;
}

.btn-outline-secondary:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #000;
}

.card.shadow {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

/* Contact Protection Styles */
/* Styling cho nút điện thoại và Zalo */
.phone-btn, .zalo-btn {
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.phone-btn:before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.2);
    transition: all 0.3s;
}

.phone-btn:hover:before {
    left: 100%;
}

.phone-placeholder {
    font-weight: 500;
}
