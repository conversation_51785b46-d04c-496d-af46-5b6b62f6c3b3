<?php
require_once 'app/models/Property.php';
require_once 'app/models/PropertyType.php';
require_once 'app/models/Ward.php';
require_once 'app/models/User.php';

class UserPropertiesController {
    private $propertyModel;
    private $propertyTypeModel;
    private $wardModel;
    private $userModel;

    public function __construct() {
        $this->propertyModel = new Property();
        $this->propertyTypeModel = new PropertyType();
        $this->wardModel = new Ward();
        $this->userModel = new User();
    }

    public function index() {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập
        if (!isset($_SESSION['user_id'])) {
            header('Location: /thuenhadanang/login');
            exit;
        }

        // Tạo CSRF token nếu chưa có
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }

        // Lấy thông tin user
        $user = $this->userModel->getUserById($_SESSION['user_id']);

        // Lấy trạng thái từ query string (nếu có)
        $status = isset($_GET['status']) ? $_GET['status'] : 'all';

        // Kiểm tra xem có phải là request AJAX không
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

        // Lấy danh sách bất động sản của user theo trạng thái
        $properties = $this->propertyModel->getUserProperties($_SESSION['user_id'], $status);

        // Đếm số lượng tin đăng theo từng trạng thái
        $countAll = $this->propertyModel->countUserProperties($_SESSION['user_id'], 'all');
        $countActive = $this->propertyModel->countUserProperties($_SESSION['user_id'], 'active');
        $countPending = $this->propertyModel->countUserProperties($_SESSION['user_id'], 'pending');
        $countExpired = $this->propertyModel->countUserProperties($_SESSION['user_id'], 'expired');
        $countHidden = $this->propertyModel->countUserProperties($_SESSION['user_id'], 'hidden');

        // Nếu là request AJAX, chỉ trả về phần nội dung bảng
        if ($isAjax) {
            // Thiết lập view và data
            $data = [
                'properties' => $properties,
                'status' => $status
            ];

            // Render view riêng cho AJAX
            require 'app/views/user-properties/ajax-properties-table.php';
            exit;
        }

        // Thiết lập tiêu đề trang
        $title = 'Quản lý tin đăng - Thuê Nhà Đà Nẵng';

        // Thiết lập view và data
        $view = 'user-properties/index';
        $data = [
            'user' => $user,
            'properties' => $properties,
            'status' => $status,
            'counts' => [
                'all' => $countAll,
                'active' => $countActive,
                'pending' => $countPending,
                'expired' => $countExpired,
                'hidden' => $countHidden
            ],
            'error' => $_SESSION['properties_error'] ?? '',
            'success' => $_SESSION['properties_success'] ?? ''
        ];

        // Xóa thông báo trong session
        unset($_SESSION['properties_error']);
        unset($_SESSION['properties_success']);

        // Render layout với view
        require 'app/views/layout.php';
    }

    public function delete($id) {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập
        if (!isset($_SESSION['user_id'])) {
            header('Location: /thuenhadanang/login');
            exit;
        }

        // Lấy thông tin bất động sản
        $property = $this->propertyModel->getPropertyById($id);

        // Kiểm tra quyền sở hữu
        if (!$property || $property->user_id != $_SESSION['user_id']) {
            $_SESSION['properties_error'] = 'Bạn không có quyền xóa tin đăng này!';
            header('Location: /thuenhadanang/dashboard/properties');
            exit;
        }

        // Xóa hình ảnh
        if ($property->images) {
            $images = json_decode($property->images, true);
            if (is_array($images)) {
                $uploadDir = __DIR__ . '/../../public/uploads/properties/';
                foreach ($images as $image) {
                    $imagePath = $uploadDir . $image;
                    if (file_exists($imagePath)) {
                        unlink($imagePath);
                    }
                }
            }
        }

        // Xóa bất động sản
        if ($this->propertyModel->delete($id)) {
            $_SESSION['properties_success'] = 'Xóa tin đăng thành công!';
        } else {
            $_SESSION['properties_error'] = 'Có lỗi xảy ra khi xóa tin đăng!';
        }

        header('Location: /thuenhadanang/dashboard/properties');
        exit;
    }



    public function extend($id) {
        // Bắt đầu output buffering
        ob_start();

        // Tắt hiển thị lỗi để tránh HTML output
        ini_set('display_errors', 0);
        error_reporting(0);

        // Clean output buffer
        ob_clean();

        // Set JSON header first
        header('Content-Type: application/json');

        try {
            // Khởi tạo session nếu chưa có
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            // Kiểm tra đăng nhập
            if (!isset($_SESSION['user_id'])) {
                echo json_encode(['success' => false, 'message' => 'Bạn cần đăng nhập để thực hiện chức năng này']);
                exit;
            }

            // Chỉ chấp nhận POST request với AJAX
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                echo json_encode(['success' => false, 'message' => 'Phương thức không được phép']);
                exit;
            }

            // Kiểm tra CSRF token
            if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                echo json_encode(['success' => false, 'message' => 'Token bảo mật không hợp lệ']);
                exit;
            }

            // Tạo yêu cầu gia hạn
            require_once __DIR__ . '/../models/ExtensionRequest.php';
            $extensionRequestModel = new ExtensionRequest();

            $result = $extensionRequestModel->createRequest($id, $_SESSION['user_id']);

            echo json_encode($result);

        } catch (Exception $e) {
            error_log('UserPropertiesController::extend error: ' . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Có lỗi xảy ra khi xử lý yêu cầu gia hạn']);
        }

        exit;
    }



    public function uploadImage($id) {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập
        if (!isset($_SESSION['user_id'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Unauthorized']);
            exit;
        }

        // Lấy thông tin bất động sản
        $property = $this->propertyModel->getPropertyById($id);

        // Kiểm tra quyền sở hữu
        if (!$property || $property->user_id != $_SESSION['user_id']) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Unauthorized']);
            exit;
        }

        // Kiểm tra method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Invalid request method']);
            exit;
        }

        // Kiểm tra file upload
        if (!isset($_FILES['file']) && !isset($_FILES['image'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'No file uploaded']);
            exit;
        }

        // Xác định tham số file được sử dụng (file hoặc image)
        $fileParam = isset($_FILES['file']) ? 'file' : 'image';

        if ($_FILES[$fileParam]['error'] !== UPLOAD_ERR_OK) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Upload error: ' . $_FILES[$fileParam]['error']]);
            exit;
        }

        try {
            // Định nghĩa thư mục tạm
            $tempUploadDir = __DIR__ . '/../../public/uploads/temp/';

            // Tạo thư mục tạm nếu chưa tồn tại
            if (!file_exists($tempUploadDir)) {
                if (!mkdir($tempUploadDir, 0777, true)) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'error' => 'Failed to create temp directory']);
                    exit;
                } else {
                    // Đảm bảo quyền ghi
                    chmod($tempUploadDir, 0777);
                }
            }

            // Kiểm tra quyền ghi vào thư mục tạm
            if (!is_writable($tempUploadDir)) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Temp directory is not writable']);
                exit;
            }

            $fileTmpName = $_FILES[$fileParam]['tmp_name'];
            $fileName = $_FILES[$fileParam]['name'];
            $fileType = $_FILES[$fileParam]['type'];
            $fileSize = $_FILES[$fileParam]['size'];

            // Kiểm tra định dạng file
            $validExtensions = ['jpg', 'jpeg', 'png'];
            $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

            if (!in_array($fileExtension, $validExtensions)) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Invalid file format. Only JPG, JPEG, and PNG are allowed.']);
                exit;
            }

            // Kiểm tra kích thước hình ảnh
            $imageInfo = getimagesize($fileTmpName);
            if ($imageInfo[0] < 300 || $imageInfo[1] < 300) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Image dimensions too small: ' . $imageInfo[0] . 'x' . $imageInfo[1] . '. Minimum size is 300x300 pixels.']);
                exit;
            }

            // Tạo tên file tạm mới với microtime để đảm bảo tính duy nhất
            $uniqueTime = microtime(true);
            $newFileName = 'temp_' . $uniqueTime . '_' . mt_rand(10000, 99999) . '.jpg';
            $targetFile = $tempUploadDir . $newFileName;

            // Upload file vào thư mục tạm
            if (move_uploaded_file($fileTmpName, $targetFile)) {
                // Ghi log để debug
                error_log('Successfully uploaded image to temp directory: ' . $newFileName);

                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'filename' => $newFileName,
                    'filepath' => '/thuenhadanang/public/uploads/temp/' . $newFileName
                ]);
            } else {
                error_log('Failed to move uploaded file from ' . $fileTmpName . ' to ' . $targetFile);
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Failed to upload file to temp directory']);
            }
        } catch (Exception $e) {
            error_log('Exception in uploadImage: ' . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Exception: ' . $e->getMessage()]);
        }
        exit;
    }

    public function deleteImage($id) {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập
        if (!isset($_SESSION['user_id'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Unauthorized']);
            exit;
        }

        // Lấy thông tin bất động sản
        $property = $this->propertyModel->getPropertyById($id);

        // Kiểm tra quyền sở hữu
        if (!$property || $property->user_id != $_SESSION['user_id']) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Unauthorized']);
            exit;
        }

        // Kiểm tra method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Invalid request method']);
            exit;
        }

        // Kiểm tra tên file
        if (!isset($_POST['filename']) || empty($_POST['filename'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'No filename provided']);
            exit;
        }

        $filename = $_POST['filename'];

        // Kiểm tra xem file là file tạm hay file chính thức
        if (strpos($filename, 'temp_') === 0) {
            // Xóa file tạm
            $tempUploadDir = __DIR__ . '/../../public/uploads/temp/';
            $filePath = $tempUploadDir . $filename;

            // Kiểm tra xem file có tồn tại không
            if (!file_exists($filePath)) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'File not found, already deleted']);
                exit;
            }

            // Xóa file tạm
            if (unlink($filePath)) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true]);
                error_log('Successfully deleted temp file: ' . $filename);
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Failed to delete temp file']);
                error_log('Failed to delete temp file: ' . $filename);
            }
        } else {
            // Xóa file chính thức
            $uploadDir = __DIR__ . '/../../public/uploads/properties/';
            $filePath = $uploadDir . $filename;

            // Kiểm tra xem file có tồn tại không
            if (!file_exists($filePath)) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'File not found']);
                exit;
            }

            // Lấy danh sách hình ảnh hiện tại
            $currentImages = [];
            if ($property->images) {
                $currentImages = json_decode($property->images, true) ?? [];
            }

            // Kiểm tra xem file có trong danh sách hình ảnh không
            if (!in_array($filename, $currentImages)) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'File not in property images']);
                exit;
            }

            // Xóa file
            if (unlink($filePath)) {
                // Xóa khỏi mảng hình ảnh
                $key = array_search($filename, $currentImages);
                if ($key !== false) {
                    unset($currentImages[$key]);
                    $currentImages = array_values($currentImages); // Reindex array
                }

                // Cập nhật main_image nếu cần
                $mainImage = $property->main_image;
                if ($mainImage == $filename) {
                    $mainImage = !empty($currentImages) ? reset($currentImages) : '';
                }

                // Cập nhật bất động sản
                $propertyData = [
                    'id' => $id,
                    'images' => json_encode($currentImages),
                    'main_image' => $mainImage
                ];

                if ($this->propertyModel->updateImages($propertyData)) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => true]);
                    error_log('Successfully deleted property image: ' . $filename);
                } else {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'error' => 'Failed to update property images']);
                    error_log('Failed to update property images after deleting: ' . $filename);
                }
            } else {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Failed to delete file']);
                error_log('Failed to delete property image: ' . $filename);
            }
        }
        exit;
    }

    public function edit($id) {
        try {
            // Khởi tạo session nếu chưa có
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            // Kiểm tra đăng nhập
            if (!isset($_SESSION['user_id'])) {
                header('Location: /thuenhadanang/login');
                exit;
            }

            // Lấy thông tin bất động sản
            $property = $this->propertyModel->getPropertyById($id);
            error_log('Property data: ' . json_encode($property));

            // Kiểm tra quyền sở hữu
            if (!$property || $property->user_id != $_SESSION['user_id']) {
                $_SESSION['properties_error'] = 'Bạn không có quyền chỉnh sửa tin đăng này!';
                header('Location: /thuenhadanang/dashboard/properties');
                exit;
            }

            // Lấy thông tin user
            $user = $this->userModel->getUserById($_SESSION['user_id']);

            // Lấy danh sách loại hình bất động sản
            $propertyTypes = $this->propertyTypeModel->getAllPropertyTypes();

            // Lấy danh sách phường/xã
            $wards = $this->wardModel->getAllWards();

            // Lấy danh sách hướng
            $directions = $this->propertyModel->getAllDirections();
            error_log('Directions data: ' . json_encode($directions));

            // Lấy danh sách hình ảnh
            $propertyImages = $this->propertyModel->getPropertyImages($id);
            error_log('Property images: ' . json_encode($propertyImages));

            // Thiết lập tiêu đề trang
            $title = 'Chỉnh sửa tin đăng - Thuê Nhà Đà Nẵng';

            // Thiết lập view và data
            $view = 'user-properties/edit';
            $data = [
                'user' => $user,
                'property' => $property,
                'propertyTypes' => $propertyTypes,
                'wards' => $wards,
                'directions' => $directions,
                'propertyImages' => $propertyImages,
                'error' => $_SESSION['properties_error'] ?? '',
                'success' => $_SESSION['properties_success'] ?? ''
            ];

            // Xóa thông báo trong session
            unset($_SESSION['properties_error']);
            unset($_SESSION['properties_success']);

            // Render layout với view
            error_log('About to render view: ' . $view);
            require 'app/views/layout.php';
        } catch (Exception $e) {
            error_log('Error in edit method: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            $_SESSION['properties_error'] = 'Có lỗi xảy ra: ' . $e->getMessage();
            header('Location: /thuenhadanang/dashboard/properties');
            exit;
        }
    }

    public function update($id) {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập
        if (!isset($_SESSION['user_id'])) {
            header('Location: /thuenhadanang/login');
            exit;
        }

        // Kiểm tra method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /thuenhadanang/dashboard/properties');
            exit;
        }

        // Lấy thông tin bất động sản
        $property = $this->propertyModel->getPropertyById($id);

        // Kiểm tra quyền sở hữu
        if (!$property || $property->user_id != $_SESSION['user_id']) {
            $_SESSION['properties_error'] = 'Bạn không có quyền chỉnh sửa tin đăng này!';
            header('Location: /thuenhadanang/dashboard/properties');
            exit;
        }

        // Validate dữ liệu
        $errors = [];

        if (empty($_POST['title'])) {
            $errors[] = 'Vui lòng nhập tiêu đề';
        }

        if (empty($_POST['description'])) {
            $errors[] = 'Vui lòng nhập mô tả';
        }

        if (empty($_POST['type_id'])) {
            $errors[] = 'Vui lòng chọn loại bất động sản';
        }

        if (empty($_POST['ward_id'])) {
            $errors[] = 'Vui lòng chọn phường/xã';
        }

        if (empty($_POST['street'])) {
            $errors[] = 'Vui lòng nhập tên đường';
        }

        if (empty($_POST['address'])) {
            $errors[] = 'Vui lòng nhập địa chỉ đầy đủ';
        }

        if (empty($_POST['price']) || !is_numeric($_POST['price'])) {
            $errors[] = 'Vui lòng nhập giá thuê hợp lệ';
        }

        if (empty($_POST['area']) || !is_numeric($_POST['area'])) {
            $errors[] = 'Vui lòng nhập diện tích hợp lệ';
        }

        // Nếu có lỗi
        if (!empty($errors)) {
            $_SESSION['properties_error'] = implode('<br>', $errors);
            header('Location: /thuenhadanang/property/edit/' . $id);
            exit;
        }

        // Xử lý slug
        $slug = !empty($_POST['slug']) ? trim($_POST['slug']) : '';

        // Nếu slug rỗng, tạo slug từ tiêu đề
        if (empty($slug)) {
            $slug = $this->propertyModel->createSlug($_POST['title']);
        } else {
            // Kiểm tra xem slug đã tồn tại chưa
            if ($this->propertyModel->isSlugExists($slug, $id)) {
                $_SESSION['properties_error'] = 'Slug đã tồn tại, vui lòng chọn slug khác';
                header('Location: /thuenhadanang/property/edit/' . $id);
                exit;
            }
        }

        // Xử lý hình ảnh
        $uploadDir = __DIR__ . '/../../public/uploads/properties/';
        $tempUploadDir = __DIR__ . '/../../public/uploads/temp/';
        $currentImages = [];
        $mainImage = $property->main_image;
        $tempImages = [];
        $processedImages = []; // Để theo dõi hình ảnh đã xử lý, tránh trùng lặp

        // Lấy danh sách hình ảnh hiện tại
        if ($property->images) {
            $currentImages = json_decode($property->images, true) ?? [];
        }

        // Xử lý xóa hình ảnh
        if (!empty($_POST['deleted_images'])) {
            $deletedImages = json_decode($_POST['deleted_images'], true);
            if (is_array($deletedImages)) {
                foreach ($deletedImages as $image) {
                    // Kiểm tra xem file là file tạm hay file chính thức
                    if (strpos($image, 'temp_') === 0) {
                        // Xóa file tạm
                        $imagePath = $tempUploadDir . $image;
                        if (file_exists($imagePath)) {
                            unlink($imagePath);
                            error_log('Deleted temp image: ' . $imagePath);
                        }
                    } else {
                        // Xóa file chính thức
                        $imagePath = $uploadDir . $image;
                        if (file_exists($imagePath)) {
                            unlink($imagePath);
                            error_log('Deleted image: ' . $imagePath);
                        }

                        // Xóa khỏi mảng hình ảnh hiện tại
                        $key = array_search($image, $currentImages);
                        if ($key !== false) {
                            unset($currentImages[$key]);
                        }

                        // Nếu hình ảnh chính bị xóa, đặt lại hình ảnh chính
                        if ($mainImage == $image) {
                            $mainImage = !empty($currentImages) ? reset($currentImages) : '';
                        }
                    }
                }
                // Reindex array after deletion
                $currentImages = array_values($currentImages);
            }
        }

        // Xử lý thứ tự hình ảnh và hình ảnh tạm
        if (!empty($_POST['image_order'])) {
            $imagesOrder = json_decode($_POST['image_order'], true);
            if (is_array($imagesOrder)) {
                // Xóa mảng hình ảnh hiện tại và tạm để tránh trùng lặp
                $currentImages = [];
                $tempImages = [];

                // Tách hình ảnh tạm và hình ảnh hiện tại
                foreach ($imagesOrder as $image) {
                    if (strpos($image, 'temp_') === 0) {
                        if (!in_array($image, $tempImages)) { // Tránh trùng lặp
                            $tempImages[] = $image;
                        }
                    } else {
                        if (!in_array($image, $currentImages)) { // Tránh trùng lặp
                            $currentImages[] = $image;
                        }
                    }
                }

                // Log để debug
                error_log('Current images after processing order: ' . json_encode($currentImages));
                error_log('Temp images after processing order: ' . json_encode($tempImages));
            }
        }

        // Xử lý hình ảnh chính
        if (!empty($_POST['main_image'])) {
            $mainImage = $_POST['main_image'];
        }

        // Di chuyển hình ảnh từ thư mục tạm sang thư mục chính thức
        if (!empty($tempImages)) {
            // Tạo thư mục nếu chưa tồn tại
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            foreach ($tempImages as $tempImage) {
                $tempFilePath = $tempUploadDir . $tempImage;

                // Kiểm tra file tồn tại
                if (file_exists($tempFilePath)) {
                    // Tạo tên file mới với microtime để đảm bảo tính duy nhất
                    $uniqueTime = microtime(true);
                    $newFileName = 'property_' . $id . '_' . $uniqueTime . '_' . mt_rand(10000, 99999) . '.jpg';
                    $targetFile = $uploadDir . $newFileName;

                    // Thêm độ trễ nhỏ để đảm bảo tên file khác nhau
                    usleep(10000); // 10ms

                    // Di chuyển file từ thư mục tạm sang thư mục chính
                    if (copy($tempFilePath, $targetFile)) {
                        // Ghi log để debug
                        error_log('File copied successfully from ' . $tempFilePath . ' to ' . $targetFile);

                        // Xóa file tạm sau khi copy thành công
                        if (@unlink($tempFilePath)) {
                            error_log('Temp file deleted: ' . $tempFilePath);
                        } else {
                            error_log('Failed to delete temp file: ' . $tempFilePath);
                        }

                        // Thêm vào mảng hình ảnh (kiểm tra trùng lặp)
                        if (!in_array($newFileName, $processedImages)) {
                            $currentImages[] = $newFileName;
                            $processedImages[] = $newFileName;
                            error_log('Added new image to current images: ' . $newFileName);
                        } else {
                            error_log('Duplicate image detected and skipped: ' . $newFileName);
                        }

                        // Cập nhật ảnh chính nếu cần
                        if ($mainImage == $tempImage) {
                            $mainImage = $newFileName;
                            error_log('Updated main image from temp: ' . $tempImage . ' to ' . $newFileName);
                        }
                    } else {
                        error_log('Failed to copy file from ' . $tempFilePath . ' to ' . $targetFile);
                    }
                } else {
                    error_log('Temp file not found: ' . $tempFilePath);
                }
            }
        }

        // Xử lý upload hình ảnh mới (từ input file truyền thống, nếu có)
        if (!empty($_FILES['property_images']['name'][0])) {
            // Tạo thư mục nếu chưa tồn tại
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            foreach ($_FILES['property_images']['tmp_name'] as $key => $tmp_name) {
                if ($_FILES['property_images']['error'][$key] === 0) {
                    $fileName = $_FILES['property_images']['name'][$key];
                    $fileSize = $_FILES['property_images']['size'][$key];
                    $fileTmp = $_FILES['property_images']['tmp_name'][$key];
                    $fileType = $_FILES['property_images']['type'][$key];

                    // Kiểm tra định dạng file
                    $validExtensions = ['jpg', 'jpeg', 'png'];
                    $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

                    if (in_array($fileExtension, $validExtensions)) {
                        // Tạo tên file mới với microtime để đảm bảo tính duy nhất
                        $uniqueTime = microtime(true);
                        $newFileName = 'property_' . $id . '_' . $uniqueTime . '_' . mt_rand(10000, 99999) . '.' . $fileExtension;
                        $targetFile = $uploadDir . $newFileName;

                        // Thêm độ trễ nhỏ để đảm bảo tên file khác nhau
                        usleep(10000); // 10ms

                        // Upload file
                        if (move_uploaded_file($fileTmp, $targetFile)) {
                            // Thêm vào mảng hình ảnh (kiểm tra trùng lặp)
                            if (!in_array($newFileName, $processedImages)) {
                                $currentImages[] = $newFileName;
                                $processedImages[] = $newFileName;
                                error_log('Added new image to current images: ' . $newFileName);
                            } else {
                                error_log('Duplicate image detected and skipped: ' . $newFileName);
                            }

                            // Đặt ảnh chính nếu chưa có
                            if (empty($mainImage)) {
                                $mainImage = $newFileName;
                            }

                            error_log('Uploaded new image: ' . $newFileName);
                        } else {
                            error_log('Failed to upload image: ' . $fileName);
                        }
                    } else {
                        error_log('Invalid file extension: ' . $fileExtension);
                    }
                }
            }
        }

        // Chuẩn bị dữ liệu
        $propertyData = [
            'id' => $id,
            'title' => trim($_POST['title']),
            'slug' => $slug,
            'description' => trim($_POST['description']),
            'type_id' => intval($_POST['type_id']),
            'address' => trim($_POST['address']),
            'ward_id' => !empty($_POST['ward_id']) ? intval($_POST['ward_id']) : null,
            'street' => !empty($_POST['street']) ? trim($_POST['street']) : null,
            'city' => !empty($_POST['city']) ? trim($_POST['city']) : 'Đà Nẵng',
            'area' => !empty($_POST['area']) ? floatval($_POST['area']) : null,
            'price' => floatval($_POST['price']),
            'price_period' => $_POST['price_period'],
            'bedrooms' => !empty($_POST['bedrooms']) ? intval($_POST['bedrooms']) : null,
            'bathrooms' => !empty($_POST['bathrooms']) ? intval($_POST['bathrooms']) : null,
            'direction' => !empty($_POST['direction']) ? $_POST['direction'] : null,
            'video_url' => !empty($_POST['video_url']) ? trim($_POST['video_url']) : null,
            'images' => json_encode($currentImages),
            'main_image' => $mainImage,
            'status' => 'display', // Giữ nguyên trạng thái hiển thị
            'active' => 0 // Chuyển về trạng thái chờ duyệt
        ];

        // Cập nhật bất động sản
        if ($this->propertyModel->update($propertyData)) {
            $_SESSION['properties_success'] = 'Cập nhật tin đăng thành công! Tin đăng của bạn đã được chuyển sang trạng thái chờ duyệt.';
            header('Location: /thuenhadanang/dashboard/properties');
        } else {
            $_SESSION['properties_error'] = 'Có lỗi xảy ra khi cập nhật tin đăng!';
            header('Location: /thuenhadanang/property/edit/' . $id);
        }
        exit;
    }
}
