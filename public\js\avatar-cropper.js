/**
 * Avatar Cropper - Xử lý crop ảnh đại diện
 */
document.addEventListener('DOMContentLoaded', function() {
    // Các biến toàn cục
    let cropper;
    let cropperModal = document.getElementById('cropperModal');
    let cropperImage = document.getElementById('cropperImage');
    let avatarInput = document.getElementById('avatarInput');
    let avatarPreview = document.getElementById('avatarPreview');
    let cropButton = document.getElementById('cropButton');
    let avatarDataInput = document.getElementById('avatarData');
    
    // Khởi tạo modal cropper nếu các phần tử tồn tại
    if (cropperModal && avatarInput) {
        // Khởi tạo Bootstrap modal
        const modal = new bootstrap.Modal(cropperModal);
        
        // X<PERSON> lý khi người dùng chọn file
        avatarInput.addEventListener('change', function(e) {
            if (e.target.files.length) {
                // Lấy file đã chọn
                const file = e.target.files[0];
                
                // Kiểm tra loại file
                if (!file.type.match('image.*')) {
                    alert('Vui lòng chọn file hình ảnh');
                    return;
                }
                
                // Đọc file và hiển thị trong modal
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Hiển thị ảnh trong modal
                    cropperImage.src = e.target.result;
                    
                    // Hiển thị modal
                    modal.show();
                    
                    // Khởi tạo cropper sau khi modal hiển thị
                    cropperModal.addEventListener('shown.bs.modal', function() {
                        // Hủy cropper cũ nếu có
                        if (cropper) {
                            cropper.destroy();
                        }
                        
                        // Khởi tạo cropper mới
                        cropper = new Cropper(cropperImage, {
                            aspectRatio: 1, // Tỷ lệ 1:1 cho avatar
                            viewMode: 1,    // Giới hạn khung nhìn trong canvas
                            dragMode: 'move',
                            guides: true,
                            center: true,
                            highlight: true,
                            cropBoxMovable: true,
                            cropBoxResizable: true,
                            toggleDragModeOnDblclick: false
                        });
                    }, { once: true });
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Xử lý khi người dùng nhấn nút Crop
        cropButton.addEventListener('click', function() {
            if (!cropper) return;
            
            // Lấy dữ liệu ảnh đã crop
            const canvas = cropper.getCroppedCanvas({
                width: 300,
                height: 300,
                minWidth: 100,
                minHeight: 100,
                maxWidth: 1000,
                maxHeight: 1000,
                fillColor: '#fff',
                imageSmoothingEnabled: true,
                imageSmoothingQuality: 'high',
            });
            
            // Chuyển đổi canvas thành dữ liệu base64
            const croppedImageData = canvas.toDataURL('image/jpeg', 0.8);
            
            // Hiển thị ảnh đã crop
            if (avatarPreview) {
                avatarPreview.src = croppedImageData;
                avatarPreview.style.display = 'block';
            }
            
            // Lưu dữ liệu ảnh vào input hidden
            if (avatarDataInput) {
                avatarDataInput.value = croppedImageData;
            }
            
            // Đóng modal
            modal.hide();
        });
    }
});
