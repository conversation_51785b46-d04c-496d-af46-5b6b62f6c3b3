<?php
require_once BASE_PATH . '/app/models/Property.php';
require_once BASE_PATH . '/app/models/PropertyType.php';
require_once BASE_PATH . '/app/models/Ward.php';

class SearchController extends BaseController {
    private $propertyModel;
    private $propertyTypeModel;
    private $wardModel;
    private $urlHandler;
    private $viewData = [];

    public function __construct() {
        $this->propertyModel = new Property();
        $this->propertyTypeModel = new PropertyType();
        $this->wardModel = new Ward();

        // Load UrlHandler for centralized URL processing
        require_once BASE_PATH . '/app/libraries/UrlHandler.php';
        $this->urlHandler = new UrlHandler();
    }

    // Phương thức mới để xử lý URL có cả type và ward
    public function filterByTypeAndWard($typeSlug, $wardSlug, $params = []) {
        error_log('filterByTypeAndWard called with type: ' . $typeSlug . ', ward: ' . $wardSlug);
        error_log('Params: ' . json_encode($params));
        error_log('GET params: ' . json_encode($_GET));

        // X<PERSON> lý trường hợp đặc biệt cho "nha-dat"
        if ($typeSlug === 'nha-dat' || $typeSlug === '') {
            if ($typeSlug === 'nha-dat') {
                // Giữ nguyên slug "nha-dat" để đảm bảo URL không bị thay đổi
                $propertyTypeName = 'Nhà đất';
                error_log('Special case: type is "nha-dat", preserving it');
            } else {
                $typeSlug = ''; // Đặt lại thành rỗng để không lọc theo loại hình
                $propertyTypeName = 'Nhà đất';
                error_log('Special case: type is empty, setting to empty string');
            }
        } else {
            // Lấy thông tin loại bất động sản
            $propertyType = $this->propertyTypeModel->getPropertyTypeBySlug($typeSlug);
            $propertyTypeName = $propertyType ? $propertyType->name : 'Nhà đất';
        }

        // Kiểm tra URL hiện tại
        $currentUrl = $_SERVER['REQUEST_URI'];
        // Nếu URL chứa "cho-thue-nha-dat", luôn sử dụng "Nhà đất" cho tiêu đề
        if (strpos($currentUrl, 'cho-thue-nha-dat') !== false) {
            $propertyTypeName = 'Nhà đất';
            error_log('URL contains cho-thue-nha-dat, forcing propertyTypeName to "Nhà đất"');
        }

        // Lấy thông tin phường/xã
        $ward = $this->wardModel->getWardBySlug($wardSlug);
        $wardName = $ward ? $ward->name : '';

        // Xử lý các tham số bổ sung
        $price = isset($params['price']) ? $params['price'] : '';
        $area = isset($params['area']) ? $params['area'] : '';
        $bedrooms = isset($params['bedrooms']) ? $params['bedrooms'] : '';
        $bathrooms = isset($params['bathrooms']) ? $params['bathrooms'] : (isset($_GET['bathrooms']) ? $_GET['bathrooms'] : '');
        $direction = isset($params['direction']) ? $params['direction'] : (isset($_GET['direction']) ? $_GET['direction'] : '');
        $sort = isset($params['sort']) ? $params['sort'] : (isset($_GET['sort']) ? $_GET['sort'] : 'default');

        // Lấy từ khóa tìm kiếm từ query string nếu có
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';

        // Log các tham số
        error_log('Processed parameters:');
        error_log('Price: ' . $price);
        error_log('Area: ' . $area);
        error_log('Bedrooms: ' . $bedrooms);
        error_log('Bathrooms: ' . $bathrooms);
        error_log('Direction: ' . $direction);

        // Tạo tiêu đề động
        $currentMonth = date('m');
        $currentYear = date('Y');

        // Tạo tiêu đề dựa trên các tham số
        if (!empty($typeSlug) && !empty($wardSlug)) {
            $dynamicTitle = "Cho thuê {$propertyTypeName} tại {$wardName} Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        } else if (!empty($typeSlug)) {
            $dynamicTitle = "Cho thuê {$propertyTypeName} tại Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        } else if (!empty($wardSlug)) {
            $dynamicTitle = "Cho thuê nhà đất tại {$wardName} Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        } else {
            $dynamicTitle = "Cho thuê nhà đất tại Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        }

        // Lấy danh sách tất cả loại bất động sản và phường/xã cho form tìm kiếm
        $propertyTypes = $this->propertyTypeModel->getAllPropertyTypes();
        $wards = $this->wardModel->getWardsByStatus(1);

        // Danh sách các hướng nhà
        $directions = [
            'dong' => 'Đông',
            'tay' => 'Tây',
            'nam' => 'Nam',
            'bac' => 'Bắc',
            'dong-bac' => 'Đông Bắc',
            'dong-nam' => 'Đông Nam',
            'tay-bac' => 'Tây Bắc',
            'tay-nam' => 'Tây Nam'
        ];

        // Danh sách các khoảng diện tích
        $areas = [
            '0-20' => 'Dưới 20m²',
            '20-30' => '20 - 30m²',
            '30-50' => '30 - 50m²',
            '50-70' => '50 - 70m²',
            '70-90' => '70 - 90m²',
            '90+' => 'Trên 90m²'
        ];

        // Nếu có từ khóa tìm kiếm, sử dụng searchProperties thay vì getPropertiesByTypeAndWard
        if (!empty($keyword)) {
            error_log('Using searchProperties with keyword: ' . $keyword);
            $properties = $this->propertyModel->searchProperties($keyword, $typeSlug, $wardSlug, $price, $sort, $bedrooms, $bathrooms, $direction, $area);
        } else {
            // Lấy bất động sản theo loại và phường/xã (không có từ khóa)
            $properties = $this->propertyModel->getPropertiesByTypeAndWard($typeSlug, $wardSlug, $price, $area, $bedrooms, $sort, $bathrooms, $direction);
        }

        // Chuẩn bị dữ liệu cho view
        $data = [
            'title' => $dynamicTitle . ' - Thuê Nhà Đà Nẵng',
            'view' => 'search',
            'properties' => $properties,
            'propertyTypes' => $propertyTypes,
            'wards' => $wards,
            'directions' => $directions,
            'areas' => $areas,
            'keyword' => $keyword,
            'selectedType' => $typeSlug,
            'selectedWard' => $wardSlug,
            'selectedPrice' => $price,
            'selectedSort' => $sort,
            'selectedBedrooms' => $bedrooms,
            'selectedBathrooms' => $bathrooms,
            'selectedDirection' => $direction,
            'selectedArea' => $area,
            'dynamicTitle' => $dynamicTitle,
            'propertyTypeName' => $propertyTypeName
        ];

        // Lưu dữ liệu để sử dụng trong các phương thức khác
        $this->viewData = $data;

        // Hiển thị view
        $this->view('search', $data);
    }

    public function index() {
        // Debug: In ra các tham số GET
        error_log('GET parameters in SearchController: ' . json_encode($_GET));

        // Lấy tham số tìm kiếm từ URL
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
        $type = isset($_GET['type']) ? trim($_GET['type']) : '';
        $ward = isset($_GET['ward']) ? trim($_GET['ward']) : ''; // Không chuyển thành chữ thường
        $price = isset($_GET['price']) ? trim($_GET['price']) : '';
        $sort = isset($_GET['sort']) ? trim($_GET['sort']) : 'default';
        $bedrooms = isset($_GET['bedrooms']) ? trim($_GET['bedrooms']) : '';
        $bathrooms = isset($_GET['bathrooms']) ? trim($_GET['bathrooms']) : '';
        $direction = isset($_GET['direction']) ? trim($_GET['direction']) : '';
        $area = isset($_GET['area']) ? trim($_GET['area']) : '';

        // Debug parameters
        error_log('Parameters in SearchController:');
        error_log('Type: "' . $type . '"');
        error_log('Ward: "' . $ward . '"');
        error_log('Price: "' . $price . '"');
        error_log('Bedrooms: "' . $bedrooms . '"');
        error_log('Area: "' . $area . '"');

        // Xử lý trường hợp đặc biệt cho "nha-dat"
        if ($type === 'nha-dat') {
            $type = ''; // Đặt lại thành rỗng để không lọc theo loại hình
            error_log('Special case: type is "nha-dat", setting to empty string');
        }
        // Kiểm tra xem type có tồn tại trong cơ sở dữ liệu không
        else if (!empty($type)) {
            $typeObj = $this->propertyTypeModel->getPropertyTypeBySlug($type);
            if ($typeObj) {
                error_log('Found property type in database: "' . $typeObj->name . '" (Slug: "' . $typeObj->slug . '")');
                // Đảm bảo sử dụng slug chính xác từ cơ sở dữ liệu
                $type = $typeObj->slug;
            } else {
                error_log('Property type not found in database: "' . $type . '"');
                // Tìm kiếm loại hình tương tự
                $similarType = $this->propertyTypeModel->getSimilarPropertyType($type);
                if ($similarType) {
                    error_log('Found similar property type: "' . $similarType->name . '" (Slug: "' . $similarType->slug . '")');
                    $type = $similarType->slug;
                }
            }
        }

        // Lấy danh sách loại hình bất động sản và phường/xã cho form tìm kiếm
        $propertyTypes = $this->propertyTypeModel->getAllPropertyTypes();
        $wards = $this->wardModel->getAllWards();

        // Lấy tên loại hình bất động sản cho tiêu đề động
        $propertyTypeName = 'nhà đất';
        if (!empty($type)) {
            $propertyType = $this->propertyTypeModel->getPropertyTypeBySlug($type);
            if ($propertyType) {
                $propertyTypeName = mb_strtolower($propertyType->name, 'UTF-8');
            }
        }

        // Kiểm tra URL hiện tại
        $currentUrl = $_SERVER['REQUEST_URI'];
        // Nếu URL chứa "cho-thue-nha-dat", luôn sử dụng "nhà đất" cho tiêu đề
        if (strpos($currentUrl, 'cho-thue-nha-dat') !== false) {
            $propertyTypeName = 'nhà đất';
            error_log('URL contains cho-thue-nha-dat, forcing propertyTypeName to "nhà đất"');
        }

        // Lấy tên phường/xã cho tiêu đề động
        $wardName = '';
        if (!empty($ward)) {
            $wardObj = $this->wardModel->getWardBySlug($ward);
            if ($wardObj) {
                $wardName = $wardObj->name;
            }
        }

        // Tạo tiêu đề động
        $currentMonth = date('m');
        $currentYear = date('Y');

        // Tạo tiêu đề dựa trên các tham số
        if (!empty($type) && !empty($ward)) {
            $dynamicTitle = "Cho thuê {$propertyTypeName} tại {$wardName} Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        } else if (!empty($type)) {
            $dynamicTitle = "Cho thuê {$propertyTypeName} tại Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        } else if (!empty($ward)) {
            $dynamicTitle = "Cho thuê nhà đất tại {$wardName} Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        } else {
            $dynamicTitle = "Cho thuê nhà đất tại Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        }

        // Danh sách các hướng nhà
        $directions = [
            'dong' => 'Đông',
            'tay' => 'Tây',
            'nam' => 'Nam',
            'bac' => 'Bắc',
            'dong-bac' => 'Đông Bắc',
            'dong-nam' => 'Đông Nam',
            'tay-bac' => 'Tây Bắc',
            'tay-nam' => 'Tây Nam'
        ];

        // Danh sách các khoảng diện tích
        $areas = [
            '0-20' => 'Dưới 20m²',
            '20-30' => '20 - 30m²',
            '30-50' => '30 - 50m²',
            '50-70' => '50 - 70m²',
            '70-90' => '70 - 90m²',
            '90+' => 'Trên 90m²'
        ];

        // Tìm kiếm bất động sản
        $properties = $this->propertyModel->searchProperties($keyword, $type, $ward, $price, $sort, $bedrooms, $bathrooms, $direction, $area);

        // Chuẩn bị dữ liệu cho view
        $data = [
            'title' => $dynamicTitle . ' - Thuê Nhà Đà Nẵng',
            'view' => 'search',
            'properties' => $properties,
            'propertyTypes' => $propertyTypes,
            'wards' => $wards,
            'directions' => $directions,
            'areas' => $areas,
            'keyword' => $keyword,
            'selectedType' => $type,
            'selectedWard' => $ward,
            'selectedPrice' => $price,
            'selectedSort' => $sort,
            'selectedBedrooms' => $bedrooms,
            'selectedBathrooms' => $bathrooms,
            'selectedDirection' => $direction,
            'selectedArea' => $area,
            'dynamicTitle' => $dynamicTitle,
            'propertyTypeName' => $propertyTypeName
        ];

        // Lưu dữ liệu để sử dụng trong các phương thức khác
        $this->viewData = $data;

        // Hiển thị view
        $this->view('search', $data);
    }

    // Phương thức tạo URL sắp xếp sử dụng UrlHandler
    public function buildSortUrl($sort) {
        // Lấy URL hiện tại và phân tích bằng UrlHandler
        $currentUrl = $_SERVER['REQUEST_URI'];
        $path = preg_replace('/^\/thuenhadanang\//', '', parse_url($currentUrl, PHP_URL_PATH));

        // Sử dụng UrlHandler để phân tích URL hiện tại
        $parsedParams = $this->urlHandler->parseUrl($path);

        // Thêm các tham số từ query string (loại bỏ các tham số không mong muốn)
        $queryParams = $_GET;

        // Loại bỏ các tham số không mong muốn
        $unwantedParams = ['url', 'action', 'controller'];
        foreach ($unwantedParams as $param) {
            unset($queryParams[$param]);
        }

        if (isset($parsedParams['additional_params'])) {
            $additionalParams = $parsedParams['additional_params'];
            // Loại bỏ các tham số không mong muốn từ additional_params
            foreach ($unwantedParams as $param) {
                unset($additionalParams[$param]);
            }
            $queryParams = array_merge($queryParams, $additionalParams);
        }

        // Cập nhật tham số sort
        if ($sort === 'default') {
            unset($queryParams['sort']);
        } else {
            $queryParams['sort'] = $sort;
        }

        // Tạo params cho UrlHandler
        $params = [
            'type' => $parsedParams['type'],
            'ward' => $parsedParams['ward'],
            'price' => $parsedParams['price'],
            'area' => $parsedParams['area'],
            'bedrooms' => $parsedParams['bedrooms'],
            'bathrooms' => $parsedParams['bathrooms'],
            'direction' => $parsedParams['direction'],
            'additional_params' => $queryParams
        ];

        // Sử dụng UrlHandler để tạo URL mới
        return $this->urlHandler->buildUrl($params);
    }

    // Phương thức tạo URL bộ lọc thân thiện với SEO sử dụng UrlHandler
    public function buildFilterUrl($paramName, $paramValue) {
        // Lấy URL hiện tại và phân tích bằng UrlHandler
        $currentUrl = $_SERVER['REQUEST_URI'];
        $path = preg_replace('/^\/thuenhadanang\//', '', parse_url($currentUrl, PHP_URL_PATH));

        // Sử dụng UrlHandler để phân tích URL hiện tại
        $parsedParams = $this->urlHandler->parseUrl($path);

        // Thêm các tham số từ query string (loại bỏ các tham số không mong muốn)
        $queryParams = $_GET;

        // Loại bỏ các tham số không mong muốn
        $unwantedParams = ['url', 'action', 'controller'];
        foreach ($unwantedParams as $param) {
            unset($queryParams[$param]);
        }

        if (isset($parsedParams['additional_params'])) {
            $additionalParams = $parsedParams['additional_params'];
            // Loại bỏ các tham số không mong muốn từ additional_params
            foreach ($unwantedParams as $param) {
                unset($additionalParams[$param]);
            }
            $queryParams = array_merge($queryParams, $additionalParams);
        }

        // Cập nhật hoặc thêm tham số mới
        if (empty($paramValue)) {
            // Nếu giá trị trống, loại bỏ tham số
            unset($parsedParams[$paramName]);
            unset($queryParams[$paramName]);
        } else {
            // Cập nhật tham số
            if (in_array($paramName, ['bathrooms', 'direction'])) {
                // Các tham số này đi vào query string
                $queryParams[$paramName] = $paramValue;
            } else {
                // Các tham số khác đi vào URL path
                $parsedParams[$paramName] = $paramValue;
            }
        }

        // Tạo params cho UrlHandler
        $params = [
            'type' => $parsedParams['type'],
            'ward' => $parsedParams['ward'],
            'price' => $parsedParams['price'],
            'area' => $parsedParams['area'],
            'bedrooms' => $parsedParams['bedrooms'],
            'bathrooms' => isset($queryParams['bathrooms']) ? $queryParams['bathrooms'] : '',
            'direction' => isset($queryParams['direction']) ? $queryParams['direction'] : '',
            'additional_params' => array_filter($queryParams, function($key) {
                return !in_array($key, ['bathrooms', 'direction']);
            }, ARRAY_FILTER_USE_KEY)
        ];

        // Sử dụng UrlHandler để tạo URL mới
        return $this->urlHandler->buildUrl($params);
    }

    // Phương thức xóa tất cả các bộ lọc nâng cao sử dụng UrlHandler
    public function clearAdvancedFilters() {
        // Lấy URL hiện tại và phân tích bằng UrlHandler
        $currentUrl = $_SERVER['REQUEST_URI'];
        error_log('Current URL for clearing filters: ' . $currentUrl);

        // Loại bỏ '/thuenhadanang/' từ đầu path nếu có
        $path = preg_replace('/^\/thuenhadanang\//', '', parse_url($currentUrl, PHP_URL_PATH));

        // Sử dụng UrlHandler để phân tích URL hiện tại
        $parsedParams = $this->urlHandler->parseUrl($path);

        // Giữ lại chỉ các tham số cơ bản (type, ward, price)
        $basicParams = [
            'type' => $parsedParams['type'],
            'ward' => $parsedParams['ward'],
            'price' => $parsedParams['price']
        ];

        // Loại bỏ các tham số rỗng
        $basicParams = array_filter($basicParams, function($value) {
            return !empty($value);
        });

        // Thêm keyword nếu có
        if (isset($_GET['keyword']) && !empty($_GET['keyword'])) {
            $basicParams['additional_params']['keyword'] = $_GET['keyword'];
        }

        // Sử dụng UrlHandler để tạo URL mới
        return $this->urlHandler->buildUrl($basicParams);
    }

    // Phương thức tạo URL thân thiện với SEO sử dụng UrlHandler
    private function buildSeoFriendlyUrl($params) {
        // Sử dụng UrlHandler để tạo URL
        return $this->urlHandler->buildUrl($params);
    }



    // Phương thức tìm kiếm theo các tham số
    public function search($params = []) {
        error_log('search method called with params: ' . json_encode($params));

        // Lấy các tham số tìm kiếm
        $type = isset($params['type']) ? $params['type'] : '';
        $ward = isset($params['ward']) ? $params['ward'] : '';
        $price = isset($params['price']) ? $params['price'] : '';
        $area = isset($params['area']) ? $params['area'] : '';
        $bedrooms = isset($params['bedrooms']) ? $params['bedrooms'] : '';
        $bathrooms = isset($params['bathrooms']) ? $params['bathrooms'] : (isset($_GET['bathrooms']) ? $_GET['bathrooms'] : '');
        $direction = isset($params['direction']) ? $params['direction'] : (isset($_GET['direction']) ? $_GET['direction'] : '');
        $sort = isset($params['sort']) ? $params['sort'] : (isset($_GET['sort']) ? $_GET['sort'] : 'default');
        $keyword = isset($params['keyword']) ? $params['keyword'] : '';

        // Debug các tham số
        error_log('Search parameters:');
        error_log('Type: ' . $type);
        error_log('Bathrooms: ' . $bathrooms);
        error_log('Direction: ' . $direction);

        // Lấy danh sách tất cả loại bất động sản và phường/xã cho form tìm kiếm
        $propertyTypes = $this->propertyTypeModel->getAllPropertyTypes();
        $wards = $this->wardModel->getWardsByStatus(1);

        // Lấy tên loại hình bất động sản cho tiêu đề động
        $propertyTypeName = 'nhà đất';
        if (!empty($type)) {
            $propertyType = $this->propertyTypeModel->getPropertyTypeBySlug($type);
            if ($propertyType) {
                $propertyTypeName = mb_strtolower($propertyType->name, 'UTF-8');
            }
        }

        // Kiểm tra URL hiện tại
        $currentUrl = $_SERVER['REQUEST_URI'];
        // Nếu URL chứa "cho-thue-nha-dat", luôn sử dụng "nhà đất" cho tiêu đề
        if (strpos($currentUrl, 'cho-thue-nha-dat') !== false) {
            $propertyTypeName = 'nhà đất';
            error_log('URL contains cho-thue-nha-dat, forcing propertyTypeName to "nhà đất"');
        }

        // Lấy tên phường/xã cho tiêu đề động
        $wardName = '';
        if (!empty($ward)) {
            $wardObj = $this->wardModel->getWardBySlug($ward);
            if ($wardObj) {
                $wardName = $wardObj->name;
            }
        }

        // Tạo tiêu đề động
        $currentMonth = date('m');
        $currentYear = date('Y');

        // Tạo tiêu đề dựa trên các tham số
        if (!empty($type) && !empty($ward)) {
            $dynamicTitle = "Cho thuê {$propertyTypeName} tại {$wardName} Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        } else if (!empty($type)) {
            $dynamicTitle = "Cho thuê {$propertyTypeName} tại Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        } else if (!empty($ward)) {
            $dynamicTitle = "Cho thuê nhà đất tại {$wardName} Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        } else {
            $dynamicTitle = "Cho thuê nhà đất tại Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
        }

        // Danh sách các hướng nhà
        $directions = [
            'dong' => 'Đông',
            'tay' => 'Tây',
            'nam' => 'Nam',
            'bac' => 'Bắc',
            'dong-bac' => 'Đông Bắc',
            'dong-nam' => 'Đông Nam',
            'tay-bac' => 'Tây Bắc',
            'tay-nam' => 'Tây Nam'
        ];

        // Danh sách các khoảng diện tích
        $areas = [
            '0-20' => 'Dưới 20m²',
            '20-30' => '20 - 30m²',
            '30-50' => '30 - 50m²',
            '50-70' => '50 - 70m²',
            '70-90' => '70 - 90m²',
            '90+' => 'Trên 90m²'
        ];

        // Tìm kiếm bất động sản
        $properties = $this->propertyModel->searchProperties($keyword, $type, $ward, $price, $sort, $bedrooms, $bathrooms, $direction, $area);

        // Chuẩn bị dữ liệu cho view
        $data = [
            'title' => $dynamicTitle . ' - Thuê Nhà Đà Nẵng',
            'view' => 'search',
            'properties' => $properties,
            'propertyTypes' => $propertyTypes,
            'wards' => $wards,
            'directions' => $directions,
            'areas' => $areas,
            'keyword' => $keyword,
            'selectedType' => $type,
            'selectedWard' => $ward,
            'selectedPrice' => $price,
            'selectedSort' => $sort,
            'selectedBedrooms' => $bedrooms,
            'selectedBathrooms' => $bathrooms,
            'selectedDirection' => $direction,
            'selectedArea' => $area,
            'dynamicTitle' => $dynamicTitle,
            'propertyTypeName' => $propertyTypeName
        ];

        // Lưu dữ liệu để sử dụng trong các phương thức khác
        $this->viewData = $data;

        // Hiển thị view
        $this->view('search', $data);
    }
}
