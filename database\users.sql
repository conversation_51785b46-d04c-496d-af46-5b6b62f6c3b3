-- <PERSON><PERSON><PERSON> bảng cũ nếu tồn tại
DROP TABLE IF EXISTS `users`;

-- <PERSON><PERSON><PERSON> bảng users
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fullname` varchar(100) NOT NULL,
  `phone` varchar(15) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `zalo` varchar(15) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `avatar` varchar(255) DEFAULT 'default-avatar.jpg',
  `role` enum('admin','user') NOT NULL DEFAULT 'user',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Th<PERSON><PERSON> tà<PERSON> k<PERSON>n admin
INSERT INTO `users` (`fullname`, `phone`, `email`, `password`, `zalo`, `address`, `avatar`, `role`) VALUES
('Admin User', '0905123456', '<EMAIL>', '$2y$10$PQ0AsDNB0UQE.z2zYEFhkuvczjXRh3F8J8p0uIL0LAE9jqGQnqhD.', '0905123456', 'Quận Hải Châu, Đà Nẵng', 'admin-avatar.jpg', 'admin'); 