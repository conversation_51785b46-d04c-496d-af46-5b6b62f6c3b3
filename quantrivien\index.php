<?php
// Entry point cho phần quản trị admin
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../app/libraries/Database.php';
require_once __DIR__ . '/../app/models/User.php';

// Khởi tạo session
session_start();

// Kiểm tra đã đăng nhập chưa
function isLoggedIn() {
    return isset($_SESSION['admin_id']);
}

// Redirect nếu chưa đăng nhập
if(!isLoggedIn() && basename($_SERVER['PHP_SELF']) != 'login.php') {
    header('Location: login.php');
    exit();
}

// Lấy trang hiện tại
$page = isset($_GET['page']) ? $_GET['page'] : 'dashboard';

// Header
include(__DIR__ . '/templates/header.php');

// Content
switch($page) {
    case 'dashboard':
        include(__DIR__ . '/pages/dashboard.php');
        break;
    case 'users':
        include(__DIR__ . '/pages/users.php');
        break;
    case 'properties':
        include(__DIR__ . '/pages/properties.php');
        break;
    case 'property-types':
        include(__DIR__ . '/pages/property-types.php');
        break;
    case 'wards':
        include(__DIR__ . '/pages/wards.php');
        break;
    case 'packages':
        include(__DIR__ . '/pages/packages.php');
        break;
    case 'user-packages':
        include(__DIR__ . '/pages/user-packages.php');
        break;
    case 'extension-requests':
        include(__DIR__ . '/pages/extension-requests.php');
        break;
    case 'settings':
        include(__DIR__ . '/pages/settings.php');
        break;
    default:
        include(__DIR__ . '/pages/dashboard.php');
}

// Footer
include(__DIR__ . '/templates/footer.php');