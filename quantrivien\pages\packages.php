<?php
// <PERSON><PERSON><PERSON> tra quyền truy cập
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Khởi tạo model
require_once '../app/models/Package.php';
$packageModel = new Package();

// Khởi tạo biến thông báo
$message = '';
$messageType = '';

// Xử lý các action
$action = isset($_GET['action']) ? $_GET['action'] : '';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Khởi tạo biến package cho form edit
$editPackage = null;

// Xử lý thêm package mới
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_package'])) {
    // Validate dữ liệu
    $errors = [];

    if (empty($_POST['name'])) {
        $errors[] = 'Tên gói dịch vụ không được để trống';
    }

    if (empty($_POST['post_limit']) || !is_numeric($_POST['post_limit']) || $_POST['post_limit'] < 0) {
        $errors[] = 'Số lượng tin đăng phải là số nguyên dương';
    }

    if (!isset($_POST['price']) || !is_numeric($_POST['price']) || $_POST['price'] < 0) {
        $errors[] = 'Giá gói phải là số nguyên không âm (có thể bằng 0 cho gói miễn phí)';
    }

    if (empty($_POST['duration_days']) || !is_numeric($_POST['duration_days']) || $_POST['duration_days'] < 1) {
        $errors[] = 'Thời hạn gói phải là số nguyên dương';
    }

    if (empty($errors)) {
        // Xử lý features
        $features = [];
        if (!empty($_POST['features'])) {
            $featuresArray = explode("\n", $_POST['features']);
            foreach ($featuresArray as $feature) {
                $feature = trim($feature);
                if (!empty($feature)) {
                    $features[] = $feature;
                }
            }
        }

        $data = [
            'name' => trim($_POST['name']),
            'description' => trim($_POST['description']),
            'post_limit' => intval($_POST['post_limit']),
            'price' => intval($_POST['price']),
            'duration_days' => intval($_POST['duration_days']),
            'features' => json_encode($features, JSON_UNESCAPED_UNICODE),
            'is_default' => isset($_POST['is_default']) ? 1 : 0,
            'status' => isset($_POST['status']) ? 1 : 0
        ];

        if ($packageModel->create($data)) {
            $message = 'Thêm gói dịch vụ thành công!';
            $messageType = 'success';
        } else {
            $message = 'Có lỗi xảy ra! Không thể thêm gói dịch vụ.';
            $messageType = 'danger';
        }
    } else {
        $message = implode('<br>', $errors);
        $messageType = 'danger';
    }
}

// Xử lý cập nhật package
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_package'])) {
    $errors = [];

    if (empty($_POST['name'])) {
        $errors[] = 'Tên gói dịch vụ không được để trống';
    }

    if (empty($_POST['post_limit']) || !is_numeric($_POST['post_limit']) || $_POST['post_limit'] < 0) {
        $errors[] = 'Số lượng tin đăng phải là số nguyên dương';
    }

    if (!isset($_POST['price']) || !is_numeric($_POST['price']) || $_POST['price'] < 0) {
        $errors[] = 'Giá gói phải là số nguyên không âm (có thể bằng 0 cho gói miễn phí)';
    }

    if (empty($_POST['duration_days']) || !is_numeric($_POST['duration_days']) || $_POST['duration_days'] < 1) {
        $errors[] = 'Thời hạn gói phải là số nguyên dương';
    }

    if (empty($errors)) {
        // Xử lý features
        $features = [];
        if (!empty($_POST['features'])) {
            $featuresArray = explode("\n", $_POST['features']);
            foreach ($featuresArray as $feature) {
                $feature = trim($feature);
                if (!empty($feature)) {
                    $features[] = $feature;
                }
            }
        }

        $data = [
            'id' => intval($_POST['id']),
            'name' => trim($_POST['name']),
            'description' => trim($_POST['description']),
            'post_limit' => intval($_POST['post_limit']),
            'price' => intval($_POST['price']),
            'duration_days' => intval($_POST['duration_days']),
            'features' => json_encode($features, JSON_UNESCAPED_UNICODE),
            'is_default' => isset($_POST['is_default']) ? 1 : 0,
            'status' => isset($_POST['status']) ? 1 : 0
        ];

        if ($packageModel->update($data)) {
            $message = 'Cập nhật gói dịch vụ thành công!';
            $messageType = 'success';
        } else {
            $message = 'Có lỗi xảy ra! Không thể cập nhật gói dịch vụ.';
            $messageType = 'danger';
        }
    } else {
        $message = implode('<br>', $errors);
        $messageType = 'danger';
    }

    // Đặt action về rỗng để hiển thị danh sách
    $action = '';
}

// Xử lý xóa package
if ($action == 'delete' && $id > 0) {
    // Kiểm tra package có đang được sử dụng không
    if ($packageModel->isPackageInUse($id)) {
        $message = 'Không thể xóa gói dịch vụ này vì đang có người dùng sử dụng!';
        $messageType = 'danger';
    } else {
        if ($packageModel->delete($id)) {
            $message = 'Xóa gói dịch vụ thành công!';
            $messageType = 'success';
        } else {
            $message = 'Có lỗi xảy ra! Không thể xóa gói dịch vụ.';
            $messageType = 'danger';
        }
    }

    // Đặt action về rỗng để hiển thị danh sách
    $action = '';
}

// Lấy thông tin package cần chỉnh sửa
if ($action == 'edit' && $id > 0) {
    $editPackage = $packageModel->getPackageById($id);
    if (!$editPackage) {
        $message = 'Không tìm thấy gói dịch vụ!';
        $messageType = 'danger';
    }
}

// Xử lý hiển thị form thêm mới
if ($action == 'add') {
    // Không cần làm gì đặc biệt, chỉ cần hiển thị form
}

// Lấy danh sách packages với thống kê
$packages = $packageModel->getPackageStats();
?>

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mt-2">Quản lý gói dịch vụ</h1>
        <?php if ($action != 'add' && $action != 'edit'): ?>
        <a href="index.php?page=packages&action=add" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>Thêm gói dịch vụ
        </a>
        <?php endif; ?>
    </div>

    <!-- Thông báo -->
    <?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if ($action == 'add' || $action == 'edit'): ?>
    <!-- Form thêm/sửa package -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <?php echo $action == 'add' ? 'Thêm gói dịch vụ mới' : 'Chỉnh sửa gói dịch vụ'; ?>
            </h5>
        </div>
        <div class="card-body">
            <form method="POST">
                <?php if ($action == 'edit'): ?>
                <input type="hidden" name="id" value="<?php echo $editPackage->id; ?>">
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Tên gói dịch vụ <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name"
                                   value="<?php echo $editPackage ? htmlspecialchars($editPackage->name) : ''; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="post_limit" class="form-label">Số lượng tin đăng <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="post_limit" name="post_limit" min="0"
                                   value="<?php echo $editPackage ? $editPackage->post_limit : ''; ?>" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="price" class="form-label">Giá gói (VNĐ) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="price" name="price" min="0"
                                   value="<?php echo $editPackage ? $editPackage->price : ''; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="duration_days" class="form-label">Thời hạn (ngày) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="duration_days" name="duration_days" min="1"
                                   value="<?php echo $editPackage ? $editPackage->duration_days : '30'; ?>" required>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Mô tả gói dịch vụ</label>
                    <textarea class="form-control" id="description" name="description" rows="3"><?php echo $editPackage ? htmlspecialchars($editPackage->description) : ''; ?></textarea>
                </div>

                <div class="mb-3">
                    <label for="features" class="form-label">Tính năng (mỗi dòng một tính năng)</label>
                    <textarea class="form-control" id="features" name="features" rows="5" placeholder="Ví dụ:&#10;Đăng tối đa 5 tin&#10;Hiển thị thông tin liên hệ&#10;Hỗ trợ qua email"><?php
                    if ($editPackage && $editPackage->features) {
                        $features = json_decode($editPackage->features, true);
                        if (is_array($features)) {
                            echo implode("\n", $features);
                        }
                    }
                    ?></textarea>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="is_default" name="is_default" value="1"
                                   <?php echo ($editPackage && $editPackage->is_default) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_default">
                                Gói mặc định cho người dùng mới
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="status" name="status" value="1"
                                   <?php echo (!$editPackage || $editPackage->status) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="status">
                                Kích hoạt gói dịch vụ
                            </label>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" name="<?php echo $action == 'add' ? 'add_package' : 'update_package'; ?>" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i><?php echo $action == 'add' ? 'Thêm gói dịch vụ' : 'Cập nhật'; ?>
                    </button>
                    <a href="index.php?page=packages" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Quay lại
                    </a>
                </div>
            </form>
        </div>
    </div>

    <?php else: ?>
    <!-- Danh sách packages -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Danh sách gói dịch vụ</h5>
        </div>
        <div class="card-body">
            <?php if (empty($packages)): ?>
            <div class="text-center py-4">
                <i class="bi bi-inbox fs-1 text-muted"></i>
                <p class="text-muted mt-2">Chưa có gói dịch vụ nào</p>
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>ID</th>
                            <th>Tên gói</th>
                            <th>Số tin đăng</th>
                            <th>Giá (VNĐ)</th>
                            <th>Thời hạn</th>
                            <th>Người dùng</th>
                            <th>Mặc định</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($packages as $package): ?>
                        <tr>
                            <td><?php echo $package->id; ?></td>
                            <td>
                                <strong><?php echo htmlspecialchars($package->name); ?></strong>
                                <?php if ($package->description): ?>
                                <br><small class="text-muted"><?php echo htmlspecialchars($package->description); ?></small>
                                <?php endif; ?>
                            </td>
                            <td><span class="badge bg-info"><?php echo number_format($package->post_limit); ?> tin</span></td>
                            <td>
                                <?php if ($package->price == 0): ?>
                                <span class="badge bg-success">Miễn phí</span>
                                <?php else: ?>
                                <?php echo number_format($package->price); ?> VNĐ
                                <?php endif; ?>
                            </td>
                            <td><?php echo $package->duration_days; ?> ngày</td>
                            <td>
                                <span class="badge bg-primary"><?php echo $package->user_count; ?> tổng</span>
                                <span class="badge bg-success"><?php echo $package->active_users; ?> đang dùng</span>
                            </td>
                            <td>
                                <?php if ($package->is_default): ?>
                                <span class="badge bg-warning">Mặc định</span>
                                <?php else: ?>
                                <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($package->status): ?>
                                <span class="badge bg-success">Hoạt động</span>
                                <?php else: ?>
                                <span class="badge bg-secondary">Tạm dừng</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="index.php?page=packages&action=edit&id=<?php echo $package->id; ?>"
                                       class="btn btn-outline-primary" title="Chỉnh sửa">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <?php if (!$package->is_default && $package->user_count == 0): ?>
                                    <a href="index.php?page=packages&action=delete&id=<?php echo $package->id; ?>"
                                       class="btn btn-outline-danger" title="Xóa"
                                       onclick="return confirm('Bạn có chắc chắn muốn xóa gói dịch vụ này?')">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>
