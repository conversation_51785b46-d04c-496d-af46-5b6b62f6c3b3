// Document Ready
$(document).ready(function() {
    // Khởi tạo Select2
    initializeSelect2();

    // Khởi tạo các tooltip
    initializeTooltips();

    // Khởi tạo các sự kiện form
    initializeFormEvents();

    // Thêm initialization cho placeholder animation
    initializePlaceholderAnimation();

    // Xử lý favorite property
    $('.property-favorite').click(function(e) {
        e.preventDefault();
        $(this).toggleClass('active');
        const icon = $(this).find('i');
        if ($(this).hasClass('active')) {
            icon.removeClass('bi-heart').addClass('bi-heart-fill');
        } else {
            icon.removeClass('bi-heart-fill').addClass('bi-heart');
        }
        // TODO: Thêm logic lưu/xóa tin yêu thích vào database
    });
});

// Khởi tạo Select2
function initializeSelect2() {
    $('.select2-ward').select2({
        placeholder: "Chọn phường/xã",
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "Không tìm thấy kết quả";
            },
            searching: function() {
                return "Đang tìm kiếm...";
            }
        }
    });
}

// Khởi tạo Tooltips
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Khởi tạo Form Events
function initializeFormEvents() {
    // Không cần ngăn chặn form submit nữa vì đã có trang search
    // Chỉ xử lý reset form
    $('.btn-reset').on('click', function() {
        $('.search-form')[0].reset();
        $('.select2-ward').val(null).trigger('change');
    });
}

// Placeholder Animation
function initializePlaceholderAnimation() {
    const placeholders = [
        "Nhập tên đường...",
        "Nguyễn Văn Linh...",
        "2/9...",
        "Trần Phú...",
        "Lê Duẩn...",
        "Đống Đa...",
        "Nguyễn Chí Thanh...",
        "Hùng Vương..."
    ];

    let currentIndex = 0;
    const $input = $('.search-address');
    const originalPlaceholder = $input.attr('placeholder');
    let currentText = '';
    let isDeleting = false;
    let typingSpeed = 100; // Tốc độ gõ chữ
    let deletingSpeed = 50; // Tốc độ xóa chữ
    let newTextDelay = 1000; // Delay trước khi gõ text mới

    function type() {
        const fullText = placeholders[currentIndex];

        if (isDeleting) {
            currentText = fullText.substring(0, currentText.length - 1);
        } else {
            currentText = fullText.substring(0, currentText.length + 1);
        }

        $input.attr('placeholder', currentText);

        if (!isDeleting && currentText === fullText) {
            isDeleting = true;
            setTimeout(type, newTextDelay);
        } else if (isDeleting && currentText === '') {
            isDeleting = false;
            currentIndex = (currentIndex + 1) % placeholders.length;
            setTimeout(type, typingSpeed);
        } else {
            setTimeout(type, isDeleting ? deletingSpeed : typingSpeed);
        }
    }

    // Bắt đầu animation
    if (!$input.is(':focus') && !$input.val()) {
        setTimeout(type, newTextDelay);
    }

    // Xử lý sự kiện focus
    $input.on('focus', function() {
        $(this).attr('placeholder', '');
    });

    // Xử lý sự kiện blur
    $input.on('blur', function() {
        if (!$(this).val()) {
            $(this).attr('placeholder', originalPlaceholder);
        }
    });
}

// Initialize Swiper and Gallery Navigation
if (document.querySelector('.gallery-thumbs')) {
    // Khởi tạo Swiper cho thumbnails trước
    const galleryThumbs = new Swiper('.gallery-thumbs', {
        spaceBetween: 10,
        slidesPerView: 'auto',
        freeMode: true,
        watchSlidesProgress: true,
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        }
    });

    // Khởi tạo Swiper cho ảnh chính
    const galleryMain = new Swiper('.gallery-main', {
        spaceBetween: 0,
        navigation: {
            nextEl: '.gallery-next',
            prevEl: '.gallery-prev',
        },
        thumbs: {
            swiper: galleryThumbs
        }
    });
}

// Initialize Fancybox
if (document.querySelector('[data-fancybox="gallery"]')) {
    Fancybox.bind('[data-fancybox="gallery"]', {
        // Custom options
        Toolbar: {
            display: {
                left: ["infobar"],
                middle: [
                    "zoomIn",
                    "zoomOut",
                    "toggle1to1",
                    "rotateCCW",
                    "rotateCW",
                    "flipX",
                    "flipY",
                ],
                right: ["slideshow", "thumbs", "close"],
            },
        },
    });
}