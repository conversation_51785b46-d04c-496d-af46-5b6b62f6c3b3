<?php
// Load configuration
require_once 'config.php';

// Load BaseController first
require_once APP_PATH . '/controllers/BaseController.php';

// Load routes
require_once APP_PATH . '/routes.php';

// Load Database class for URL parsing
require_once APP_PATH . '/libraries/Database.php';

// Load UrlHandler for centralized URL processing
require_once APP_PATH . '/libraries/UrlHandler.php';

// Autoload classes
spl_autoload_register(function ($class) {
    $file = APP_PATH . '/' . str_replace('\\', '/', $class) . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// Parse URL
$url = isset($_GET['url']) ? rtrim($_GET['url'], '/') : '';

// Check if URL matches the pattern {slug}-{id}
if (preg_match('/^(.+)-(\d+)$/', $url, $matches)) {
    $slug = $matches[1];
    $id = $matches[2];

    // Load PropertyController for this pattern
    $controllerFile = APP_PATH . '/controllers/PropertyController.php';
    if (file_exists($controllerFile)) {
        require_once $controllerFile;
        $controller = new PropertyController();
        $controller->detail($slug, $id);
        exit;
    }
}

// Initialize UrlHandler for SEO-friendly URL processing
$urlHandler = new UrlHandler();
$parsedParams = $urlHandler->parseUrl($url);

// Check if URL was matched by UrlHandler
if ($parsedParams['matched']) {
    error_log('URL matched by UrlHandler: ' . json_encode($parsedParams));

    $controllerName = $parsedParams['controller'];
    $action = $parsedParams['action'];

    // Load the appropriate controller
    $controllerFile = APP_PATH . '/controllers/' . $controllerName . '.php';
    if (file_exists($controllerFile)) {
        require_once $controllerFile;
        $controller = new $controllerName();

        // Call the appropriate action based on the parsed parameters
        if ($action === 'filterByTypeAndWard') {
            $typeSlug = $parsedParams['type'];
            $wardSlug = $parsedParams['ward'];

            // Prepare additional parameters
            $additionalParams = [
                'price' => $parsedParams['price'],
                'area' => $parsedParams['area'],
                'bedrooms' => $parsedParams['bedrooms'],
                'bathrooms' => $parsedParams['bathrooms'],
                'direction' => $parsedParams['direction']
            ];

            // Add query parameters
            if (isset($parsedParams['additional_params'])) {
                foreach ($parsedParams['additional_params'] as $key => $value) {
                    $_GET[$key] = $value;
                }
            }

            $controller->filterByTypeAndWard($typeSlug, $wardSlug, $additionalParams);
            exit;
        }
    }
}

// Handle legacy URL patterns that are not covered by UrlHandler
if (preg_match('/^(thue|cho-thue)/', $url) && !$parsedParams['matched']) {
    error_log('Processing legacy URL pattern: ' . $url);

    // Simple legacy patterns that set $_GET parameters for SearchController
    if (preg_match('/^thue-([a-z0-9-]+)$/', $url, $matches)) {
        $_GET['type'] = $matches[1];
    }
    else if (preg_match('/^thue-nha-dat-gia-(tu|tren)-([0-9]+)-?([0-9]+)?-trieu$/', $url, $matches)) {
        if ($matches[1] == 'tu' && isset($matches[3]) && !empty($matches[3])) {
            $_GET['price'] = $matches[2] . '-' . $matches[3];
        } else {
            $_GET['price'] = $matches[2] . '+';
        }
    }


    // Redirect to SearchController for processing
    $controllerFile = APP_PATH . '/controllers/SearchController.php';
    if (file_exists($controllerFile)) {
        require_once $controllerFile;
        $controller = new SearchController();
        $controller->index();
        exit;
    }




























    // Fallback: Redirect to SearchController for any unmatched legacy patterns
    $controllerFile = APP_PATH . '/controllers/SearchController.php';
    if (file_exists($controllerFile)) {
        require_once $controllerFile;
        $controller = new SearchController();
        $controller->index();
        exit;
    }
}

// Handle dynamic routes with parameters (e.g., property/edit/{id})
$matched = false;

// Debug information (disabled for production)
// error_log('Current URL: ' . $url);
// error_log('Available routes: ' . json_encode(array_keys($routes)));

foreach ($routes as $pattern => $handler) {
    // Check if the pattern contains a parameter placeholder like {id}
    if (strpos($pattern, '{') !== false) {
        // Convert the route pattern to a regex pattern
        $regexPattern = preg_replace('/{([a-zA-Z0-9_]+)}/', '([^/]+)', $pattern);
        $regexPattern = '#^' . $regexPattern . '$#';

        // Debug information (disabled for production)
        // error_log('Checking pattern: ' . $pattern . ' (regex: ' . $regexPattern . ') against URL: ' . $url);

        // Check if the URL matches the pattern
        if (preg_match($regexPattern, $url, $matches)) {
            // Debug information (disabled for production)
            // error_log('Match found for pattern: ' . $pattern . ' with URL: ' . $url);
            // error_log('Matches: ' . json_encode($matches));

            // Extract the parameter values
            array_shift($matches); // Remove the full match

            // Get the controller and action
            list($controllerName, $action) = $handler;
            $controllerFile = APP_PATH . '/controllers/' . $controllerName . '.php';

            // error_log('Controller: ' . $controllerName . ', Action: ' . $action);
            // error_log('Controller file: ' . $controllerFile);

            if (file_exists($controllerFile)) {
                require_once $controllerFile;
                $controllerInstance = new $controllerName();

                if (method_exists($controllerInstance, $action)) {
                    // Call the controller method with the extracted parameters
                    // error_log('Calling ' . $controllerName . '->' . $action . '(' . implode(', ', $matches) . ')');
                    call_user_func_array([$controllerInstance, $action], $matches);
                    $matched = true;
                    exit;
                } else {
                    // error_log('Action not found: ' . $action . ' in controller: ' . $controllerName);
                    throw new Exception("Action not found: {$action}");
                }
            } else {
                // error_log('Controller file not found: ' . $controllerFile);
                throw new Exception("Controller not found: {$controllerName}");
            }
        }
    }
}

// Handle regular routes
if (!$matched && isset($routes[$url])) {
    // error_log('Handling regular route: ' . $url);
    list($controllerName, $action) = $routes[$url];
    $controllerFile = APP_PATH . '/controllers/' . $controllerName . '.php';

    // error_log('Controller: ' . $controllerName . ', Action: ' . $action);
    // error_log('Controller file: ' . $controllerFile);

    if (file_exists($controllerFile)) {
        require_once $controllerFile;
        $controllerInstance = new $controllerName();
        if (method_exists($controllerInstance, $action)) {
            // error_log('Calling ' . $controllerName . '->' . $action . '()');
            $controllerInstance->$action();
        } else {
            // error_log('Action not found: ' . $action . ' in controller: ' . $controllerName);
            throw new Exception("Action not found: {$action}");
        }
    } else {
        // error_log('Controller file not found: ' . $controllerFile);
        throw new Exception("Controller not found: {$controllerName}");
    }
} else if (!$matched) {
    // Default to home if route not found
    // error_log('No matching route found for URL: ' . $url . ', redirecting to home');
    header('Location: /thuenhadanang');
    exit;
}
