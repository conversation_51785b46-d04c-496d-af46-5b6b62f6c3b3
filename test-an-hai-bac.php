<?php
// Load configuration
require_once 'config.php';

// Load database library
require_once APP_PATH . '/libraries/Database.php';

// Load Ward model
require_once APP_PATH . '/models/Ward.php';
require_once APP_PATH . '/models/Property.php';

echo '<h1>An Hai Bac Ward Test</h1>';

// Create database instance
$db = new Database();

// Test 1: Check if the ward exists in the database
echo '<h2>Test 1: Check if "An Hai Bac" ward exists</h2>';
$db->query('SELECT * FROM wards WHERE slug LIKE :slug');
$db->bind(':slug', '%an-hai-bac%');
$ward = $db->single();

if ($ward) {
    echo '<p>Ward found:</p>';
    echo '<ul>';
    echo '<li>ID: ' . $ward->id . '</li>';
    echo '<li>Name: ' . htmlspecialchars($ward->name) . '</li>';
    echo '<li>Slug: ' . htmlspecialchars($ward->slug) . '</li>';
    echo '</ul>';
    
    $wardId = $ward->id;
} else {
    echo '<p>Ward not found with slug containing "an-hai-bac"</p>';
    
    // Check for similar wards
    $db->query('SELECT * FROM wards WHERE slug LIKE :pattern');
    $db->bind(':pattern', '%an-hai%');
    $similarWards = $db->resultSet();
    
    if (!empty($similarWards)) {
        echo '<p>Similar wards found:</p>';
        echo '<ul>';
        foreach ($similarWards as $w) {
            echo '<li>ID: ' . $w->id . ', Name: ' . htmlspecialchars($w->name) . ', Slug: ' . htmlspecialchars($w->slug) . '</li>';
        }
        echo '</ul>';
        
        // Use the first similar ward for further tests
        $wardId = $similarWards[0]->id;
    } else {
        echo '<p>No similar wards found</p>';
        $wardId = 0;
    }
}

// Test 2: Check if there are properties with this ward
if ($wardId > 0) {
    echo '<h2>Test 2: Check for properties in this ward</h2>';
    $db->query('SELECT COUNT(*) as count FROM properties WHERE ward_id = :ward_id');
    $db->bind(':ward_id', $wardId);
    $result = $db->single();
    
    echo '<p>Number of properties in this ward: ' . $result->count . '</p>';
    
    if ($result->count > 0) {
        // List the properties
        $db->query('SELECT p.*, pt.name as type_name FROM properties p 
                    JOIN property_types pt ON p.type_id = pt.id
                    WHERE p.ward_id = :ward_id');
        $db->bind(':ward_id', $wardId);
        $properties = $db->resultSet();
        
        echo '<h3>Properties in this ward:</h3>';
        echo '<table border="1">';
        echo '<tr><th>ID</th><th>Title</th><th>Type</th><th>Status</th></tr>';
        
        foreach ($properties as $property) {
            echo '<tr>';
            echo '<td>' . $property->id . '</td>';
            echo '<td>' . htmlspecialchars($property->title) . '</td>';
            echo '<td>' . htmlspecialchars($property->type_name) . '</td>';
            echo '<td>' . htmlspecialchars($property->status) . '</td>';
            echo '</tr>';
        }
        
        echo '</table>';
    }
}

// Test 3: Simulate the search process
echo '<h2>Test 3: Simulate search process</h2>';
$testSlug = 'an-hai-bac';
echo '<p>Testing search with ward slug: "' . $testSlug . '"</p>';

// Create Property model instance
$propertyModel = new Property();

// Call the searchProperties method with only the ward parameter
$properties = $propertyModel->searchProperties('', '', $testSlug, '', 'default', '', '', '', '');

echo '<p>Number of properties found by search: ' . count($properties) . '</p>';

if (count($properties) > 0) {
    echo '<h3>Properties found by search:</h3>';
    echo '<table border="1">';
    echo '<tr><th>ID</th><th>Title</th><th>Type</th><th>Ward</th><th>Status</th></tr>';
    
    foreach ($properties as $property) {
        echo '<tr>';
        echo '<td>' . $property->id . '</td>';
        echo '<td>' . htmlspecialchars($property->title) . '</td>';
        echo '<td>' . htmlspecialchars($property->type_name) . '</td>';
        echo '<td>' . htmlspecialchars($property->ward_name) . '</td>';
        echo '<td>' . htmlspecialchars($property->status) . '</td>';
        echo '</tr>';
    }
    
    echo '</table>';
} else {
    echo '<p>No properties found by search</p>';
}

// Test 4: Check URL processing
echo '<h2>Test 4: Check URL processing</h2>';
echo '<p>Simulating URL: /thuenhadanang/thue-nha-dat-an-hai-bac</p>';

// Simulate the URL processing in index.php
$url = 'thue-nha-dat-an-hai-bac';
if (preg_match('/^thue-nha-dat-([a-z0-9-]+)$/', $url, $matches)) {
    $extractedWard = $matches[1];
    echo '<p>Extracted ward from URL: "' . $extractedWard . '"</p>';
    
    // Check if this ward exists
    $db->query('SELECT * FROM wards WHERE LOWER(slug) = LOWER(:slug)');
    $db->bind(':slug', $extractedWard);
    $wardResult = $db->single();
    
    if ($wardResult) {
        echo '<p>Ward found in database:</p>';
        echo '<ul>';
        echo '<li>ID: ' . $wardResult->id . '</li>';
        echo '<li>Name: ' . htmlspecialchars($wardResult->name) . '</li>';
        echo '<li>Slug: ' . htmlspecialchars($wardResult->slug) . '</li>';
        echo '</ul>';
    } else {
        echo '<p>Ward not found in database with slug: "' . $extractedWard . '"</p>';
    }
} else {
    echo '<p>URL pattern does not match</p>';
}
