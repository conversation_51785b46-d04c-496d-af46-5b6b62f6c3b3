<?php
// <PERSON><PERSON><PERSON> tra quyền truy cập
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Khởi tạo model
require_once '../app/models/UserPackage.php';
require_once '../app/models/Package.php';
require_once '../app/models/User.php';

$userPackageModel = new UserPackage();
$packageModel = new Package();
$userModel = new User();

// Khởi tạo biến thông báo
$message = '';
$messageType = '';

// Xử lý các action
$action = isset($_GET['action']) ? $_GET['action'] : '';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Xử lý cập nhật trạng thái
if ($action == 'update_status' && $id > 0 && isset($_GET['status'])) {
    $status = $_GET['status'];
    $allowedStatuses = ['active', 'expired', 'cancelled'];

    if (in_array($status, $allowedStatuses)) {
        if ($userPackageModel->updateStatus($id, $status)) {
            $message = 'Cập nhật trạng thái thành công!';
            $messageType = 'success';
        } else {
            $message = 'Có lỗi xảy ra! Không thể cập nhật trạng thái.';
            $messageType = 'danger';
        }
    } else {
        $message = 'Trạng thái không hợp lệ!';
        $messageType = 'danger';
    }

    // Đặt action về rỗng để hiển thị danh sách
    $action = '';
}

// Xử lý xóa user package
if ($action == 'delete' && $id > 0) {
    if ($userPackageModel->delete($id)) {
        $message = 'Xóa gói dịch vụ người dùng thành công!';
        $messageType = 'success';
    } else {
        $message = 'Có lỗi xảy ra! Không thể xóa gói dịch vụ người dùng.';
        $messageType = 'danger';
    }

    // Đặt action về rỗng để hiển thị danh sách
    $action = '';
}

// Xử lý thêm gói cho người dùng
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['assign_package'])) {
    $errors = [];

    if (empty($_POST['user_id']) || !is_numeric($_POST['user_id'])) {
        $errors[] = 'Vui lòng chọn người dùng';
    }

    if (empty($_POST['package_id']) || !is_numeric($_POST['package_id'])) {
        $errors[] = 'Vui lòng chọn gói dịch vụ';
    }

    if (empty($errors)) {
        $userId = intval($_POST['user_id']);
        $packageId = intval($_POST['package_id']);

        // Lấy thông tin package
        $package = $packageModel->getPackageById($packageId);
        if ($package) {
            // Tính ngày kết thúc
            $startDate = date('Y-m-d H:i:s');

            // Nếu là gói miễn phí (price = 0), không có thời hạn
            if ($package->price == 0) {
                $endDate = '2099-12-31 23:59:59'; // Ngày rất xa trong tương lai (vĩnh viễn)
            } else {
                $endDate = date('Y-m-d H:i:s', strtotime('+' . $package->duration_days . ' days'));
            }

            $data = [
                'user_id' => $userId,
                'package_id' => $packageId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'posts_used' => 0,
                'posts_remaining' => $package->post_limit,
                'status' => 'active'
            ];

            if ($userPackageModel->create($data)) {
                $message = 'Gán gói dịch vụ cho người dùng thành công!';
                $messageType = 'success';
            } else {
                $message = 'Có lỗi xảy ra! Không thể gán gói dịch vụ.';
                $messageType = 'danger';
            }
        } else {
            $message = 'Gói dịch vụ không tồn tại!';
            $messageType = 'danger';
        }
    } else {
        $message = implode('<br>', $errors);
        $messageType = 'danger';
    }

    // Đặt action về rỗng để hiển thị danh sách
    $action = '';
}

// Cập nhật các gói hết hạn
$userPackageModel->updateExpiredPackages();

// Lấy danh sách user packages
$userPackages = $userPackageModel->getAllUserPackages();

// Lấy danh sách packages và users cho form assign
$packages = $packageModel->getActivePackages();
$users = $userModel->getAllUsers();

// Lấy thống kê
$stats = $userPackageModel->getUserPackageStats();
?>

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mt-2">Quản lý gói dịch vụ người dùng</h1>
        <?php if ($action != 'assign'): ?>
        <a href="index.php?page=user-packages&action=assign" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>Gán gói cho người dùng
        </a>
        <?php endif; ?>
    </div>

    <!-- Thông báo -->
    <?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- Thống kê -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Tổng gói</h6>
                            <h3><?php echo number_format($stats->total_packages); ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-box-seam fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Đang hoạt động</h6>
                            <h3><?php echo number_format($stats->active_packages); ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Hết hạn</h6>
                            <h3><?php echo number_format($stats->expired_packages); ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-clock fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Đã hủy</h6>
                            <h3><?php echo number_format($stats->cancelled_packages); ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-x-circle fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($action == 'assign'): ?>
    <!-- Form gán gói cho người dùng -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Gán gói dịch vụ cho người dùng</h5>
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="user_id" class="form-label">Người dùng <span class="text-danger">*</span></label>
                            <select class="form-select" id="user_id" name="user_id" required>
                                <option value="">Chọn người dùng</option>
                                <?php foreach ($users as $user): ?>
                                <?php if ($user->role == 'user'): ?>
                                <option value="<?php echo $user->id; ?>">
                                    <?php echo htmlspecialchars($user->fullname . ' (' . $user->email . ')'); ?>
                                </option>
                                <?php endif; ?>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="package_id" class="form-label">Gói dịch vụ <span class="text-danger">*</span></label>
                            <select class="form-select" id="package_id" name="package_id" required>
                                <option value="">Chọn gói dịch vụ</option>
                                <?php foreach ($packages as $package): ?>
                                <option value="<?php echo $package->id; ?>">
                                    <?php echo htmlspecialchars($package->name . ' (' . $package->post_limit . ' tin, ' . number_format($package->price) . ' VNĐ)'); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" name="assign_package" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>Gán gói dịch vụ
                    </button>
                    <a href="index.php?page=user-packages" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Quay lại
                    </a>
                </div>
            </form>
        </div>
    </div>

    <?php else: ?>
    <!-- Danh sách user packages -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Danh sách gói dịch vụ người dùng</h5>
        </div>
        <div class="card-body">
            <?php if (empty($userPackages)): ?>
            <div class="text-center py-4">
                <i class="bi bi-inbox fs-1 text-muted"></i>
                <p class="text-muted mt-2">Chưa có gói dịch vụ nào được gán</p>
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>ID</th>
                            <th>Người dùng</th>
                            <th>Gói dịch vụ</th>
                            <th>Tin đã dùng/Tổng</th>
                            <th>Ngày bắt đầu</th>
                            <th>Ngày kết thúc</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($userPackages as $userPackage): ?>
                        <tr>
                            <td><?php echo $userPackage->id; ?></td>
                            <td>
                                <strong><?php echo htmlspecialchars($userPackage->fullname); ?></strong>
                                <br><small class="text-muted"><?php echo htmlspecialchars($userPackage->email); ?></small>
                            </td>
                            <td>
                                <strong><?php echo htmlspecialchars($userPackage->package_name); ?></strong>
                                <br><span class="badge bg-info"><?php echo $userPackage->post_limit; ?> tin</span>
                            </td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <?php
                                    $percentage = $userPackage->post_limit > 0 ? ($userPackage->posts_used / $userPackage->post_limit) * 100 : 0;
                                    $progressClass = $percentage >= 80 ? 'bg-danger' : ($percentage >= 60 ? 'bg-warning' : 'bg-success');
                                    ?>
                                    <div class="progress-bar <?php echo $progressClass; ?>" style="width: <?php echo $percentage; ?>%">
                                        <?php echo $userPackage->posts_used; ?>/<?php echo $userPackage->post_limit; ?>
                                    </div>
                                </div>
                                <small class="text-muted">Còn lại: <?php echo $userPackage->posts_remaining; ?> tin</small>
                            </td>
                            <td><?php echo date('d/m/Y H:i', strtotime($userPackage->start_date)); ?></td>
                            <td>
                                <?php
                                // Kiểm tra xem có phải gói miễn phí không
                                $packageModel = new Package();
                                $package = $packageModel->getPackageById($userPackage->package_id);
                                $isFreePackage = $package && $package->price == 0;

                                if ($isFreePackage): ?>
                                    <span class="badge bg-success">Vĩnh viễn</span>
                                <?php else: ?>
                                    <?php echo date('d/m/Y H:i', strtotime($userPackage->end_date)); ?>
                                    <?php if (strtotime($userPackage->end_date) < time()): ?>
                                    <br><small class="text-danger">Đã hết hạn</small>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                switch ($userPackage->status) {
                                    case 'active':
                                        echo '<span class="badge bg-success">Hoạt động</span>';
                                        break;
                                    case 'expired':
                                        echo '<span class="badge bg-warning">Hết hạn</span>';
                                        break;
                                    case 'cancelled':
                                        echo '<span class="badge bg-danger">Đã hủy</span>';
                                        break;
                                }
                                ?>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        Thao tác
                                    </button>
                                    <ul class="dropdown-menu">
                                        <?php if ($userPackage->status == 'active'): ?>
                                        <li><a class="dropdown-item" href="index.php?page=user-packages&action=update_status&id=<?php echo $userPackage->id; ?>&status=expired">Đánh dấu hết hạn</a></li>
                                        <li><a class="dropdown-item" href="index.php?page=user-packages&action=update_status&id=<?php echo $userPackage->id; ?>&status=cancelled">Hủy gói</a></li>
                                        <?php elseif ($userPackage->status == 'expired'): ?>
                                        <li><a class="dropdown-item" href="index.php?page=user-packages&action=update_status&id=<?php echo $userPackage->id; ?>&status=active">Kích hoạt lại</a></li>
                                        <?php elseif ($userPackage->status == 'cancelled'): ?>
                                        <li><a class="dropdown-item" href="index.php?page=user-packages&action=update_status&id=<?php echo $userPackage->id; ?>&status=active">Kích hoạt lại</a></li>
                                        <?php endif; ?>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="index.php?page=user-packages&action=delete&id=<?php echo $userPackage->id; ?>" onclick="return confirm('Bạn có chắc chắn muốn xóa?')">Xóa</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>
