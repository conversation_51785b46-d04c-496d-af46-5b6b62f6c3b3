<?php
require_once __DIR__ . '/../libraries/Database.php';

class UserPackage {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    // L<PERSON><PERSON> t<PERSON>t cả user packages
    public function getAllUserPackages() {
        $this->db->query('SELECT up.*, u.fullname, u.email, p.name as package_name, p.post_limit
                         FROM user_packages up
                         LEFT JOIN users u ON up.user_id = u.id
                         LEFT JOIN packages p ON up.package_id = p.id
                         ORDER BY up.created_at DESC');
        return $this->db->resultSet();
    }

    // Lấy user package theo user ID
    public function getUserPackageByUserId($userId) {
        $this->db->query('SELECT up.*, p.name as package_name, p.post_limit, p.features, p.price
                         FROM user_packages up
                         LEFT JOIN packages p ON up.package_id = p.id
                         WHERE up.user_id = :user_id AND up.status = "active"
                         ORDER BY up.created_at DESC LIMIT 1');
        $this->db->bind(':user_id', $userId);
        return $this->db->single();
    }

    // Lấy user package theo ID
    public function getUserPackageById($id) {
        $this->db->query('SELECT up.*, u.fullname, u.email, p.name as package_name, p.post_limit
                         FROM user_packages up
                         LEFT JOIN users u ON up.user_id = u.id
                         LEFT JOIN packages p ON up.package_id = p.id
                         WHERE up.id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    // Tạo user package mới
    public function create($data) {
        $this->db->query('INSERT INTO user_packages (user_id, package_id, start_date, end_date, posts_used, posts_remaining, status)
                         VALUES (:user_id, :package_id, :start_date, :end_date, :posts_used, :posts_remaining, :status)');

        $this->db->bind(':user_id', $data['user_id']);
        $this->db->bind(':package_id', $data['package_id']);
        $this->db->bind(':start_date', $data['start_date']);
        $this->db->bind(':end_date', $data['end_date']);
        $this->db->bind(':posts_used', $data['posts_used'] ?? 0);
        $this->db->bind(':posts_remaining', $data['posts_remaining']);
        $this->db->bind(':status', $data['status'] ?? 'active');

        return $this->db->execute();
    }

    // Gán gói mặc định cho user mới
    public function assignDefaultPackageToUser($userId) {
        // Lấy gói mặc định
        require_once __DIR__ . '/Package.php';
        $packageModel = new Package();
        $defaultPackage = $packageModel->getDefaultPackage();

        if (!$defaultPackage) {
            return false;
        }

        // Tính ngày kết thúc
        $startDate = date('Y-m-d H:i:s');

        // Nếu là gói miễn phí (price = 0), không có thời hạn
        if ($defaultPackage->price == 0) {
            $endDate = '2099-12-31 23:59:59'; // Ngày rất xa trong tương lai (vĩnh viễn)
        } else {
            $endDate = date('Y-m-d H:i:s', strtotime('+' . $defaultPackage->duration_days . ' days'));
        }

        $data = [
            'user_id' => $userId,
            'package_id' => $defaultPackage->id,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'posts_used' => 0,
            'posts_remaining' => $defaultPackage->post_limit,
            'status' => 'active'
        ];

        return $this->create($data);
    }

    // Cập nhật số lượng tin đã sử dụng
    public function updatePostsUsed($userPackageId, $increment = 1) {
        $this->db->query('UPDATE user_packages SET
                         posts_used = posts_used + :increment,
                         posts_remaining = posts_remaining - :increment_2
                         WHERE id = :id AND posts_remaining > 0');
        $this->db->bind(':increment', $increment);
        $this->db->bind(':increment_2', $increment);
        $this->db->bind(':id', $userPackageId);
        return $this->db->execute();
    }

    // Kiểm tra user có thể đăng tin không
    public function canUserPost($userId) {
        $userPackage = $this->getUserPackageByUserId($userId);

        if (!$userPackage) {
            return false;
        }

        // Kiểm tra trạng thái gói
        if ($userPackage->status !== 'active') {
            return false;
        }

        // Lấy thông tin package để kiểm tra giá
        require_once __DIR__ . '/Package.php';
        $packageModel = new Package();
        $package = $packageModel->getPackageById($userPackage->package_id);

        // Nếu không phải gói miễn phí, kiểm tra thời hạn
        if ($package && $package->price > 0 && strtotime($userPackage->end_date) < time()) {
            return false;
        }

        // Kiểm tra còn tin để đăng
        return $userPackage->posts_remaining > 0;
    }

    // Lấy số tin còn lại của user
    public function getUserPostsRemaining($userId) {
        $userPackage = $this->getUserPackageByUserId($userId);
        return $userPackage ? $userPackage->posts_remaining : 0;
    }

    // Cập nhật trạng thái user package
    public function updateStatus($id, $status) {
        $this->db->query('UPDATE user_packages SET status = :status WHERE id = :id');
        $this->db->bind(':status', $status);
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }

    // Xóa user package
    public function delete($id) {
        $this->db->query('DELETE FROM user_packages WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }

    // Cập nhật gói hết hạn (chỉ áp dụng cho gói trả phí)
    public function updateExpiredPackages() {
        $this->db->query('UPDATE user_packages up
                         INNER JOIN packages p ON up.package_id = p.id
                         SET up.status = "expired"
                         WHERE up.status = "active"
                         AND up.end_date < NOW()
                         AND p.price > 0');
        return $this->db->execute();
    }

    // Lấy thống kê user packages
    public function getUserPackageStats() {
        $this->db->query('SELECT
                         COUNT(*) as total_packages,
                         COUNT(CASE WHEN status = "active" THEN 1 END) as active_packages,
                         COUNT(CASE WHEN status = "expired" THEN 1 END) as expired_packages,
                         COUNT(CASE WHEN status = "cancelled" THEN 1 END) as cancelled_packages
                         FROM user_packages');
        return $this->db->single();
    }
}
