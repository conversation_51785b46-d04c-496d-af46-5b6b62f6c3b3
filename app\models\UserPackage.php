<?php
require_once __DIR__ . '/../libraries/Database.php';

class UserPackage {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    // L<PERSON>y tất cả user packages
    public function getAllUserPackages() {
        $this->db->query('SELECT up.*, u.name as user_name, u.email as user_email,
                         p.name as package_name, p.post_limit as package_post_limit, p.price as package_price
                         FROM user_packages up
                         LEFT JOIN users u ON up.user_id = u.id
                         LEFT JOIN packages p ON up.package_id = p.id
                         ORDER BY up.created_at DESC');
        return $this->db->resultSet();
    }

    // Lấy user package theo user ID
    public function getUserPackageByUserId($userId) {
        $this->db->query('SELECT up.*, p.name as package_name, p.post_limit, p.features, p.price
                         FROM user_packages up
                         LEFT JOIN packages p ON up.package_id = p.id
                         WHERE up.user_id = :user_id AND up.status = "active"
                         ORDER BY up.created_at DESC LIMIT 1');
        $this->db->bind(':user_id', $userId);
        return $this->db->single();
    }

    // Lấy user package theo ID
    public function getUserPackageById($id) {
        $this->db->query('SELECT up.*, u.fullname, u.email, p.name as package_name, p.post_limit
                         FROM user_packages up
                         LEFT JOIN users u ON up.user_id = u.id
                         LEFT JOIN packages p ON up.package_id = p.id
                         WHERE up.id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    // Tạo user package mới
    public function create($data) {
        $this->db->query('INSERT INTO user_packages (user_id, package_id, start_date, end_date, posts_used, posts_remaining, status)
                         VALUES (:user_id, :package_id, :start_date, :end_date, :posts_used, :posts_remaining, :status)');

        $this->db->bind(':user_id', $data['user_id']);
        $this->db->bind(':package_id', $data['package_id']);
        $this->db->bind(':start_date', $data['start_date']);
        $this->db->bind(':end_date', $data['end_date']);
        $this->db->bind(':posts_used', $data['posts_used'] ?? 0);
        $this->db->bind(':posts_remaining', $data['posts_remaining']);
        $this->db->bind(':status', $data['status'] ?? 'active');

        return $this->db->execute();
    }

    // Gán gói mặc định cho user mới
    public function assignDefaultPackageToUser($userId) {
        // Lấy gói mặc định
        require_once __DIR__ . '/Package.php';
        $packageModel = new Package();
        $defaultPackage = $packageModel->getDefaultPackage();

        if (!$defaultPackage) {
            return false;
        }

        // Tính ngày kết thúc
        $startDate = date('Y-m-d H:i:s');

        // Nếu là gói miễn phí (price = 0), không có thời hạn
        if ($defaultPackage->price == 0) {
            $endDate = '2099-12-31 23:59:59'; // Ngày rất xa trong tương lai (vĩnh viễn)
        } else {
            $endDate = date('Y-m-d H:i:s', strtotime('+' . $defaultPackage->duration_days . ' days'));
        }

        $data = [
            'user_id' => $userId,
            'package_id' => $defaultPackage->id,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'posts_used' => 0,
            'posts_remaining' => $defaultPackage->post_limit,
            'status' => 'active'
        ];

        return $this->create($data);
    }

    // Cập nhật số lượng tin đã sử dụng
    public function updatePostsUsed($userPackageId, $increment = 1) {
        $this->db->query('UPDATE user_packages SET
                         posts_used = posts_used + :increment,
                         posts_remaining = posts_remaining - :increment_2
                         WHERE id = :id AND posts_remaining > 0');
        $this->db->bind(':increment', $increment);
        $this->db->bind(':increment_2', $increment);
        $this->db->bind(':id', $userPackageId);
        return $this->db->execute();
    }

    // Kiểm tra user có thể đăng tin không (đếm tin thực tế từ database)
    public function canUserPost($userId) {
        $userPackage = $this->getUserPackageByUserId($userId);

        if (!$userPackage) {
            return false;
        }

        // Kiểm tra trạng thái gói
        if ($userPackage->status !== 'active') {
            return false;
        }

        // Lấy thông tin package để kiểm tra giá và giới hạn
        require_once __DIR__ . '/Package.php';
        $packageModel = new Package();
        $package = $packageModel->getPackageById($userPackage->package_id);

        if (!$package) {
            return false;
        }

        // Nếu không phải gói miễn phí, kiểm tra thời hạn
        if ($package->price > 0 && strtotime($userPackage->end_date) < time()) {
            return false;
        }

        // Đếm số tin thực tế đã đăng (chỉ tính tin đang hoạt động)
        $actualPostsUsed = $this->getActualPostsUsed($userId);

        // Kiểm tra còn tin để đăng
        return $actualPostsUsed < $package->post_limit;
    }

    // Đếm số tin thực tế đã đăng của user (chỉ tính tin đang hoạt động)
    public function getActualPostsUsed($userId) {
        $this->db->query('SELECT COUNT(*) as count FROM properties
                         WHERE user_id = :user_id
                         AND active IN (0, 1)');  // 0 = pending, 1 = approved
        $this->db->bind(':user_id', $userId);
        $result = $this->db->single();
        return $result ? $result->count : 0;
    }

    // Lấy số tin còn lại của user (tính theo tin thực tế)
    public function getUserPostsRemaining($userId) {
        $userPackage = $this->getUserPackageByUserId($userId);
        if (!$userPackage) {
            return 0;
        }

        // Lấy thông tin package
        require_once __DIR__ . '/Package.php';
        $packageModel = new Package();
        $package = $packageModel->getPackageById($userPackage->package_id);

        if (!$package) {
            return 0;
        }

        // Đếm tin thực tế đã đăng
        $actualPostsUsed = $this->getActualPostsUsed($userId);

        // Tính số tin còn lại
        return max(0, $package->post_limit - $actualPostsUsed);
    }

    // Cập nhật trạng thái user package
    public function updateStatus($id, $status) {
        $this->db->query('UPDATE user_packages SET status = :status WHERE id = :id');
        $this->db->bind(':status', $status);
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }

    // Xóa user package
    public function delete($id) {
        $this->db->query('DELETE FROM user_packages WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }

    // Cập nhật gói hết hạn (chỉ áp dụng cho gói trả phí)
    public function updateExpiredPackages() {
        $this->db->query('UPDATE user_packages up
                         INNER JOIN packages p ON up.package_id = p.id
                         SET up.status = "expired"
                         WHERE up.status = "active"
                         AND up.end_date < NOW()
                         AND p.price > 0');
        return $this->db->execute();
    }

    // Lấy thống kê user packages
    public function getUserPackageStats() {
        $this->db->query('SELECT
                         COUNT(*) as total_packages,
                         COUNT(CASE WHEN status = "active" THEN 1 END) as active_packages,
                         COUNT(CASE WHEN status = "expired" THEN 1 END) as expired_packages,
                         COUNT(CASE WHEN status = "cancelled" THEN 1 END) as cancelled_packages
                         FROM user_packages');
        return $this->db->single();
    }

    // Cập nhật số tin còn lại cho tất cả user đang sử dụng một gói cụ thể
    public function updatePostsRemainingForPackage($packageId, $newPostLimit) {
        // Cập nhật posts_remaining dựa trên số tin mới và số tin đã sử dụng
        // Đảm bảo posts_remaining không âm
        $this->db->query('UPDATE user_packages SET
                         posts_remaining = GREATEST(0, :new_post_limit - posts_used)
                         WHERE package_id = :package_id AND status = "active"');
        $this->db->bind(':new_post_limit', $newPostLimit);
        $this->db->bind(':package_id', $packageId);
        return $this->db->execute();
    }

    // Lấy số lượng user đang sử dụng một gói cụ thể
    public function getUserCountByPackage($packageId) {
        $this->db->query('SELECT COUNT(*) as user_count
                         FROM user_packages
                         WHERE package_id = :package_id AND status = "active"');
        $this->db->bind(':package_id', $packageId);
        $result = $this->db->single();
        return $result ? $result->user_count : 0;
    }

    // Lấy danh sách user packages theo package ID với thông tin user
    public function getUserPackagesByPackageId($packageId) {
        $this->db->query('SELECT up.*, u.name as user_name, u.email as user_email, u.phone as user_phone
                         FROM user_packages up
                         INNER JOIN users u ON up.user_id = u.id
                         WHERE up.package_id = :package_id AND up.status = "active"
                         ORDER BY up.created_at DESC');
        $this->db->bind(':package_id', $packageId);
        return $this->db->resultSet();
    }

    // Đồng bộ số tin trong user_packages với tin thực tế
    public function syncUserPackagePostCounts($userId = null) {
        // Nếu có userId, chỉ sync cho user đó, nếu không thì sync cho tất cả
        $whereClause = $userId ? 'WHERE up.user_id = :user_id' : '';

        $this->db->query("UPDATE user_packages up
                         INNER JOIN packages p ON up.package_id = p.id
                         SET up.posts_used = (
                             SELECT COUNT(*) FROM properties
                             WHERE user_id = up.user_id
                             AND active IN (0, 1)
                         ),
                         up.posts_remaining = GREATEST(0, p.post_limit - (
                             SELECT COUNT(*) FROM properties
                             WHERE user_id = up.user_id
                             AND active IN (0, 1)
                         ))
                         {$whereClause}");

        if ($userId) {
            $this->db->bind(':user_id', $userId);
        }

        return $this->db->execute();
    }

    // Lấy thông tin chi tiết gói của user với số tin thực tế
    public function getUserPackageWithRealCounts($userId) {
        $userPackage = $this->getUserPackageByUserId($userId);
        if (!$userPackage) {
            return null;
        }

        // Lấy thông tin package
        require_once __DIR__ . '/Package.php';
        $packageModel = new Package();
        $package = $packageModel->getPackageById($userPackage->package_id);

        if (!$package) {
            return null;
        }

        // Đếm tin thực tế
        $actualPostsUsed = $this->getActualPostsUsed($userId);
        $postsRemaining = max(0, $package->post_limit - $actualPostsUsed);

        // Thêm thông tin thực tế vào object
        $userPackage->actual_posts_used = $actualPostsUsed;
        $userPackage->actual_posts_remaining = $postsRemaining;
        $userPackage->package_name = $package->name;
        $userPackage->package_post_limit = $package->post_limit;
        $userPackage->package_price = $package->price;

        return $userPackage;
    }
}
