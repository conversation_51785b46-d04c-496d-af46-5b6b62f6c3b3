<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AJAX Search Integration - <PERSON><PERSON><PERSON> Nẵng</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            padding: 2rem 0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .test-header {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .btn-test {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .iframe-container {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            margin-top: 1rem;
        }
        .iframe-container iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="mb-3">🧪 AJAX Search Integration Test</h1>
            <p class="mb-0">
                Kiểm tra tích hợp AJAX search trên trang search thực tế.
                Test các chức năng: real-time search, form submission, sort, advanced filters.
            </p>
        </div>

        <!-- API Status Check -->
        <div class="test-section">
            <h3>🔗 API Status Check</h3>
            <p>Kiểm tra trạng thái API endpoints</p>
            
            <div class="mb-3">
                <button class="btn btn-primary btn-test" onclick="checkApiStatus()">
                    Check API Status
                </button>
                <button class="btn btn-secondary btn-test" onclick="testApiRouting()">
                    Test API Routing
                </button>
            </div>

            <div id="apiStatusResults"></div>
        </div>

        <!-- Frontend Integration Test -->
        <div class="test-section">
            <h3>⚡ Frontend Integration Test</h3>
            <p>Test AJAX search trên trang search thực tế</p>
            
            <div class="mb-3">
                <button class="btn btn-success btn-test" onclick="openSearchPage()">
                    Open Search Page
                </button>
                <button class="btn btn-success btn-test" onclick="testAjaxFunctionality()">
                    Test AJAX Functionality
                </button>
                <button class="btn btn-info btn-test" onclick="checkJavaScriptErrors()">
                    Check JS Errors
                </button>
            </div>

            <div id="frontendTestResults"></div>
            
            <!-- Embedded Search Page -->
            <div class="iframe-container" id="searchPageContainer" style="display: none;">
                <iframe id="searchPageFrame" src=""></iframe>
            </div>
        </div>

        <!-- User Experience Test -->
        <div class="test-section">
            <h3>👤 User Experience Test</h3>
            <p>Test trải nghiệm người dùng với AJAX search</p>
            
            <div class="mb-3">
                <button class="btn btn-warning btn-test" onclick="testRealTimeSearch()">
                    Test Real-time Search
                </button>
                <button class="btn btn-warning btn-test" onclick="testFormSubmission()">
                    Test Form Submission
                </button>
                <button class="btn btn-warning btn-test" onclick="testSortFunctionality()">
                    Test Sort Functionality
                </button>
                <button class="btn btn-warning btn-test" onclick="testAdvancedFilters()">
                    Test Advanced Filters
                </button>
            </div>

            <div id="uxTestResults"></div>
        </div>

        <!-- Performance Test -->
        <div class="test-section">
            <h3>🚀 Performance Test</h3>
            <p>Kiểm tra hiệu suất AJAX search</p>
            
            <div class="mb-3">
                <button class="btn btn-info btn-test" onclick="testResponseTime()">
                    Test Response Time
                </button>
                <button class="btn btn-info btn-test" onclick="testLoadingStates()">
                    Test Loading States
                </button>
                <button class="btn btn-info btn-test" onclick="testErrorHandling()">
                    Test Error Handling
                </button>
            </div>

            <div id="performanceTestResults"></div>
        </div>

        <!-- Integration Summary -->
        <div class="test-section">
            <h3>📊 Integration Summary</h3>
            <div id="integrationSummary">
                <div class="alert alert-info">
                    <h5>🎯 Integration Checklist</h5>
                    <ul class="mb-0">
                        <li>✅ API endpoints configured</li>
                        <li>✅ JavaScript files included</li>
                        <li>✅ Form IDs and structure compatible</li>
                        <li>✅ Event listeners setup</li>
                        <li>🔄 Real-time search functionality</li>
                        <li>🔄 URL management and history</li>
                        <li>🔄 Loading states and error handling</li>
                        <li>🔄 Backward compatibility maintained</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test configuration
        const API_BASE_URL = '/thuenhadanang/api';
        const SEARCH_PAGE_URL = '/thuenhadanang/cho-thue-nha-dat';

        // Utility functions
        function showResult(containerId, title, status, data, timing = null) {
            const container = document.getElementById(containerId);
            const statusClass = status === 'success' ? 'status-success' : 
                               status === 'error' ? 'status-error' : 'status-warning';
            
            const timingInfo = timing ? ` (${timing}ms)` : '';
            
            const resultHtml = `
                <div class="test-result">
                    <div class="mb-2">
                        <span class="status-indicator ${statusClass}"></span>
                        <strong>${title}</strong>${timingInfo}
                    </div>
                    <pre>${typeof data === 'object' ? JSON.stringify(data, null, 2) : data}</pre>
                </div>
            `;
            
            container.innerHTML += resultHtml;
            container.scrollTop = container.scrollHeight;
        }

        // API Status Tests
        async function checkApiStatus() {
            const startTime = Date.now();
            try {
                const response = await fetch(`${API_BASE_URL}/search`);
                const data = await response.json();
                const timing = Date.now() - startTime;
                
                if (data.success) {
                    showResult('apiStatusResults', 'API Status Check', 'success', {
                        status: 'API is working',
                        propertiesCount: data.data.count,
                        responseTime: `${timing}ms`
                    }, timing);
                } else {
                    showResult('apiStatusResults', 'API Status Check', 'error', data.error, timing);
                }
            } catch (error) {
                const timing = Date.now() - startTime;
                showResult('apiStatusResults', 'API Status Check', 'error', {
                    message: error.message,
                    type: 'Network Error'
                }, timing);
            }
        }

        async function testApiRouting() {
            const routes = [
                '/thuenhadanang/api/search',
                '/thuenhadanang/api/search?keyword=test',
                '/thuenhadanang/api/search?action=filters'
            ];

            for (const route of routes) {
                const startTime = Date.now();
                try {
                    const response = await fetch(route);
                    const timing = Date.now() - startTime;
                    
                    showResult('apiStatusResults', `Route: ${route}`, 
                        response.ok ? 'success' : 'error', {
                        status: response.status,
                        statusText: response.statusText,
                        url: route
                    }, timing);
                } catch (error) {
                    const timing = Date.now() - startTime;
                    showResult('apiStatusResults', `Route: ${route}`, 'error', {
                        message: error.message
                    }, timing);
                }
            }
        }

        // Frontend Integration Tests
        function openSearchPage() {
            const iframe = document.getElementById('searchPageFrame');
            const container = document.getElementById('searchPageContainer');
            
            iframe.src = SEARCH_PAGE_URL;
            container.style.display = 'block';
            
            showResult('frontendTestResults', 'Search Page Loaded', 'success', {
                url: SEARCH_PAGE_URL,
                message: 'Search page loaded in iframe below'
            });

            // Check if page loads successfully
            iframe.onload = function() {
                showResult('frontendTestResults', 'Page Load Complete', 'success', {
                    message: 'Search page loaded successfully',
                    readyState: 'complete'
                });
            };

            iframe.onerror = function() {
                showResult('frontendTestResults', 'Page Load Error', 'error', {
                    message: 'Failed to load search page'
                });
            };
        }

        function testAjaxFunctionality() {
            showResult('frontendTestResults', 'AJAX Functionality Test', 'success', {
                message: 'Testing AJAX functionality...',
                steps: [
                    '1. Check if ajax-search.js is loaded',
                    '2. Verify AjaxSearch class is initialized',
                    '3. Test form event listeners',
                    '4. Verify API calls are made'
                ],
                note: 'Open browser DevTools to see console logs and network requests'
            });
        }

        function checkJavaScriptErrors() {
            showResult('frontendTestResults', 'JavaScript Error Check', 'success', {
                message: 'Check browser console for JavaScript errors',
                instructions: [
                    '1. Open DevTools (F12)',
                    '2. Go to Console tab',
                    '3. Look for any red error messages',
                    '4. Check Network tab for failed requests'
                ]
            });
        }

        // UX Tests
        function testRealTimeSearch() {
            showResult('uxTestResults', 'Real-time Search Test', 'warning', {
                message: 'Manual test required',
                steps: [
                    '1. Type in keyword field',
                    '2. Wait 800ms for debounced search',
                    '3. Verify AJAX request is made',
                    '4. Check results update without page reload'
                ]
            });
        }

        function testFormSubmission() {
            showResult('uxTestResults', 'Form Submission Test', 'warning', {
                message: 'Manual test required',
                steps: [
                    '1. Fill search form fields',
                    '2. Click search button',
                    '3. Verify preventDefault works',
                    '4. Check AJAX search is triggered'
                ]
            });
        }

        function testSortFunctionality() {
            showResult('uxTestResults', 'Sort Functionality Test', 'warning', {
                message: 'Manual test required',
                steps: [
                    '1. Change sort dropdown',
                    '2. Verify AJAX request includes sort parameter',
                    '3. Check results are re-sorted',
                    '4. Verify URL is updated'
                ]
            });
        }

        function testAdvancedFilters() {
            showResult('uxTestResults', 'Advanced Filters Test', 'warning', {
                message: 'Manual test required',
                steps: [
                    '1. Click "Bộ lọc nâng cao" button',
                    '2. Set bedrooms, bathrooms, area, direction',
                    '3. Click "Hiển thị kết quả"',
                    '4. Verify AJAX search with all parameters'
                ]
            });
        }

        // Performance Tests
        async function testResponseTime() {
            const tests = [];
            const iterations = 3;
            
            for (let i = 0; i < iterations; i++) {
                const startTime = Date.now();
                try {
                    await fetch(`${API_BASE_URL}/search?test=${i}`);
                    tests.push(Date.now() - startTime);
                } catch (error) {
                    tests.push(-1);
                }
            }
            
            const avgTime = tests.filter(t => t > 0).reduce((a, b) => a + b, 0) / tests.filter(t => t > 0).length;
            
            showResult('performanceTestResults', 'Response Time Test', 'success', {
                iterations,
                averageTime: `${avgTime.toFixed(2)}ms`,
                allTimes: tests,
                rating: avgTime < 100 ? 'Excellent' : avgTime < 300 ? 'Good' : 'Needs Optimization'
            });
        }

        function testLoadingStates() {
            showResult('performanceTestResults', 'Loading States Test', 'warning', {
                message: 'Manual test required',
                checkpoints: [
                    '✅ Loading overlay appears during search',
                    '✅ Spinner animation works',
                    '✅ Loading text is displayed',
                    '✅ Loading disappears when complete',
                    '✅ Multiple requests are handled properly'
                ]
            });
        }

        function testErrorHandling() {
            showResult('performanceTestResults', 'Error Handling Test', 'warning', {
                message: 'Manual test required',
                scenarios: [
                    '1. Network error (disconnect internet)',
                    '2. API error (invalid parameters)',
                    '3. Timeout error (slow connection)',
                    '4. JSON parse error (malformed response)'
                ],
                expectedBehavior: 'Graceful error messages, no crashes'
            });
        }

        // Auto-run basic tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkApiStatus();
            }, 500);
        });
    </script>
</body>
</html>
