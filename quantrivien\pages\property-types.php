<?php
// Ki<PERSON>m tra quyền truy cập
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Khởi tạo model
require_once '../app/models/PropertyType.php';
$propertyTypeModel = new PropertyType();

// Khởi tạo biến thông báo
$message = '';
$messageType = '';

// Xử lý các action
$action = isset($_GET['action']) ? $_GET['action'] : '';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Khởi tạo biến property type cho form edit
$editPropertyType = null;

// Xử lý thêm loại hình mới
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_property_type'])) {
    // Validate dữ liệu
    $errors = [];

    if (empty($_POST['name'])) {
        $errors[] = 'Tên loại hình không được để trống';
    }

    // Nếu không có lỗi, tiến hành thêm mới
    if (empty($errors)) {
        // Xử lý slug
        $slug = trim($_POST['slug']);

        // Nếu slug rỗng, tạo slug từ tên
        if (empty($slug)) {
            $slug = $propertyTypeModel->createSlug($_POST['name']);
        } else {
            // Kiểm tra xem slug đã tồn tại chưa
            if ($propertyTypeModel->isSlugExists($slug)) {
                $errors[] = 'Slug đã tồn tại, vui lòng chọn slug khác';
                $message = implode('<br>', $errors);
                $messageType = 'danger';
                // Giữ action là 'add' để hiển thị lại form
                $action = 'add';
                // Hiển thị form với thông báo lỗi
                goto display_form;
            }
        }

        // Chuẩn bị dữ liệu
        $propertyTypeData = [
            'name' => trim($_POST['name']),
            'slug' => $slug,
            'description' => !empty($_POST['description']) ? trim($_POST['description']) : null,
            'status' => isset($_POST['status']) ? 1 : 0
        ];

        // Thêm loại hình mới
        if ($propertyTypeModel->create($propertyTypeData)) {
            $message = 'Thêm loại hình mới thành công!';
            $messageType = 'success';

            // Đặt action về rỗng để hiển thị danh sách
            $action = '';
        } else {
            $message = 'Có lỗi xảy ra! Không thể thêm loại hình mới.';
            $messageType = 'danger';
        }
    } else {
        // Hiển thị lỗi
        $message = implode('<br>', $errors);
        $messageType = 'danger';
    }
}

// Xử lý cập nhật loại hình
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_property_type'])) {
    // Validate dữ liệu
    $errors = [];

    if (empty($_POST['name'])) {
        $errors[] = 'Tên loại hình không được để trống';
    }

    // Nếu không có lỗi, tiến hành cập nhật
    if (empty($errors)) {
        $propertyTypeId = intval($_POST['property_type_id']);
        $currentPropertyType = $propertyTypeModel->getPropertyTypeById($propertyTypeId);

        // Xử lý slug
        $slug = trim($_POST['slug']);

        // Nếu slug rỗng, tạo slug từ tên
        if (empty($slug)) {
            $slug = $propertyTypeModel->createSlug($_POST['name']);
        }
        // Nếu slug đã thay đổi, kiểm tra xem slug mới đã tồn tại chưa
        else if ($slug != $currentPropertyType->slug) {
            // Kiểm tra xem slug đã tồn tại chưa
            if ($propertyTypeModel->isSlugExists($slug, $propertyTypeId)) {
                $errors[] = 'Slug đã tồn tại, vui lòng chọn slug khác';
                $editPropertyType = $currentPropertyType;
                $message = implode('<br>', $errors);
                $messageType = 'danger';
                // Giữ action là 'edit' để hiển thị lại form
                $action = 'edit';
                // Hiển thị form với thông báo lỗi
                goto display_form;
            }
        }

        // Chuẩn bị dữ liệu
        $propertyTypeData = [
            'id' => $propertyTypeId,
            'name' => trim($_POST['name']),
            'slug' => $slug,
            'description' => !empty($_POST['description']) ? trim($_POST['description']) : null,
            'status' => isset($_POST['status']) ? 1 : 0
        ];

        // Cập nhật loại hình
        if ($propertyTypeModel->update($propertyTypeData)) {
            $message = 'Cập nhật loại hình thành công!';
            $messageType = 'success';

            // Đặt action về rỗng để hiển thị danh sách
            $action = '';
        } else {
            $message = 'Có lỗi xảy ra! Không thể cập nhật loại hình.';
            $messageType = 'danger';
        }
    } else {
        // Hiển thị lỗi
        $message = implode('<br>', $errors);
        $messageType = 'danger';
    }
}

// Xử lý xóa loại hình
if ($action == 'delete' && $id > 0) {
    if ($propertyTypeModel->delete($id)) {
        $message = 'Xóa loại hình thành công!';
        $messageType = 'success';
    } else {
        $message = 'Có lỗi xảy ra! Không thể xóa loại hình.';
        $messageType = 'danger';
    }

    // Đặt action về rỗng để hiển thị danh sách
    $action = '';
}

// Lấy thông tin loại hình cần chỉnh sửa
if ($action == 'edit' && $id > 0) {
    $editPropertyType = $propertyTypeModel->getPropertyTypeById($id);
    if (!$editPropertyType) {
        $message = 'Không tìm thấy loại hình!';
        $messageType = 'danger';
    }
}

// Xử lý hiển thị form thêm mới
if ($action == 'add') {
    // Không cần làm gì đặc biệt, chỉ cần hiển thị form
}

// Xử lý tìm kiếm
$searchTerm = '';
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $searchTerm = trim($_GET['search']);
    $propertyTypes = $propertyTypeModel->searchPropertyTypes($searchTerm);
} else {
    // Lấy tất cả loại hình nếu không có tìm kiếm
    $propertyTypes = $propertyTypeModel->getAllPropertyTypes();
}

// Label cho goto
display_form:
?>

<!-- Hiển thị thông báo nếu có -->
<?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">Quản lý loại hình bất động sản</h1>
    <?php if ($action == ''): ?>
        <a href="index.php?page=property-types&action=add" class="btn btn-primary">
            <i class="bi bi-plus-circle me-1"></i> Thêm mới
        </a>
    <?php endif; ?>
</div>

<?php if ($action == 'add' || $action == 'edit'): ?>
    <!-- Form thêm/sửa loại hình -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-<?php echo $action == 'add' ? 'plus-circle' : 'pencil-square'; ?> me-1"></i>
            <?php echo $action == 'add' ? 'Thêm loại hình mới' : 'Chỉnh sửa loại hình'; ?>
        </div>
        <div class="card-body">
            <form action="index.php?page=property-types" method="POST">
                <?php if ($action == 'edit'): ?>
                    <input type="hidden" name="property_type_id" value="<?php echo $editPropertyType->id; ?>">
                <?php endif; ?>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="name" class="form-label">Tên loại hình <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required
                               value="<?php echo $action == 'edit' ? htmlspecialchars($editPropertyType->name) : ''; ?>">
                    </div>
                    <div class="col-md-6">
                        <label for="slug" class="form-label">Slug</label>
                        <input type="text" class="form-control" id="slug" name="slug"
                               value="<?php echo $action == 'edit' ? htmlspecialchars($editPropertyType->slug) : ''; ?>"
                               placeholder="Để trống để tự động tạo từ tên">
                        <div class="form-text">Định dạng URL thân thiện, chỉ chứa chữ cái, số và dấu gạch ngang.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="status" class="form-label">Trạng thái</label>
                        <div class="form-check form-switch mt-2">
                            <input class="form-check-input" type="checkbox" id="status" name="status"
                                   <?php echo ($action == 'edit' && $editPropertyType->status == 1) || $action == 'add' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="status">Kích hoạt</label>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Mô tả</label>
                    <textarea class="form-control" id="description" name="description" rows="3"><?php echo $action == 'edit' ? htmlspecialchars($editPropertyType->description) : ''; ?></textarea>
                </div>

                <div class="mt-4">
                    <button type="submit" name="<?php echo $action == 'add' ? 'add_property_type' : 'update_property_type'; ?>" class="btn btn-success">
                        <i class="bi bi-save me-1"></i> <?php echo $action == 'add' ? 'Thêm mới' : 'Cập nhật'; ?>
                    </button>
                    <a href="index.php?page=property-types" class="btn btn-secondary ms-2">
                        <i class="bi bi-x-circle me-1"></i> Hủy
                    </a>
                </div>
            </form>
        </div>
    </div>
<?php else: ?>
    <!-- Form tìm kiếm -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-search me-1"></i>
            Tìm kiếm loại hình
        </div>
        <div class="card-body">
            <form action="index.php" method="GET" class="row g-3">
                <input type="hidden" name="page" value="property-types">

                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" placeholder="Nhập tên hoặc mô tả loại hình..." value="<?php echo htmlspecialchars($searchTerm); ?>">
                        <button class="btn btn-primary" type="submit">
                            <i class="bi bi-search me-1"></i> Tìm kiếm
                        </button>
                    </div>
                </div>

                <?php if (!empty($searchTerm)): ?>
                    <div class="col-md-12">
                        <a href="index.php?page=property-types" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i> Xóa bộ lọc
                        </a>
                    </div>
                <?php endif; ?>
            </form>
        </div>
    </div>

    <!-- Bảng danh sách loại hình -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-table me-1"></i>
            Danh sách loại hình bất động sản
        </div>
        <div class="card-body">
            <?php if (empty($propertyTypes)): ?>
                <div class="alert alert-info">
                    Không tìm thấy loại hình bất động sản nào.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th width="5%">ID</th>
                                <th width="20%">Tên loại hình</th>
                                <th width="15%">Slug</th>
                                <th>Mô tả</th>
                                <th width="10%">Trạng thái</th>
                                <th width="15%">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($propertyTypes as $propertyType): ?>
                                <tr>
                                    <td><?php echo $propertyType->id; ?></td>
                                    <td><?php echo htmlspecialchars($propertyType->name); ?></td>
                                    <td><?php echo htmlspecialchars($propertyType->slug); ?></td>
                                    <td><?php echo htmlspecialchars($propertyType->description ?? ''); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $propertyType->status == 1 ? 'success' : 'danger'; ?>">
                                            <?php echo $propertyType->status == 1 ? 'Kích hoạt' : 'Vô hiệu'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="index.php?page=property-types&action=edit&id=<?php echo $propertyType->id; ?>" class="btn btn-sm btn-primary">
                                            <i class="bi bi-pencil-square"></i> Sửa
                                        </a>
                                        <a href="index.php?page=property-types&action=delete&id=<?php echo $propertyType->id; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Bạn có chắc chắn muốn xóa loại hình này?');">
                                            <i class="bi bi-trash"></i> Xóa
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<!-- JavaScript để tự động tạo slug từ tên -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');

    if (nameInput && slugInput) {
        nameInput.addEventListener('input', function() {
            // Chỉ tự động tạo slug nếu trường slug rỗng
            if (slugInput.value === '') {
                slugInput.value = createSlug(nameInput.value);
            }
        });
    }

    // Hàm tạo slug từ chuỗi
    function createSlug(str) {
        // Chuyển đổi tiếng Việt sang không dấu
        str = str.toLowerCase();
        str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
        str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
        str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
        str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
        str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
        str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
        str = str.replace(/đ/g, 'd');

        // Xóa ký tự đặc biệt
        str = str.replace(/[^a-z0-9\s-]/g, '');

        // Xóa khoảng trắng thay bằng dấu gạch ngang
        str = str.replace(/[\s]+/g, '-');

        return str;
    }
});
</script>