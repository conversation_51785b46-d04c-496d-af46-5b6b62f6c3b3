<?php
// <PERSON><PERSON> lý AJAX cho các chức năng liên quan đến bất động sản
// <PERSON><PERSON><PERSON> bảo không có output nào trước header
error_reporting(E_ALL);
ini_set('display_errors', 0);
ob_start();

// Bắt đầu session
session_start();

// Kiểm tra đăng nhập
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['success' => false, 'message' => 'Không có quyền truy cập']);
    exit;
}

// Kết nối database và models
require_once __DIR__ . '/../app/libraries/Database.php';
require_once __DIR__ . '/../app/models/Property.php';

// Khởi tạo các model cần thiết
$propertyModel = new Property();

// Xử lý các action
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'delete_image':
            // Xóa hình ảnh
            $imageId = $_POST['image_id'] ?? '';
            $propertyId = $_POST['property_id'] ?? '';

            if (empty($imageId) || empty($propertyId)) {
                echo json_encode(['success' => false, 'message' => 'Thiếu thông tin cần thiết']);
                exit;
            }

            // Ghi log để debug
            error_log('AJAX delete_image: Image ID = ' . $imageId . ', Property ID = ' . $propertyId);

            // Xóa hình ảnh
            $result = $propertyModel->deletePropertyImage($imageId);

            // Thêm thông tin debug
            if (!$result) {
                error_log('AJAX delete_image failed: Image ID = ' . $imageId . ', Property ID = ' . $propertyId);
                // Kiểm tra xem có lỗi nào được ghi lại trong error log không
                $errorMessage = error_get_last();
                if ($errorMessage) {
                    error_log('PHP Error: ' . print_r($errorMessage, true));
                }
            }

            echo json_encode([
                'success' => $result,
                'debug' => [
                    'image_id' => $imageId,
                    'property_id' => $propertyId
                ]
            ]);
            break;

        case 'set_main_image':
            // Đặt ảnh chính
            $imageId = $_POST['image_id'] ?? '';
            $propertyId = $_POST['property_id'] ?? '';

            if (empty($imageId) || empty($propertyId)) {
                echo json_encode(['success' => false, 'message' => 'Thiếu thông tin cần thiết']);
                exit;
            }

            // Ghi log để debug
            error_log('AJAX set_main_image: Image ID = ' . $imageId . ', Property ID = ' . $propertyId);

            // Đặt ảnh chính
            $result = $propertyModel->setMainImage($imageId, $propertyId);

            // Thêm thông tin debug
            if (!$result) {
                error_log('AJAX set_main_image failed: Image ID = ' . $imageId . ', Property ID = ' . $propertyId);
                // Kiểm tra xem có lỗi nào được ghi lại trong error log không
                $errorMessage = error_get_last();
                if ($errorMessage) {
                    error_log('PHP Error: ' . print_r($errorMessage, true));
                }
            }

            echo json_encode(['success' => $result, 'debug' => [
                'image_id' => $imageId,
                'property_id' => $propertyId
            ]]);
            break;

        case 'save_image_order':
            // Lưu thứ tự hình ảnh
            $propertyId = $_POST['property_id'] ?? '';
            $imageOrderData = $_POST['image_order_data'] ?? '';

            if (empty($propertyId) || empty($imageOrderData)) {
                echo json_encode(['success' => false, 'message' => 'Thiếu thông tin cần thiết']);
                exit;
            }

            // Ghi log để debug
            error_log('AJAX save_image_order: Property ID = ' . $propertyId);
            error_log('Image order data: ' . $imageOrderData);

            try {
                $imageOrder = json_decode($imageOrderData, true);
                error_log('Decoded image order: ' . print_r($imageOrder, true));

                if (is_array($imageOrder) && count($imageOrder) > 0) {
                    // Lấy thông tin bất động sản hiện tại
                    $property = $propertyModel->getPropertyById($propertyId);

                    if ($property) {
                        // Lấy danh sách hình ảnh hiện tại
                        $currentImages = $propertyModel->getPropertyImages($propertyId);
                        error_log('Current images: ' . print_r($currentImages, true));

                        // Tạo mảng mới theo thứ tự đã sắp xếp
                        $newImagesOrder = [];
                        $newMainImage = null;

                        // Lặp qua thứ tự mới
                        foreach ($imageOrder as $imageId) {
                            // Tìm hình ảnh tương ứng
                            foreach ($currentImages as $image) {
                                if ($image->id === $imageId) {
                                    $newImagesOrder[] = $image->image_path;

                                    // Đặt ảnh đầu tiên làm ảnh chính
                                    if ($newMainImage === null) {
                                        $newMainImage = $image->image_path;
                                    }

                                    break;
                                }
                            }
                        }

                        error_log('New images order: ' . print_r($newImagesOrder, true));
                        error_log('New main image: ' . $newMainImage);

                        // Cập nhật thứ tự hình ảnh và ảnh chính
                        if (!empty($newImagesOrder)) {
                            // Cập nhật vào database
                            $db = new Database();
                            $db->query('UPDATE properties SET images = :images, main_image = :main_image WHERE id = :id');
                            $db->bind(':id', $propertyId);
                            $db->bind(':images', json_encode($newImagesOrder));
                            $db->bind(':main_image', $newMainImage);

                            if ($db->execute()) {
                                echo json_encode([
                                    'success' => true,
                                    'message' => 'Cập nhật thứ tự hình ảnh thành công!',
                                    'debug' => [
                                        'property_id' => $propertyId,
                                        'image_count' => count($newImagesOrder)
                                    ]
                                ]);
                                error_log('Image order updated successfully for property ID: ' . $propertyId);
                            } else {
                                echo json_encode([
                                    'success' => false,
                                    'message' => 'Có lỗi xảy ra! Không thể cập nhật thứ tự hình ảnh.',
                                    'debug' => [
                                        'property_id' => $propertyId,
                                        'error' => $db->errorInfo()
                                    ]
                                ]);
                                error_log('Failed to update image order for property ID: ' . $propertyId . '. Database error: ' . print_r($db->errorInfo(), true));
                            }
                        } else {
                            echo json_encode([
                                'success' => false,
                                'message' => 'Không có hình ảnh nào để sắp xếp.',
                                'debug' => [
                                    'property_id' => $propertyId
                                ]
                            ]);
                            error_log('No images to sort for property ID: ' . $propertyId);
                        }
                    } else {
                        echo json_encode([
                            'success' => false,
                            'message' => 'Không tìm thấy bất động sản!',
                            'debug' => [
                                'property_id' => $propertyId
                            ]
                        ]);
                        error_log('Property not found. ID: ' . $propertyId);
                    }
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Dữ liệu thứ tự hình ảnh không hợp lệ.',
                        'debug' => [
                            'property_id' => $propertyId,
                            'image_order_data' => $imageOrderData
                        ]
                    ]);
                    error_log('Invalid image order data for property ID: ' . $propertyId);
                }
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Lỗi xử lý dữ liệu thứ tự hình ảnh: ' . $e->getMessage(),
                    'debug' => [
                        'property_id' => $propertyId,
                        'error' => $e->getMessage()
                    ]
                ]);
                error_log('Error processing image order data for property ID: ' . $propertyId . '. Error: ' . $e->getMessage());
            }
            break;

        default:
            echo json_encode(['success' => false, 'message' => 'Action không hợp lệ']);
            break;
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Phương thức không hợp lệ']);
}

// Kết thúc output buffering
ob_end_flush();
