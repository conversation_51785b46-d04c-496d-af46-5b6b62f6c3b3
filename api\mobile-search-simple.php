<?php
/**
 * Simple Mobile Search API
 * Minimal implementation for testing
 */

// Clean output buffer and set headers
while (ob_get_level()) {
    ob_end_clean();
}

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Define BASE_PATH for UrlHandler
    if (!defined('BASE_PATH')) {
        define('BASE_PATH', __DIR__ . '/..');
    }

    // Include required files for database access
    require_once __DIR__ . '/../app/libraries/Database.php';
    require_once __DIR__ . '/../app/models/Property.php';
    require_once __DIR__ . '/../app/models/PropertyType.php';
    require_once __DIR__ . '/../app/models/Ward.php';
    require_once __DIR__ . '/../app/libraries/UrlHandler.php';

    // Initialize models
    $propertyModel = new Property();
    $propertyTypeModel = new PropertyType();
    $wardModel = new Ward();
    $urlHandler = new UrlHandler();

    // Get parameters
    $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
    $type = isset($_GET['type']) ? trim($_GET['type']) : '';
    $ward = isset($_GET['ward']) ? trim($_GET['ward']) : '';
    $price = isset($_GET['price']) ? trim($_GET['price']) : '';
    $bedrooms = isset($_GET['bedrooms']) ? trim($_GET['bedrooms']) : '';
    $bathrooms = isset($_GET['bathrooms']) ? trim($_GET['bathrooms']) : '';
    $area = isset($_GET['area']) ? trim($_GET['area']) : '';
    $direction = isset($_GET['direction']) ? trim($_GET['direction']) : '';
    $sort = isset($_GET['sort']) ? trim($_GET['sort']) : 'default';
    $action = isset($_GET['action']) ? trim($_GET['action']) : 'search';

    // Log request
    error_log('Mobile API with Database: ' . json_encode($_GET));

    // Perform actual database search
    $properties = [];

    if (!empty($keyword)) {
        // Search with keyword
        $properties = $propertyModel->searchProperties(
            $keyword, $type, $ward, $price, $sort,
            $bedrooms, $bathrooms, $direction, $area
        );
    } else {
        // Search without keyword
        $properties = $propertyModel->getPropertiesByTypeAndWard(
            $type, $ward, $price, $area, $bedrooms,
            $sort, $bathrooms, $direction
        );
    }

    // Format properties for mobile response
    $formattedProperties = [];
    foreach ($properties as $property) {
        // Handle images
        $images = json_decode($property->images, true);
        $imagePath = !empty($images) ? '/thuenhadanang/public/uploads/properties/' . $images[0] : '/thuenhadanang/public/images/no-image.jpg';

        // Format price
        $pricePeriod = '';
        switch ($property->price_period) {
            case 'month':
                $pricePeriod = 'tháng';
                break;
            case 'quarter':
                $pricePeriod = 'quý';
                break;
            case 'year':
                $pricePeriod = 'năm';
                break;
            default:
                $pricePeriod = 'tháng';
        }

        $formattedPrice = number_format($property->price / 1000000, 1);
        $formattedPrice = rtrim(rtrim($formattedPrice, '0'), '.');
        $priceDisplay = $formattedPrice . ' triệu/' . $pricePeriod;

        $formattedProperties[] = [
            'id' => $property->id,
            'title' => $property->title,
            'slug' => $property->slug,
            'price' => $property->price,
            'price_period' => $property->price_period,
            'area' => $property->area,
            'bedrooms' => $property->bedrooms,
            'bathrooms' => $property->bathrooms,
            'ward_name' => $property->ward_name,
            'type_name' => $property->type_name,
            'featured' => $property->featured,
            'created_at' => $property->created_at,
            'image' => $imagePath,
            'formatted_price' => $priceDisplay,
            'is_new' => (strtotime($property->created_at) > strtotime('-7 days')),
            'url' => '/thuenhadanang/' . $property->slug . '-' . $property->id
        ];
    }

    // Properties are already filtered by database query
    $filteredProperties = $formattedProperties;

    // Build response based on action
    switch ($action) {
        case 'filters':
            // Get real data from database
            $propertyTypes = $propertyTypeModel->getAllPropertyTypes();
            $wards = $wardModel->getAllWards();

            $response = [
                'success' => true,
                'data' => [
                    'propertyTypes' => array_map(function($type) {
                        return ['slug' => $type->slug, 'name' => $type->name];
                    }, $propertyTypes),
                    'wards' => array_map(function($ward) {
                        return ['slug' => $ward->slug, 'name' => $ward->name];
                    }, $wards)
                ]
            ];
            break;

        case 'suggestions':
            $query = isset($_GET['q']) ? trim($_GET['q']) : '';
            $suggestions = [];

            if (strlen($query) >= 2) {
                // Get real suggestions from database
                $suggestions = $propertyModel->getSearchSuggestions($query, 5);
            }

            $response = [
                'success' => true,
                'suggestions' => $suggestions,
                'query' => $query
            ];
            break;

        default: // search
            // Build SEO-friendly URL using UrlHandler
            $urlParams = [
                'type' => $type,
                'ward' => $ward,
                'price' => $price,
                'area' => $area,
                'bedrooms' => $bedrooms,
                'bathrooms' => $bathrooms,
                'direction' => $direction
            ];

            // Add additional params
            $additionalParams = [];
            if (!empty($keyword)) {
                $additionalParams['keyword'] = $keyword;
            }
            if (!empty($sort) && $sort !== 'default') {
                $additionalParams['sort'] = $sort;
            }

            if (!empty($additionalParams)) {
                $urlParams['additional_params'] = $additionalParams;
            }

            // Generate SEO-friendly URL
            $searchUrl = $urlHandler->buildUrl($urlParams);

            // Generate dynamic title (same logic as PC SearchController)
            $propertyTypeName = 'nhà đất';
            if (!empty($type)) {
                $propertyType = $propertyTypeModel->getPropertyTypeBySlug($type);
                if ($propertyType) {
                    $propertyTypeName = mb_strtolower($propertyType->name, 'UTF-8');
                }
            }

            // Check current URL for "cho-thue-nha-dat" (same as PC)
            $currentUrl = $_SERVER['REQUEST_URI'];
            if (strpos($currentUrl, 'cho-thue-nha-dat') !== false) {
                $propertyTypeName = 'nhà đất';
            }

            // Get ward name for title
            $wardName = '';
            if (!empty($ward)) {
                $wardObj = $wardModel->getWardBySlug($ward);
                if ($wardObj) {
                    $wardName = $wardObj->name;
                }
            }

            // Create dynamic title (same logic as PC - NO keyword in title)
            $currentMonth = date('m');
            $currentYear = date('Y');

            if (!empty($type) && !empty($ward)) {
                $dynamicTitle = "Cho thuê {$propertyTypeName} tại {$wardName} Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
            } else if (!empty($type)) {
                $dynamicTitle = "Cho thuê {$propertyTypeName} tại Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
            } else if (!empty($ward)) {
                $dynamicTitle = "Cho thuê nhà đất tại {$wardName} Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
            } else {
                $dynamicTitle = "Cho thuê nhà đất tại Đà Nẵng cập nhật tháng {$currentMonth}/{$currentYear}";
            }

            // NOTE: PC does NOT add keyword to title, so mobile should match PC behavior
            // Keyword is handled separately in search logic, not in title display

            $response = [
                'success' => true,
                'data' => [
                    'properties' => $filteredProperties,
                    'count' => count($filteredProperties),
                    'metadata' => [
                        'title' => $dynamicTitle,
                        'selectedFilters' => [
                            'keyword' => $keyword,
                            'type' => $type,
                            'ward' => $ward,
                            'price' => $price,
                            'bedrooms' => $bedrooms,
                            'bathrooms' => $bathrooms,
                            'area' => $area,
                            'direction' => $direction,
                            'sort' => $sort
                        ],
                        'url' => $searchUrl
                    ]
                ],
                'timestamp' => time(),
                'mobile' => true
            ];
            break;
    }

    // Output JSON response
    echo json_encode($response, JSON_PRETTY_PRINT);

} catch (Exception $e) {
    // Error response
    error_log('Simple Mobile API Error: ' . $e->getMessage());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => [
            'message' => 'Simple mobile search error',
            'details' => $e->getMessage(),
            'mobile' => true
        ],
        'timestamp' => time()
    ]);
}
?>
