<?php
require_once __DIR__ . '/../libraries/Database.php';

class PropertyContact {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    // Lấy thông tin liên hệ theo property_id
    public function getContactByPropertyId($propertyId) {
        $this->db->query('SELECT * FROM property_contacts WHERE property_id = :property_id');
        $this->db->bind(':property_id', $propertyId);
        return $this->db->single();
    }

    // Thêm thông tin liên hệ mới
    public function create($data) {
        $this->db->query('INSERT INTO property_contacts (property_id, name, phone, zalo, email)
                          VALUES (:property_id, :name, :phone, :zalo, :email)');

        // Bind values
        $this->db->bind(':property_id', $data['property_id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':phone', $data['phone']);
        $this->db->bind(':zalo', $data['zalo'] ?? null);
        $this->db->bind(':email', $data['email'] ?? null);

        // Execute
        $result = $this->db->execute();

        // Ghi log để debug
        error_log('PropertyContact create result: ' . ($result ? 'success' : 'failed') . ' for property_id: ' . $data['property_id']);

        return $result;
    }

    // Cập nhật thông tin liên hệ
    public function update($data) {
        $this->db->query('UPDATE property_contacts SET
                          name = :name,
                          phone = :phone,
                          zalo = :zalo,
                          email = :email
                          WHERE property_id = :property_id');

        // Bind values
        $this->db->bind(':property_id', $data['property_id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':phone', $data['phone']);
        $this->db->bind(':zalo', $data['zalo'] ?? null);
        $this->db->bind(':email', $data['email'] ?? null);

        // Execute
        return $this->db->execute();
    }

    // Xóa thông tin liên hệ
    public function delete($propertyId) {
        $this->db->query('DELETE FROM property_contacts WHERE property_id = :property_id');
        $this->db->bind(':property_id', $propertyId);
        return $this->db->execute();
    }

    // Lưu hoặc cập nhật thông tin liên hệ
    public function saveOrUpdate($data) {
        // Kiểm tra xem đã có thông tin liên hệ cho property này chưa
        $contact = $this->getContactByPropertyId($data['property_id']);

        if ($contact) {
            // Nếu đã có, cập nhật
            return $this->update($data);
        } else {
            // Nếu chưa có, tạo mới
            return $this->create($data);
        }
    }
}
