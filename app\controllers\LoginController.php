<?php
require_once 'app/models/User.php';

class LoginController {
    private $userModel;
    
    public function __construct() {
        $this->userModel = new User();
    }

    public function index() {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Nếu đã đăng nhập thì chuyển về trang chủ
        if (isset($_SESSION['user_id'])) {
            header('Location: /thuenhadanang/');
            exit;
        }

        // Thiết lập tiêu đề trang
        $title = 'Đăng nhập - Thuê <PERSON>hà Đà Nẵng';
        
        // Lấy thông báo lỗi nếu có
        $error = $_SESSION['login_error'] ?? '';
        unset($_SESSION['login_error']);
        
        // Thiết lập view và data
        $view = 'login';
        $data = ['error' => $error];
        
        // Render layout với view
        require 'app/views/layout.php';
    }

    public function auth() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Khởi tạo session nếu chưa có
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            // Lấy dữ liệu từ form
            $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
            $password = $_POST['password'] ?? '';
            $remember = isset($_POST['remember']);

            // Debug: Kiểm tra dữ liệu đầu vào
            error_log("Login attempt - Email: " . $email);
            
            // Validate dữ liệu
            if (empty($email) || empty($password)) {
                $_SESSION['login_error'] = 'Vui lòng nhập đầy đủ email và mật khẩu';
                header('Location: /thuenhadanang/login');
                exit;
            }

            // Lấy thông tin user từ database
            $user = $this->userModel->getUserByEmail($email);
            
            // Debug: Kiểm tra user có tồn tại không
            if ($user) {
                error_log("User found - ID: " . $user->id);
                error_log("Stored hash: " . $user->password);
                error_log("Input password: " . $password);
                
                // Debug: Kiểm tra kết quả verify password
                $verify_result = password_verify($password, $user->password);
                error_log("Password verify result: " . ($verify_result ? 'true' : 'false'));
            } else {
                error_log("User not found for email: " . $email);
            }

            // Kiểm tra đăng nhập
            if ($user && password_verify($password, $user->password)) {
                // Lưu thông tin user vào session
                $_SESSION['user_id'] = $user->id;
                $_SESSION['user_role'] = $user->role;
                $_SESSION['user_name'] = $user->fullname;
                $_SESSION['user_email'] = $user->email;
                $_SESSION['user_avatar'] = $user->avatar;

                error_log("Login successful for user: " . $user->email);

                // Xử lý "Ghi nhớ đăng nhập"
                if ($remember) {
                    // Tạo token ngẫu nhiên
                    $token = bin2hex(random_bytes(32));
                    
                    // Lưu token vào cookie, thời hạn 30 ngày
                    setcookie('remember_token', $token, time() + (86400 * 30), '/', '', true, true);
                }

                // Chuyển hướng về trang chủ
                header('Location: /thuenhadanang/dashboard');
                exit;
            } else {
                error_log("Login failed for email: " . $email);
                // Lưu thông báo lỗi vào session
                $_SESSION['login_error'] = 'Email hoặc mật khẩu không chính xác';
                header('Location: /thuenhadanang/login');
                exit;
            }
        }

        // Nếu không phải POST request, chuyển về trang đăng nhập
        header('Location: /thuenhadanang/login');
        exit;
    }

    public function logout() {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Xóa cookie ghi nhớ đăng nhập nếu có
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }

        // Hủy session
        session_destroy();
        
        // Chuyển hướng về trang chủ
        header('Location: /thuenhadanang');
        exit;
    }
} 