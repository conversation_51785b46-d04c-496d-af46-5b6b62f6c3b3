<?php
require_once __DIR__ . '/../libraries/Database.php';

class ExtensionRequest {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    // Tạo yêu cầu gia hạn mới
    public function createRequest($propertyId, $userId) {
        try {
            // Kiểm tra xem đã có yêu cầu pending chưa
            if ($this->hasPendingRequest($propertyId)) {
                return ['success' => false, 'message' => 'Bất động sản này đã có yêu cầu gia hạn đang chờ xử lý'];
            }

            // Lấy thông tin property để tính ngày gia hạn
            require_once __DIR__ . '/Property.php';
            $propertyModel = new Property();
            $property = $propertyModel->getPropertyById($propertyId);

            if (!$property) {
                return ['success' => false, 'message' => 'Không tìm thấy bất động sản'];
            }

            // Kiểm tra quyền sở hữu
            if ($property->user_id != $userId) {
                return ['success' => false, 'message' => 'Bạn không có quyền gia hạn bất động sản này'];
            }

            // Tính ngày hết hạn mới (14 ngày từ ngày yêu cầu)
            $requestedExpirationDate = date('Y-m-d H:i:s', strtotime('+14 days'));

            // Kiểm tra xem Database class có method beginTransaction không
            if (method_exists($this->db, 'beginTransaction')) {
                $this->db->beginTransaction();
            }

            // Tạo yêu cầu gia hạn
            $this->db->query('INSERT INTO extension_requests (property_id, user_id, requested_expiration_date, status)
                             VALUES (:property_id, :user_id, :requested_expiration_date, "pending")');
            $this->db->bind(':property_id', $propertyId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':requested_expiration_date', $requestedExpirationDate);

            if (!$this->db->execute()) {
                if (method_exists($this->db, 'rollback')) {
                    $this->db->rollback();
                }
                return ['success' => false, 'message' => 'Không thể tạo yêu cầu gia hạn'];
            }

            // Cập nhật trạng thái property
            $this->db->query('UPDATE properties SET extension_status = "pending" WHERE id = :id');
            $this->db->bind(':id', $propertyId);

            if (!$this->db->execute()) {
                if (method_exists($this->db, 'rollback')) {
                    $this->db->rollback();
                }
                return ['success' => false, 'message' => 'Không thể cập nhật trạng thái bất động sản'];
            }

            if (method_exists($this->db, 'commit')) {
                $this->db->commit();
            }

            return ['success' => true, 'message' => 'Yêu cầu gia hạn đã được gửi và đang chờ phê duyệt'];

        } catch (Exception $e) {
            if (method_exists($this->db, 'rollback')) {
                $this->db->rollback();
            }
            error_log('ExtensionRequest::createRequest error: ' . $e->getMessage());
            return ['success' => false, 'message' => 'Có lỗi xảy ra khi tạo yêu cầu gia hạn'];
        }
    }

    // Kiểm tra xem property có yêu cầu gia hạn pending không
    public function hasPendingRequest($propertyId) {
        $this->db->query('SELECT id FROM extension_requests WHERE property_id = :property_id AND status = "pending"');
        $this->db->bind(':property_id', $propertyId);
        return $this->db->single() !== false;
    }

    // Lấy yêu cầu gia hạn pending của property
    public function getPendingRequest($propertyId) {
        $this->db->query('SELECT * FROM extension_requests WHERE property_id = :property_id AND status = "pending"');
        $this->db->bind(':property_id', $propertyId);
        return $this->db->single();
    }

    // Lấy tất cả yêu cầu gia hạn (cho admin)
    public function getAllRequests($status = null) {
        $sql = 'SELECT er.*, p.title as property_title, p.slug as property_slug, 
                       u.fullname as user_name, u.email as user_email,
                       a.fullname as admin_name
                FROM extension_requests er
                LEFT JOIN properties p ON er.property_id = p.id
                LEFT JOIN users u ON er.user_id = u.id
                LEFT JOIN users a ON er.admin_id = a.id';
        
        if ($status) {
            $sql .= ' WHERE er.status = :status';
        }
        
        $sql .= ' ORDER BY er.created_at DESC';
        
        $this->db->query($sql);
        
        if ($status) {
            $this->db->bind(':status', $status);
        }
        
        return $this->db->resultSet();
    }

    // Phê duyệt yêu cầu gia hạn (admin)
    public function approveRequest($requestId, $adminId, $adminNote = null) {
        try {
            // Lấy thông tin yêu cầu
            $this->db->query('SELECT * FROM extension_requests WHERE id = :id AND status = "pending"');
            $this->db->bind(':id', $requestId);
            $request = $this->db->single();

            if (!$request) {
                return ['success' => false, 'message' => 'Không tìm thấy yêu cầu gia hạn'];
            }

            // Bắt đầu transaction
            $this->db->beginTransaction();

            // Cập nhật yêu cầu gia hạn
            $this->db->query('UPDATE extension_requests SET 
                             status = "approved", 
                             admin_id = :admin_id, 
                             admin_note = :admin_note, 
                             processed_at = NOW() 
                             WHERE id = :id');
            $this->db->bind(':id', $requestId);
            $this->db->bind(':admin_id', $adminId);
            $this->db->bind(':admin_note', $adminNote);
            
            if (!$this->db->execute()) {
                $this->db->rollback();
                return ['success' => false, 'message' => 'Không thể cập nhật yêu cầu gia hạn'];
            }

            // Cập nhật ngày hết hạn của property
            $this->db->query('UPDATE properties SET 
                             expiration_date = :expiration_date,
                             extension_status = "none",
                             updated_at = NOW()
                             WHERE id = :id');
            $this->db->bind(':id', $request->property_id);
            $this->db->bind(':expiration_date', $request->requested_expiration_date);
            
            if (!$this->db->execute()) {
                $this->db->rollback();
                return ['success' => false, 'message' => 'Không thể cập nhật ngày hết hạn'];
            }

            $this->db->commit();
            return ['success' => true, 'message' => 'Đã phê duyệt yêu cầu gia hạn thành công'];

        } catch (Exception $e) {
            $this->db->rollback();
            error_log('ExtensionRequest::approveRequest error: ' . $e->getMessage());
            return ['success' => false, 'message' => 'Có lỗi xảy ra khi phê duyệt yêu cầu'];
        }
    }

    // Từ chối yêu cầu gia hạn (admin)
    public function rejectRequest($requestId, $adminId, $adminNote = null) {
        try {
            // Lấy thông tin yêu cầu
            $this->db->query('SELECT * FROM extension_requests WHERE id = :id AND status = "pending"');
            $this->db->bind(':id', $requestId);
            $request = $this->db->single();

            if (!$request) {
                return ['success' => false, 'message' => 'Không tìm thấy yêu cầu gia hạn'];
            }

            // Bắt đầu transaction
            $this->db->beginTransaction();

            // Cập nhật yêu cầu gia hạn
            $this->db->query('UPDATE extension_requests SET 
                             status = "rejected", 
                             admin_id = :admin_id, 
                             admin_note = :admin_note, 
                             processed_at = NOW() 
                             WHERE id = :id');
            $this->db->bind(':id', $requestId);
            $this->db->bind(':admin_id', $adminId);
            $this->db->bind(':admin_note', $adminNote);
            
            if (!$this->db->execute()) {
                $this->db->rollback();
                return ['success' => false, 'message' => 'Không thể cập nhật yêu cầu gia hạn'];
            }

            // Reset trạng thái extension của property
            $this->db->query('UPDATE properties SET extension_status = "none" WHERE id = :id');
            $this->db->bind(':id', $request->property_id);
            
            if (!$this->db->execute()) {
                $this->db->rollback();
                return ['success' => false, 'message' => 'Không thể cập nhật trạng thái bất động sản'];
            }

            $this->db->commit();
            return ['success' => true, 'message' => 'Đã từ chối yêu cầu gia hạn'];

        } catch (Exception $e) {
            $this->db->rollback();
            error_log('ExtensionRequest::rejectRequest error: ' . $e->getMessage());
            return ['success' => false, 'message' => 'Có lỗi xảy ra khi từ chối yêu cầu'];
        }
    }

    // Lấy thống kê yêu cầu gia hạn
    public function getStats() {
        $this->db->query('SELECT 
                         COUNT(*) as total,
                         COUNT(CASE WHEN status = "pending" THEN 1 END) as pending,
                         COUNT(CASE WHEN status = "approved" THEN 1 END) as approved,
                         COUNT(CASE WHEN status = "rejected" THEN 1 END) as rejected
                         FROM extension_requests');
        return $this->db->single();
    }
}
?>
