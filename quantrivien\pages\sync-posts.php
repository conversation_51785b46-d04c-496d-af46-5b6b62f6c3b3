<?php
// <PERSON><PERSON><PERSON> tra quyền admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

require_once __DIR__ . '/../../app/models/UserPackage.php';

$userPackageModel = new UserPackage();
$message = '';
$messageType = '';

// Xử lý đồng bộ
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sync_all'])) {
    if ($userPackageModel->syncUserPackagePostCounts()) {
        $message = 'Đồng bộ số tin thành công cho tất cả người dùng!';
        $messageType = 'success';
    } else {
        $message = 'Có lỗi xảy ra khi đồng bộ số tin!';
        $messageType = 'danger';
    }
}

// Lấy danh sách user packages với thông tin thực tế
$userPackages = $userPackageModel->getAllUserPackages();

// <PERSON><PERSON><PERSON> toán thống kê
$totalUsers = count($userPackages);
$syncNeeded = 0;

foreach ($userPackages as $userPackage) {
    $actualPostsUsed = $userPackageModel->getActualPostsUsed($userPackage->user_id);
    if ($actualPostsUsed != $userPackage->posts_used) {
        $syncNeeded++;
    }
}
?>

<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mt-2">Đồng bộ số tin đăng</h1>
        <a href="index.php?page=packages" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>Quay lại
        </a>
    </div>

    <?php if ($message): ?>
    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
        <?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- Thống kê -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small">Tổng số user</div>
                            <div class="h3"><?php echo $totalUsers; ?></div>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small">Cần đồng bộ</div>
                            <div class="h3"><?php echo $syncNeeded; ?></div>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-exclamation-triangle fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small">Đã đồng bộ</div>
                            <div class="h3"><?php echo $totalUsers - $syncNeeded; ?></div>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Nút đồng bộ -->
    <?php if ($syncNeeded > 0): ?>
    <div class="card mb-4">
        <div class="card-body text-center">
            <h5 class="card-title">Có <?php echo $syncNeeded; ?> người dùng cần đồng bộ số tin</h5>
            <p class="card-text">Hệ thống phát hiện có sự khác biệt giữa số tin lưu trong database và số tin thực tế. Nhấn nút bên dưới để đồng bộ.</p>
            <form method="POST" style="display: inline;">
                <button type="submit" name="sync_all" class="btn btn-warning btn-lg"
                        onclick="return confirm('Bạn có chắc chắn muốn đồng bộ số tin cho tất cả <?php echo $totalUsers; ?> người dùng?')">
                    <i class="bi bi-arrow-repeat me-2"></i>Đồng bộ tất cả
                </button>
            </form>
        </div>
    </div>
    <?php endif; ?>

    <!-- Danh sách chi tiết -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Chi tiết số tin của từng người dùng</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>ID</th>
                            <th>Người dùng</th>
                            <th>Gói dịch vụ</th>
                            <th>Tin trong DB</th>
                            <th>Tin thực tế</th>
                            <th>Tin còn lại (DB)</th>
                            <th>Tin còn lại (thực tế)</th>
                            <th>Trạng thái</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($userPackages as $userPackage): ?>
                        <?php
                        $actualPostsUsed = $userPackageModel->getActualPostsUsed($userPackage->user_id);
                        $actualPostsRemaining = $userPackageModel->getUserPostsRemaining($userPackage->user_id);
                        $needSync = ($actualPostsUsed != $userPackage->posts_used);
                        ?>
                        <tr class="<?php echo $needSync ? 'table-warning' : ''; ?>">
                            <td><?php echo $userPackage->id; ?></td>
                            <td>
                                <strong><?php echo htmlspecialchars($userPackage->user_name); ?></strong>
                                <br><small class="text-muted"><?php echo htmlspecialchars($userPackage->user_email); ?></small>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo htmlspecialchars($userPackage->package_name); ?></span>
                                <br><small class="text-muted">Giới hạn: <?php echo $userPackage->package_post_limit; ?> tin</small>
                            </td>
                            <td>
                                <span class="badge bg-secondary"><?php echo $userPackage->posts_used; ?></span>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $needSync ? 'warning' : 'success'; ?>">
                                    <?php echo $actualPostsUsed; ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-secondary"><?php echo $userPackage->posts_remaining; ?></span>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $actualPostsRemaining > 0 ? 'success' : 'danger'; ?>">
                                    <?php echo $actualPostsRemaining; ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($needSync): ?>
                                <span class="badge bg-warning">Cần đồng bộ</span>
                                <?php else: ?>
                                <span class="badge bg-success">Đã đồng bộ</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.table-warning {
    --bs-table-bg: #fff3cd;
}
</style>
