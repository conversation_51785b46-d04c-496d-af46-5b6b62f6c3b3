<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Mobile Search - <PERSON>hu<PERSON> Nẵng</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .debug-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        
        .debug-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        
        .debug-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #f77316;
            padding-bottom: 10px;
        }
        
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .debug-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
        
        .debug-btn {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background: white;
            color: #495057;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .debug-btn:hover {
            background: #f8f9fa;
            border-color: #f77316;
            color: #f77316;
        }
        
        .debug-btn.primary {
            background: #f77316;
            border-color: #f77316;
            color: white;
        }
        
        .debug-btn.primary:hover {
            background: #e56b17;
            border-color: #e56b17;
        }
        
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-info { color: #17a2b8; }
        .status-warning { color: #ffc107; }
        
        .iframe-container {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            margin-top: 15px;
        }
        
        .iframe-container iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="debug-card">
            <div class="debug-title">🔍 Mobile Search Debug Console</div>
            
            <div class="debug-buttons">
                <button class="debug-btn primary" onclick="testMobileSearchSystem()">Test Mobile Search System</button>
                <button class="debug-btn" onclick="testDesktopAjaxIntegration()">Test Desktop AJAX Integration</button>
                <button class="debug-btn" onclick="testAPIEndpoints()">Test API Endpoints</button>
                <button class="debug-btn" onclick="simulateMobileSearch()">Simulate Mobile Search</button>
                <button class="debug-btn" onclick="clearLogs()">Clear Logs</button>
            </div>
            
            <div id="debugLog" class="debug-log">
[Debug Console Ready]
Click buttons above to run tests...
            </div>
        </div>
        
        <div class="debug-card">
            <div class="debug-title">📱 Live Mobile Search Test</div>
            <p class="text-muted">Test mobile search directly in the iframe below:</p>
            
            <div class="iframe-container">
                <iframe src="/thuenhadanang/search" id="searchFrame"></iframe>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let logContainer;
        
        document.addEventListener('DOMContentLoaded', function() {
            logContainer = document.getElementById('debugLog');
            log('🚀 Debug console initialized', 'success');
        });
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = `status-${type}`;
            const logEntry = `[${timestamp}] ${message}\n`;
            
            logContainer.innerHTML += `<span class="${className}">${logEntry}</span>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLogs() {
            logContainer.innerHTML = '[Debug Console Cleared]\n';
        }
        
        async function testMobileSearchSystem() {
            log('🔍 Testing Mobile Search System...', 'info');
            
            try {
                // Test if mobile search is available in iframe
                const iframe = document.getElementById('searchFrame');
                const iframeWindow = iframe.contentWindow;
                
                // Wait for iframe to load
                await new Promise(resolve => {
                    if (iframe.contentDocument.readyState === 'complete') {
                        resolve();
                    } else {
                        iframe.onload = resolve;
                    }
                });
                
                // Check mobile search in iframe
                if (iframeWindow.mobileSearch) {
                    log('✅ Mobile Search System found in iframe', 'success');
                    
                    // Test mobile search methods
                    const formData = iframeWindow.mobileSearch.getFormData();
                    log(`📊 Current form data: ${JSON.stringify(formData)}`, 'info');
                    
                } else {
                    log('❌ Mobile Search System not found in iframe', 'error');
                }
                
            } catch (error) {
                log(`❌ Error testing mobile search: ${error.message}`, 'error');
            }
        }
        
        async function testDesktopAjaxIntegration() {
            log('🖥️ Testing Desktop AJAX Integration...', 'info');
            
            try {
                const iframe = document.getElementById('searchFrame');
                const iframeWindow = iframe.contentWindow;
                
                // Wait for iframe to load
                await new Promise(resolve => {
                    if (iframe.contentDocument.readyState === 'complete') {
                        resolve();
                    } else {
                        iframe.onload = resolve;
                    }
                });
                
                // Check desktop AJAX in iframe
                if (iframeWindow.ajaxSearch) {
                    log('✅ Desktop AJAX System found in iframe', 'success');
                    
                    // Check methods
                    if (typeof iframeWindow.ajaxSearch.updateResults === 'function') {
                        log('✅ updateResults method available', 'success');
                    } else {
                        log('❌ updateResults method not available', 'error');
                    }
                    
                    if (typeof iframeWindow.ajaxSearch.updateUrl === 'function') {
                        log('✅ updateUrl method available', 'success');
                    } else {
                        log('❌ updateUrl method not available', 'error');
                    }
                    
                } else {
                    log('❌ Desktop AJAX System not found in iframe', 'error');
                }
                
            } catch (error) {
                log(`❌ Error testing desktop AJAX: ${error.message}`, 'error');
            }
        }
        
        async function testAPIEndpoints() {
            log('🌐 Testing API Endpoints...', 'info');
            
            const endpoints = [
                { name: 'Simple API', url: '/thuenhadanang/api/mobile-search-simple.php?keyword=test' },
                { name: 'Simple API Filters', url: '/thuenhadanang/api/mobile-search-simple.php?action=filters' },
                { name: 'Simple API Suggestions', url: '/thuenhadanang/api/mobile-search-simple.php?action=suggestions&q=an' },
                { name: 'Main API', url: '/thuenhadanang/api/mobile-search.php?keyword=test' },
                { name: 'Desktop API', url: '/thuenhadanang/api/search.php?keyword=test' }
            ];
            
            for (const endpoint of endpoints) {
                try {
                    log(`Testing ${endpoint.name}...`, 'info');
                    
                    const response = await fetch(endpoint.url + '&_t=' + Date.now());
                    
                    if (!response.ok) {
                        log(`❌ ${endpoint.name}: HTTP ${response.status}`, 'error');
                        continue;
                    }
                    
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        const text = await response.text();
                        log(`❌ ${endpoint.name}: Non-JSON response: ${text.substring(0, 100)}`, 'error');
                        continue;
                    }
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        const count = data.data ? data.data.count || 0 : 0;
                        log(`✅ ${endpoint.name}: Success (${count} results)`, 'success');
                    } else {
                        log(`❌ ${endpoint.name}: ${data.error.message}`, 'error');
                    }
                    
                } catch (error) {
                    log(`❌ ${endpoint.name}: ${error.message}`, 'error');
                }
            }
        }
        
        async function simulateMobileSearch() {
            log('📱 Simulating Mobile Search...', 'info');
            
            try {
                const iframe = document.getElementById('searchFrame');
                const iframeWindow = iframe.contentWindow;
                
                // Wait for iframe to load
                await new Promise(resolve => {
                    if (iframe.contentDocument.readyState === 'complete') {
                        resolve();
                    } else {
                        iframe.onload = resolve;
                    }
                });
                
                if (iframeWindow.mobileSearch) {
                    log('📱 Setting test search parameters...', 'info');
                    
                    // Set test values in mobile search form
                    const keywordInput = iframe.contentDocument.getElementById('mobileSearchKeyword');
                    if (keywordInput) {
                        keywordInput.value = 'test search';
                        log('✅ Keyword set to "test search"', 'success');
                    }
                    
                    // Trigger search
                    log('🔍 Triggering mobile search...', 'info');
                    await iframeWindow.mobileSearch.performAjaxSearch();
                    
                    log('✅ Mobile search triggered successfully', 'success');
                    
                } else {
                    log('❌ Mobile search not available for simulation', 'error');
                }
                
            } catch (error) {
                log(`❌ Error simulating mobile search: ${error.message}`, 'error');
            }
        }
        
        // Listen for messages from iframe
        window.addEventListener('message', function(event) {
            if (event.origin !== window.location.origin) return;
            
            if (event.data.type === 'mobileSearchLog') {
                log(`📱 ${event.data.message}`, event.data.level || 'info');
            }
        });
    </script>
</body>
</html>
