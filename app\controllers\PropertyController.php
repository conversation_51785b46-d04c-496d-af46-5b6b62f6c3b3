<?php
require_once BASE_PATH . '/app/models/Property.php';
require_once BASE_PATH . '/app/models/PropertyType.php';
require_once BASE_PATH . '/app/models/User.php';
require_once BASE_PATH . '/app/models/Ward.php';
require_once BASE_PATH . '/app/models/PropertyContact.php';

class PropertyController extends BaseController {
    private $propertyModel;
    private $propertyTypeModel;
    private $userModel;
    private $wardModel;
    private $propertyContactModel;

    public function __construct() {
        $this->propertyModel = new Property();
        $this->propertyTypeModel = new PropertyType();
        $this->userModel = new User();
        $this->wardModel = new Ward();
        $this->propertyContactModel = new PropertyContact();
    }

    public function detail($slug = '', $id = null) {
        if (empty($id)) {
            header('Location: /thuenhadanang');
            exit;
        }

        // Lấy thông tin chi tiết bất động sản từ database
        $property = $this->propertyModel->getPropertyById($id);

        if (!$property) {
            // Nếu không tìm thấy bất động sản, chuyển hướng về trang chủ
            header('Location: /thuenhadanang');
            exit;
        }

        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra trạng thái tin đăng và quyền xem
        $canView = true;
        $viewStatus = 'normal'; // normal, pending, hidden, expired
        $statusMessage = '';

        // Kiểm tra ngày hết hạn
        $isExpired = false;
        if ($property->expiration_date && strtotime($property->expiration_date) < time()) {
            $isExpired = true;
            $viewStatus = 'expired';
            $statusMessage = 'Tin đăng này đã hết hạn';
        }

        // Kiểm tra trạng thái tin đăng
        if ($property->active == 0) {
            // Tin đăng chưa được phê duyệt
            $viewStatus = 'pending';
            $statusMessage = 'Tin đăng đang chờ phê duyệt và chưa hiển thị công khai';

            // Chỉ cho phép người đăng tin xem được nội dung
            $canView = isset($_SESSION['user_id']) && $_SESSION['user_id'] == $property->user_id;

            if (!$canView) {
                header('Location: /thuenhadanang/cho-thue-nha-dat');
                exit;
            }
        } else if ($property->active == 2 || $property->status == 'hide') {
            // Tin đăng bị từ chối hoặc ẩn
            $viewStatus = 'hidden';
            $statusMessage = 'Tin đăng này hiện đang tạm ẩn';

            // Chỉ cho phép người đăng tin xem được nội dung
            $canView = isset($_SESSION['user_id']) && $_SESSION['user_id'] == $property->user_id;

            if (!$canView) {
                header('Location: /thuenhadanang/cho-thue-nha-dat');
                exit;
            }
        }

        // Tạo địa chỉ đầy đủ nếu chưa có
        if (empty($property->address) || $property->address === 'ABC') {
            $address_parts = [];

            if (!empty($property->street)) {
                $address_parts[] = $property->street;
            }

            if (!empty($property->ward_name)) {
                $address_parts[] = 'Phường ' . $property->ward_name;
            }

            if (!empty($property->city)) {
                $address_parts[] = $property->city;
            }

            $property->address = implode(', ', $address_parts);
        }

        // Lấy thông tin chủ sở hữu
        $owner = null;
        $contactInfo = null;

        // Kiểm tra xem có phải tin đăng free không (user_id = 1 là tài khoản Guest/Admin)
        $isFreeProperty = ($property->user_id == 1);

        if ($isFreeProperty) {
            // Nếu là tin đăng free, lấy thông tin liên hệ từ bảng property_contacts
            $contactInfo = $this->propertyContactModel->getContactByPropertyId($property->id);
        } else if ($property->user_id) {
            // Nếu không phải tin đăng free, lấy thông tin chủ sở hữu
            $owner = $this->userModel->getUserById($property->user_id);
        }

        // Lấy danh sách hình ảnh
        $images = [];
        if (!empty($property->images)) {
            $imagesArray = json_decode($property->images);
            if (is_array($imagesArray)) {
                foreach ($imagesArray as $image) {
                    $images[] = '/thuenhadanang/public/uploads/properties/' . $image;
                }
            }
        }

        // Nếu không có hình ảnh, sử dụng hình ảnh mặc định
        if (empty($images) && !empty($property->main_image)) {
            $images[] = '/thuenhadanang/public/uploads/properties/' . $property->main_image;
        }

        // Nếu vẫn không có hình ảnh, sử dụng hình ảnh mặc định
        if (empty($images)) {
            $images[] = '/thuenhadanang/public/uploads/properties/default-property.jpg';
        }

        // Định dạng giá
        $formattedPrice = '';
        $priceInMillions = $property->price / 1000000;

        if ($priceInMillions >= 1) {
            // Sử dụng abs() để tránh lỗi với số âm và round() để làm tròn
            $remainder = abs(round($priceInMillions * 10) % 10);
            $formattedPrice = $remainder === 0
                ? (int)$priceInMillions . ' triệu'
                : number_format($priceInMillions, 1) . ' triệu';
        } else {
            $formattedPrice = number_format($property->price, 0, ',', '.') . ' đồng';
        }

        // Thêm đơn vị thời gian
        switch ($property->price_period) {
            case 'day':
                $formattedPrice .= '/ngày';
                break;
            case 'week':
                $formattedPrice .= '/tuần';
                break;
            case 'month':
                $formattedPrice .= '/tháng';
                break;
            case 'quarter':
                $formattedPrice .= '/quý';
                break;
            case 'year':
                $formattedPrice .= '/năm';
                break;
        }

        // Chuyển đổi hướng từ tiếng Anh sang tiếng Việt
        if (!empty($property->direction)) {
            $directions = [
                'north' => 'Bắc',
                'northeast' => 'Đông Bắc',
                'east' => 'Đông',
                'southeast' => 'Đông Nam',
                'south' => 'Nam',
                'southwest' => 'Tây Nam',
                'west' => 'Tây',
                'northwest' => 'Tây Bắc',
                'dong' => 'Đông',
                'tay' => 'Tây',
                'nam' => 'Nam',
                'bac' => 'Bắc',
                'dong-bac' => 'Đông Bắc',
                'dong-nam' => 'Đông Nam',
                'tay-bac' => 'Tây Bắc',
                'tay-nam' => 'Tây Nam'
            ];

            $direction_key = strtolower(trim($property->direction));
            if (isset($directions[$direction_key])) {
                $property->direction = $directions[$direction_key];
            }
        }

        // Lấy các bất động sản tương tự
        $similarProperties = $this->propertyModel->getSimilarProperties($property->type_id, $property->id, 4);

        // Hàm định dạng số điện thoại
        function formatPhoneNumber($phoneNumber) {
            // Loại bỏ tất cả các ký tự không phải số
            $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);

            // Nếu số điện thoại có 10 chữ số (0xxx.xxx.xxx)
            if (strlen($phoneNumber) === 10) {
                return substr($phoneNumber, 0, 4) . '.' . substr($phoneNumber, 4, 3) . '.' . substr($phoneNumber, 7);
            }

            // Nếu số điện thoại có 11 chữ số (0xxxx.xxx.xxx)
            if (strlen($phoneNumber) === 11) {
                return substr($phoneNumber, 0, 5) . '.' . substr($phoneNumber, 5, 3) . '.' . substr($phoneNumber, 8);
            }

            // Trả về số điện thoại gốc nếu không khớp với định dạng nào
            return $phoneNumber;
        }

        // Chuẩn bị dữ liệu để truyền vào view
        $data = [
            'title' => $property->title . ' - Thuê Nhà Đà Nẵng',
            'view' => 'property-detail',
            'property' => $property,
            'owner' => $owner,
            'contactInfo' => $contactInfo,
            'isFreeProperty' => $isFreeProperty,
            'images' => $images,
            'formattedPrice' => $formattedPrice,
            'similarProperties' => $similarProperties,
            'formatPhoneNumber' => 'formatPhoneNumber',
            'viewStatus' => $viewStatus,
            'statusMessage' => $statusMessage,
            'isExpired' => $isExpired,
            'canView' => $canView
        ];

        $this->view('property-detail', $data);
    }
}