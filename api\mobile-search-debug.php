<?php
/**
 * Mobile Search Debug API
 * Simple debug endpoint to test mobile search
 */

// Disable error display for clean JSON
ini_set('display_errors', 0);
error_reporting(0);

// Set JSON header first
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Start output buffering to catch any unwanted output
ob_start();

try {
    // Log to file instead of output
    error_log('Mobile Search Debug API called: ' . $_SERVER['REQUEST_URI']);
    
    // Simple test response
    $response = [
        'success' => true,
        'message' => 'Mobile Search Debug API working',
        'data' => [
            'properties' => [],
            'count' => 0,
            'metadata' => [
                'title' => 'Debug Search Results',
                'selectedFilters' => $_GET,
                'url' => '/thuenhadanang/search'
            ]
        ],
        'debug' => [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $_SERVER['REQUEST_METHOD'],
            'params' => $_GET,
            'mobile' => true
        ]
    ];
    
    // Clean any unwanted output
    ob_clean();
    
    // Output JSON
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    // Clean any unwanted output
    ob_clean();
    
    // Log error
    error_log('Mobile Search Debug Error: ' . $e->getMessage());
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => [
            'message' => 'Debug API error',
            'details' => $e->getMessage(),
            'mobile' => true
        ],
        'timestamp' => time()
    ]);
}

// End output buffering
ob_end_flush();
?>
