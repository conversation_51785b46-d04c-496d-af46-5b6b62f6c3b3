<?php
require_once 'app/models/Property.php';
require_once 'app/models/PropertyType.php';
require_once 'app/models/Ward.php';
require_once 'app/models/User.php';
require_once 'app/libraries/Database.php';

class PropertyListingController {
    private $propertyModel;
    private $propertyTypeModel;
    private $wardModel;
    private $userModel;
    private $db;

    public function __construct() {
        $this->propertyModel = new Property();
        $this->propertyTypeModel = new PropertyType();
        $this->wardModel = new Ward();
        $this->userModel = new User();
    }

    public function index() {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập
        if (!isset($_SESSION['user_id'])) {
            header('Location: /thuenhadanang/login');
            exit;
        }

        // Lấy thông tin user
        $user = $this->userModel->getUserById($_SESSION['user_id']);

        // Lấy danh sách loại hình bất động sản
        $propertyTypes = $this->propertyTypeModel->getAllPropertyTypes();

        // Lấy danh sách phường/xã
        $wards = $this->wardModel->getAllWards();

        // Lấy danh sách hướng
        $directions = $this->propertyModel->getAllDirections();

        // Thiết lập tiêu đề trang
        $title = 'Đăng tin bất động sản - Thuê Nhà Đà Nẵng';

        // Thiết lập view và data
        $view = 'property-listing/step1';
        $data = [
            'user' => $user,
            'propertyTypes' => $propertyTypes,
            'wards' => $wards,
            'directions' => $directions,
            'error' => $_SESSION['property_listing_error'] ?? '',
            'success' => $_SESSION['property_listing_success'] ?? '',
            'formData' => $_SESSION['property_listing_data'] ?? []
        ];

        // Xóa thông báo trong session
        unset($_SESSION['property_listing_error']);
        unset($_SESSION['property_listing_success']);

        // Render layout với view
        require 'app/views/layout.php';
    }

    public function step1() {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập
        if (!isset($_SESSION['user_id'])) {
            header('Location: /thuenhadanang/login');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate dữ liệu
            $errors = [];

            if (empty($_POST['type_id'])) {
                $errors[] = 'Vui lòng chọn loại bất động sản';
            }

            if (empty($_POST['ward_id'])) {
                $errors[] = 'Vui lòng chọn phường/xã';
            }

            if (empty($_POST['street'])) {
                $errors[] = 'Vui lòng nhập tên đường';
            }

            if (empty($_POST['address'])) {
                $errors[] = 'Vui lòng nhập địa chỉ đầy đủ';
            }

            if (empty($_POST['price']) || !is_numeric($_POST['price'])) {
                $errors[] = 'Vui lòng nhập giá thuê hợp lệ';
            }

            if (empty($_POST['area']) || !is_numeric($_POST['area'])) {
                $errors[] = 'Vui lòng nhập diện tích hợp lệ';
            }

            if (empty($_POST['title'])) {
                $errors[] = 'Vui lòng nhập tiêu đề';
            }

            if (empty($_POST['description'])) {
                $errors[] = 'Vui lòng nhập mô tả';
            }

            // Nếu có lỗi
            if (!empty($errors)) {
                $_SESSION['property_listing_error'] = implode('<br>', $errors);
                $_SESSION['property_listing_data'] = $_POST;
                header('Location: /thuenhadanang/property-listing');
                exit;
            }

            // Lưu dữ liệu vào session
            $_SESSION['property_listing_data'] = $_POST;

            // Chuyển đến bước 2
            header('Location: /thuenhadanang/property-listing/step2');
            exit;
        }

        // Nếu không phải POST request, chuyển về trang đăng tin
        header('Location: /thuenhadanang/property-listing');
        exit;
    }

    public function step2() {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập
        if (!isset($_SESSION['user_id'])) {
            header('Location: /thuenhadanang/login');
            exit;
        }

        // Kiểm tra dữ liệu bước 1
        if (empty($_SESSION['property_listing_data'])) {
            header('Location: /thuenhadanang/property-listing');
            exit;
        }

        // Lấy thông tin user
        $user = $this->userModel->getUserById($_SESSION['user_id']);

        // Thiết lập tiêu đề trang
        $title = 'Đăng tin bất động sản - Bước 2 - Thuê Nhà Đà Nẵng';

        // Thiết lập view và data
        $view = 'property-listing/step2';
        $data = [
            'user' => $user,
            'error' => $_SESSION['property_listing_error'] ?? '',
            'success' => $_SESSION['property_listing_success'] ?? '',
            'formData' => $_SESSION['property_listing_data'] ?? []
        ];

        // Xóa thông báo trong session
        unset($_SESSION['property_listing_error']);
        unset($_SESSION['property_listing_success']);

        // Render layout với view
        require 'app/views/layout.php';
    }

    public function saveProperty() {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập
        if (!isset($_SESSION['user_id'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Bạn cần đăng nhập để thực hiện chức năng này']);
            exit;
        }

        // Kiểm tra quyền đăng tin dựa trên gói dịch vụ
        require_once __DIR__ . '/../models/UserPackage.php';
        $userPackageModel = new UserPackage();

        if (!$userPackageModel->canUserPost($_SESSION['user_id'])) {
            $userPackageInfo = $userPackageModel->getUserPackageWithRealCounts($_SESSION['user_id']);
            $message = 'Bạn đã hết lượt đăng tin';

            if (!$userPackageInfo) {
                $message = 'Bạn chưa có gói dịch vụ. Vui lòng liên hệ admin để được hỗ trợ.';
            } else {
                // Lấy thông tin package
                require_once __DIR__ . '/../models/Package.php';
                $packageModel = new Package();
                $package = $packageModel->getPackageById($userPackageInfo->package_id);

                if ($package) {
                    if ($package->price == 0) {
                        $message = "Bạn đã sử dụng hết {$userPackageInfo->actual_posts_used}/{$package->post_limit} tin đăng miễn phí trong gói {$package->name}. Hãy nâng cấp lên gói Chuyên nghiệp để có thêm nhiều tin đăng.";
                    } else {
                        $message = "Bạn đã sử dụng hết {$userPackageInfo->actual_posts_used}/{$package->post_limit} tin đăng trong gói {$package->name}. Vui lòng nâng cấp gói để tiếp tục đăng tin.";
                    }
                }
            }

            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => $message]);
            exit;
        }

        // Kiểm tra dữ liệu bước 1
        if (empty($_SESSION['property_listing_data'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Không tìm thấy dữ liệu bất động sản']);
            exit;
        }

        // Lấy dữ liệu từ session
        $formData = $_SESSION['property_listing_data'];

        // Xử lý dữ liệu hình ảnh từ request
        $tempImages = [];
        $tempMainImage = null;

        if (isset($_POST['images'])) {
            $tempImages = json_decode($_POST['images'], true);
            if (!is_array($tempImages)) {
                $tempImages = [];
            }
        }

        if (isset($_POST['main_image'])) {
            $tempMainImage = $_POST['main_image'];
        } elseif (!empty($tempImages)) {
            $tempMainImage = $tempImages[0]; // Lấy ảnh đầu tiên làm ảnh chính nếu không chọn
        }

        // Tạo slug từ tiêu đề
        $slug = $this->propertyModel->createSlug($formData['title']);

        // Tạo bản ghi bất động sản trước để lấy ID
        $propertyData = [
            'user_id' => $_SESSION['user_id'],
            'title' => $formData['title'],
            'slug' => $slug,
            'description' => $formData['description'],
            'type_id' => $formData['type_id'],
            'address' => $formData['address'],
            'ward_id' => $formData['ward_id'],
            'street' => $formData['street'],
            'city' => 'Đà Nẵng',
            'area' => $formData['area'],
            'price' => $formData['price'],
            'price_period' => $formData['price_period'],
            'bedrooms' => $formData['bedrooms'] ?? null,
            'bathrooms' => $formData['bathrooms'] ?? null,
            'direction' => $formData['direction'] ?? null,
            'video_url' => $formData['video_url'] ?? null,
            'images' => '[]', // Tạm thời để trống, sẽ cập nhật sau
            'main_image' => '', // Tạm thời để trống, sẽ cập nhật sau
            'status' => 'hide', // Trạng thái ẩn
            'featured' => 0,
            'active' => 0 // 0 = Chưa kích hoạt (đang chờ duyệt)
        ];

        // Lưu dữ liệu vào database
        $propertyId = $this->propertyModel->create($propertyData);

        if ($propertyId) {
            // Định nghĩa các thư mục
            $tempUploadDir = __DIR__ . '/../../public/uploads/temp/';
            $uploadDir = __DIR__ . '/../../public/uploads/properties/';

            // Tạo thư mục nếu chưa tồn tại
            if (!file_exists($uploadDir)) {
                if (!mkdir($uploadDir, 0777, true)) {
                    error_log('Failed to create upload directory: ' . $uploadDir);
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => 'Có lỗi xảy ra khi tạo thư mục lưu trữ hình ảnh']);
                    exit;
                } else {
                    // Đảm bảo quyền ghi
                    chmod($uploadDir, 0777);
                }
            }

            // Di chuyển hình ảnh từ thư mục tạm sang thư mục chính thức
            $propertyImages = [];
            $mainImage = '';

            foreach ($tempImages as $tempImage) {
                $tempFilePath = $tempUploadDir . $tempImage;

                // Kiểm tra file tồn tại
                if (file_exists($tempFilePath)) {
                    // Tạo tên file mới
                    $newFileName = 'property_' . $propertyId . '_' . time() . '_' . mt_rand(1000, 9999) . '.jpg';
                    $targetFile = $uploadDir . $newFileName;

                    // Di chuyển file từ thư mục tạm sang thư mục chính
                    if (copy($tempFilePath, $targetFile)) {
                        // Ghi log để debug
                        error_log('File copied successfully from ' . $tempFilePath . ' to ' . $targetFile);

                        // Xóa file tạm sau khi copy thành công
                        if (@unlink($tempFilePath)) {
                            error_log('Temp file deleted: ' . $tempFilePath);
                        } else {
                            error_log('Failed to delete temp file: ' . $tempFilePath);
                        }

                        // Thêm vào mảng hình ảnh
                        $propertyImages[] = $newFileName;

                        // Đặt ảnh chính nếu đây là ảnh chính đã chọn
                        if ($tempImage === $tempMainImage) {
                            $mainImage = $newFileName;
                        }
                    } else {
                        error_log('Failed to copy file from ' . $tempFilePath . ' to ' . $targetFile);
                    }
                } else {
                    error_log('Temp file not found: ' . $tempFilePath);
                }
            }

            // Nếu không có ảnh chính được đặt (có thể do lỗi), lấy ảnh đầu tiên làm ảnh chính
            if (empty($mainImage) && !empty($propertyImages)) {
                $mainImage = $propertyImages[0];
            }

            // Cập nhật thông tin hình ảnh trong database
            $this->db = new Database();
            $this->db->query('UPDATE properties SET images = :images, main_image = :main_image WHERE id = :id');
            $this->db->bind(':id', $propertyId);
            $this->db->bind(':images', json_encode($propertyImages));
            $this->db->bind(':main_image', $mainImage);

            if ($this->db->execute()) {
                error_log('Property images updated successfully for ID: ' . $propertyId);

                // Không cần cập nhật user_packages nữa vì chúng ta đếm tin thực tế
                // Chỉ ghi log để theo dõi
                error_log('Property created successfully for user ID: ' . $_SESSION['user_id'] . ', Property ID: ' . $propertyId);
            } else {
                error_log('Failed to update property images for ID: ' . $propertyId);
            }

            // Xóa dữ liệu trong session
            unset($_SESSION['property_listing_data']);

            // Trả về kết quả thành công
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'Đăng tin thành công! Tin của bạn đang chờ phê duyệt.',
                'property_id' => $propertyId
            ]);
        } else {
            // Trả về kết quả lỗi
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Có lỗi xảy ra khi đăng tin']);
        }
        exit;
    }

    public function uploadImage() {
        // Bắt đầu output buffering
        ob_start();

        // Tắt hiển thị lỗi để tránh HTML output
        ini_set('display_errors', 0);
        error_reporting(0);

        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập
        if (!isset($_SESSION['user_id'])) {
            ob_clean();
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Unauthorized']);
            exit;
        }

        // Định nghĩa các thư mục
        $tempUploadDir = __DIR__ . '/../../public/uploads/temp/';

        // Tạo thư mục nếu chưa tồn tại
        if (!file_exists($tempUploadDir)) {
            if (!mkdir($tempUploadDir, 0777, true)) {
                error_log('Failed to create temp directory: ' . $tempUploadDir);
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Failed to create temp directory']);
                exit;
            } else {
                // Đảm bảo quyền ghi
                chmod($tempUploadDir, 0777);
            }
        }

        // Xử lý upload file
        try {
            // Log để debug
            error_log('PropertyListingController::uploadImage - Starting upload process');
            error_log('PropertyListingController::uploadImage - $_FILES: ' . print_r($_FILES, true));

            // Kiểm tra file upload
            if (!isset($_FILES['file']) || $_FILES['file']['error'] != 0) {
                $error = isset($_FILES['file']) ? 'Error code: ' . $_FILES['file']['error'] : 'No file uploaded';
                error_log('PropertyListingController::uploadImage - Upload error: ' . $error);
                ob_clean();
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'No file uploaded or upload error: ' . $error]);
                exit;
            }

            $file = $_FILES['file'];
            $fileName = $file['name'];
            $fileTmpName = $file['tmp_name'];
            $fileSize = $file['size'];
            $fileType = $file['type'];

            // Kiểm tra loại file
            if (!in_array($fileType, ['image/jpeg', 'image/jpg', 'image/png'])) {
                ob_clean();
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Invalid file type: ' . $fileType]);
                exit;
            }

            // Kiểm tra kích thước file - Đã loại bỏ giới hạn kích thước vì chúng ta đã nén ở phía client
            // Tuy nhiên, vẫn giữ một giới hạn hợp lý để tránh tấn công (20MB)
            if ($fileSize > 20971520) { // 20MB = 20 * 1024 * 1024
                ob_clean();
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'File quá lớn: ' . ($fileSize / 1048576) . 'MB. Kích thước tối đa là 20MB']);
                exit;
            }

            // Kiểm tra kích thước hình ảnh
            $imageInfo = getimagesize($fileTmpName);
            if ($imageInfo[0] < 300 || $imageInfo[1] < 300) {
                ob_clean();
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Image dimensions too small: ' . $imageInfo[0] . 'x' . $imageInfo[1]]);
                exit;
            }

            // Kiểm tra quyền ghi vào thư mục
            if (!is_writable($tempUploadDir)) {
                ob_clean();
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Temp directory is not writable: ' . $tempUploadDir]);
                exit;
            }

            // Tạo tên file mới
            $newFileName = 'temp_' . time() . '_' . mt_rand(1000, 9999) . '.jpg';
            $targetFile = $tempUploadDir . $newFileName;

            // Upload file
            error_log('PropertyListingController::uploadImage - Attempting to move file from: ' . $fileTmpName . ' to: ' . $targetFile);
            error_log('PropertyListingController::uploadImage - is_uploaded_file: ' . (is_uploaded_file($fileTmpName) ? 'true' : 'false'));
            error_log('PropertyListingController::uploadImage - file_exists(tmp): ' . (file_exists($fileTmpName) ? 'true' : 'false'));
            error_log('PropertyListingController::uploadImage - is_writable(target_dir): ' . (is_writable(dirname($targetFile)) ? 'true' : 'false'));

            if (move_uploaded_file($fileTmpName, $targetFile)) {
                error_log('PropertyListingController::uploadImage - File uploaded successfully: ' . $newFileName);
                ob_clean();
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'filename' => $newFileName]);
            } else {
                $lastError = error_get_last();
                $errorMsg = $lastError ? $lastError['message'] : 'Unknown error';
                error_log('PropertyListingController::uploadImage - Failed to upload file: ' . $errorMsg);
                error_log('PropertyListingController::uploadImage - PHP last error: ' . print_r($lastError, true));
                ob_clean();
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Failed to upload file: ' . $errorMsg]);
            }
        } catch (Exception $e) {
            ob_clean();
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Exception: ' . $e->getMessage()]);
        }
        exit;
    }

    public function removeImage() {
        // Bắt đầu output buffering
        ob_start();

        // Tắt hiển thị lỗi
        ini_set('display_errors', 0);
        error_reporting(0);

        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập
        if (!isset($_SESSION['user_id'])) {
            ob_clean();
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Unauthorized']);
            exit;
        }

        // Định nghĩa thư mục
        $tempUploadDir = __DIR__ . '/../../public/uploads/temp/';

        // Xử lý xóa file
        try {
            // Kiểm tra tên file
            if (!isset($_POST['filename'])) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'No filename provided']);
                exit;
            }

            $filename = $_POST['filename'];

            // Kiểm tra tên file hợp lệ (chỉ cho phép xóa file trong thư mục temp)
            if (!preg_match('/^temp_\d+_\d+\.jpg$/', $filename)) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Invalid filename: ' . $filename]);
                exit;
            }

            $filePath = $tempUploadDir . $filename;

            // Kiểm tra xem file có tồn tại không
            if (!file_exists($filePath)) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'File not found, already deleted']);
                exit;
            }

            // Xóa file
            if (unlink($filePath)) {
                header('Content-Type: application/json');
                echo json_encode(['success' => true]);
            } else {
                $lastError = error_get_last();
                $errorMsg = $lastError ? $lastError['message'] : 'Unknown error';
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'error' => 'Failed to delete file: ' . $errorMsg]);
            }
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Exception: ' . $e->getMessage()]);
        }
        exit;
    }
}
