<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="container mt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/thuenhadanang" class="text-decoration-none">Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Đ<PERSON>ng nhập</li>
    </ol>
</nav>

<!-- Login Form -->
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card border-0 shadow">
                <div class="card-body p-4">
                    <!-- Logo và tiêu đề -->
                    <div class="text-center mb-4">
                        <h1 class="h3 text-primary fw-bold mb-2">Đăng nhập</h1>
                        <p class="text-secondary mb-4">Chào mừng bạn đến vớ<PERSON> Nẵng</p>
                    </div>

                    <!-- Hi<PERSON><PERSON> thị thông báo lỗi nếu có -->
                    <?php if (!empty($data['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $data['error']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endif; ?>

                    <!-- Form đăng nhập -->
                    <form action="/thuenhadanang/login/auth" method="POST">
                        <div class="mb-3">
                            <div class="form-floating">
                                <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                                <label for="email">
                                    <i class="bi bi-envelope me-2"></i>
                                    Email
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-floating">
                                <input type="password" class="form-control" id="password" name="password" placeholder="••••••••" required>
                                <label for="password">
                                    <i class="bi bi-lock me-2"></i>
                                    Mật khẩu
                                </label>
                                <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y text-decoration-none pe-3" id="togglePassword">
                                    <i class="bi bi-eye text-muted"></i>
                                </button>
                            </div>
                            <div class="d-flex justify-content-end mt-2">
                                <a href="/thuenhadanang/forgot-password" class="text-decoration-none small text-primary">Quên mật khẩu?</a>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label text-secondary" for="remember">Ghi nhớ đăng nhập</label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">Đăng nhập</button>

                        <div class="text-center">
                            <p class="text-secondary mb-3">Hoặc đăng nhập với</p>
                            <div class="d-flex gap-2 justify-content-center mb-4">
                                <button type="button" class="btn btn-outline-secondary flex-grow-1 d-flex align-items-center justify-content-center">
                                    <i class="bi bi-google me-2"></i>Google
                                </button>
                                <button type="button" class="btn btn-outline-secondary flex-grow-1 d-flex align-items-center justify-content-center">
                                    <i class="bi bi-facebook me-2"></i>Facebook
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Đăng ký -->
                    <div class="text-center">
                        <p class="mb-0 text-secondary">Chưa có tài khoản? 
                            <a href="/thuenhadanang/register" class="text-decoration-none text-primary">Đăng ký ngay</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Script để toggle password visibility -->
<script>
document.getElementById('togglePassword').addEventListener('click', function() {
    const password = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (password.type === 'password') {
        password.type = 'text';
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
    } else {
        password.type = 'password';
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
    }
});</script> 