/**
 * AJAX Search Debug Script
 * Provides debugging tools for AJAX search functionality
 */

class AjaxSearchDebugger {
    constructor() {
        this.isEnabled = true;
        this.logs = [];
        this.init();
    }

    init() {
        if (!this.isEnabled) return;
        
        console.log('🔧 AJAX Search Debugger initialized');
        this.createDebugPanel();
        this.monitorAjaxRequests();
        this.monitorFormEvents();
        this.addKeyboardShortcuts();
    }

    log(type, message, data = null) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = {
            timestamp,
            type,
            message,
            data
        };
        
        this.logs.push(logEntry);
        
        // Console output with colors
        const colors = {
            info: '#2196F3',
            success: '#4CAF50',
            warning: '#FF9800',
            error: '#F44336',
            ajax: '#9C27B0'
        };
        
        console.log(
            `%c[${timestamp}] AJAX Debug - ${type.toUpperCase()}: ${message}`,
            `color: ${colors[type] || '#333'}; font-weight: bold;`,
            data || ''
        );
        
        this.updateDebugPanel();
    }

    createDebugPanel() {
        // Create debug panel HTML
        const debugPanel = document.createElement('div');
        debugPanel.id = 'ajax-debug-panel';
        debugPanel.innerHTML = `
            <div class="debug-header">
                <h4>🔧 AJAX Search Debug</h4>
                <div class="debug-controls">
                    <button onclick="ajaxDebugger.clearLogs()">Clear</button>
                    <button onclick="ajaxDebugger.exportLogs()">Export</button>
                    <button onclick="ajaxDebugger.togglePanel()">Hide</button>
                </div>
            </div>
            <div class="debug-content">
                <div class="debug-stats">
                    <span class="stat">Requests: <span id="debug-request-count">0</span></span>
                    <span class="stat">Errors: <span id="debug-error-count">0</span></span>
                    <span class="stat">Avg Time: <span id="debug-avg-time">0ms</span></span>
                </div>
                <div class="debug-logs" id="debug-logs"></div>
            </div>
        `;

        // Add CSS
        const debugStyles = document.createElement('style');
        debugStyles.textContent = `
            #ajax-debug-panel {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 400px;
                max-height: 500px;
                background: white;
                border: 2px solid #ddd;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                z-index: 10000;
                font-family: monospace;
                font-size: 12px;
                display: none;
            }
            
            .debug-header {
                background: #f8f9fa;
                padding: 10px;
                border-bottom: 1px solid #ddd;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .debug-header h4 {
                margin: 0;
                font-size: 14px;
            }
            
            .debug-controls button {
                margin-left: 5px;
                padding: 2px 8px;
                font-size: 11px;
                border: 1px solid #ccc;
                background: white;
                border-radius: 3px;
                cursor: pointer;
            }
            
            .debug-controls button:hover {
                background: #f0f0f0;
            }
            
            .debug-content {
                padding: 10px;
                max-height: 400px;
                overflow-y: auto;
            }
            
            .debug-stats {
                display: flex;
                gap: 15px;
                margin-bottom: 10px;
                padding-bottom: 10px;
                border-bottom: 1px solid #eee;
            }
            
            .stat {
                font-weight: bold;
                color: #666;
            }
            
            .debug-logs {
                max-height: 300px;
                overflow-y: auto;
            }
            
            .debug-log-entry {
                padding: 5px;
                margin-bottom: 5px;
                border-left: 3px solid #ddd;
                background: #f9f9f9;
                border-radius: 3px;
            }
            
            .debug-log-entry.info { border-left-color: #2196F3; }
            .debug-log-entry.success { border-left-color: #4CAF50; }
            .debug-log-entry.warning { border-left-color: #FF9800; }
            .debug-log-entry.error { border-left-color: #F44336; }
            .debug-log-entry.ajax { border-left-color: #9C27B0; }
            
            .debug-log-time {
                color: #666;
                font-size: 10px;
            }
            
            .debug-log-data {
                background: #f0f0f0;
                padding: 5px;
                margin-top: 5px;
                border-radius: 3px;
                font-size: 10px;
                white-space: pre-wrap;
                max-height: 100px;
                overflow-y: auto;
            }
        `;
        
        document.head.appendChild(debugStyles);
        document.body.appendChild(debugPanel);
        
        this.debugPanel = debugPanel;
    }

    updateDebugPanel() {
        if (!this.debugPanel) return;
        
        const logsContainer = document.getElementById('debug-logs');
        const requestCount = document.getElementById('debug-request-count');
        const errorCount = document.getElementById('debug-error-count');
        const avgTime = document.getElementById('debug-avg-time');
        
        // Update stats
        const ajaxLogs = this.logs.filter(log => log.type === 'ajax');
        const errorLogs = this.logs.filter(log => log.type === 'error');
        
        if (requestCount) requestCount.textContent = ajaxLogs.length;
        if (errorCount) errorCount.textContent = errorLogs.length;
        
        // Calculate average response time
        const responseTimes = ajaxLogs
            .filter(log => log.data && log.data.responseTime)
            .map(log => log.data.responseTime);
        
        if (responseTimes.length > 0 && avgTime) {
            const avg = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
            avgTime.textContent = Math.round(avg) + 'ms';
        }
        
        // Update logs
        if (logsContainer) {
            logsContainer.innerHTML = this.logs.slice(-20).map(log => `
                <div class="debug-log-entry ${log.type}">
                    <div class="debug-log-time">${log.timestamp}</div>
                    <div class="debug-log-message">${log.message}</div>
                    ${log.data ? `<div class="debug-log-data">${JSON.stringify(log.data, null, 2)}</div>` : ''}
                </div>
            `).join('');
            
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
    }

    monitorAjaxRequests() {
        // Override fetch to monitor AJAX requests
        const originalFetch = window.fetch;
        
        window.fetch = async (...args) => {
            const startTime = Date.now();
            const url = args[0];
            
            // Only monitor our API requests
            if (url.includes('/api/search')) {
                this.log('ajax', `Request started: ${url}`);
            }
            
            try {
                const response = await originalFetch(...args);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (url.includes('/api/search')) {
                    this.log('ajax', `Request completed: ${response.status}`, {
                        url,
                        status: response.status,
                        responseTime
                    });
                }
                
                return response;
            } catch (error) {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (url.includes('/api/search')) {
                    this.log('error', `Request failed: ${error.message}`, {
                        url,
                        error: error.message,
                        responseTime
                    });
                }
                
                throw error;
            }
        };
    }

    monitorFormEvents() {
        // Monitor form events
        document.addEventListener('submit', (e) => {
            if (e.target.id === 'searchPageForm') {
                this.log('info', 'Form submitted', {
                    formId: e.target.id,
                    preventDefault: e.defaultPrevented
                });
            }
        });
        
        // Monitor input changes
        document.addEventListener('change', (e) => {
            if (e.target.closest('#searchPageForm')) {
                this.log('info', `Form field changed: ${e.target.name || e.target.id}`, {
                    field: e.target.name || e.target.id,
                    value: e.target.value
                });
            }
        });
    }

    addKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+Shift+D to toggle debug panel
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                this.togglePanel();
            }
            
            // Ctrl+Shift+C to clear logs
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                this.clearLogs();
            }
        });
    }

    togglePanel() {
        if (!this.debugPanel) return;
        
        const isVisible = this.debugPanel.style.display !== 'none';
        this.debugPanel.style.display = isVisible ? 'none' : 'block';
        
        this.log('info', isVisible ? 'Debug panel hidden' : 'Debug panel shown');
    }

    clearLogs() {
        this.logs = [];
        this.updateDebugPanel();
        console.clear();
        this.log('info', 'Debug logs cleared');
    }

    exportLogs() {
        const data = JSON.stringify(this.logs, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `ajax-debug-logs-${new Date().toISOString().slice(0, 19)}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        this.log('info', 'Debug logs exported');
    }

    // Public methods for manual testing
    testApiEndpoint(params = {}) {
        const url = '/thuenhadanang/api/search?' + new URLSearchParams(params).toString();
        
        this.log('info', 'Manual API test started', { params });
        
        return fetch(url, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.log('success', 'Manual API test completed', data);
            return data;
        })
        .catch(error => {
            this.log('error', 'Manual API test failed', { error: error.message });
            throw error;
        });
    }

    simulateSearch(params) {
        this.log('info', 'Simulating search', params);
        
        // Simulate form filling
        Object.keys(params).forEach(key => {
            const field = document.getElementById(`search${key.charAt(0).toUpperCase() + key.slice(1)}`);
            if (field) {
                field.value = params[key];
                field.dispatchEvent(new Event('change', { bubbles: true }));
            }
        });
        
        // Trigger search
        const searchBtn = document.getElementById('searchPageBtn');
        if (searchBtn) {
            searchBtn.click();
        }
    }
}

// Initialize debugger
const ajaxDebugger = new AjaxSearchDebugger();

// Show debug panel by default in development
if (window.location.hostname === 'localhost') {
    setTimeout(() => {
        ajaxDebugger.togglePanel();
        ajaxDebugger.log('info', 'AJAX Search Debug Panel ready! Press Ctrl+Shift+D to toggle');
    }, 1000);
}

// Expose to global scope for console access
window.ajaxDebugger = ajaxDebugger;
