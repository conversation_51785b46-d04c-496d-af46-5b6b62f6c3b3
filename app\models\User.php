<?php
require_once __DIR__ . '/../libraries/Database.php';

class User {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    // L<PERSON>y t<PERSON>t cả users
    public function getAllUsers() {
        $this->db->query('SELECT * FROM users ORDER BY created_at DESC');
        return $this->db->resultSet();
    }

    // Tìm kiếm users theo email hoặc tên
    public function searchUsers($searchTerm) {
        $this->db->query('SELECT * FROM users WHERE email LIKE :email_search OR fullname LIKE :name_search ORDER BY created_at DESC');
        $this->db->bind(':email_search', '%' . $searchTerm . '%');
        $this->db->bind(':name_search', '%' . $searchTerm . '%');
        return $this->db->resultSet();
    }

    // Tìm kiếm users cho AJAX Select2
    public function searchUsersForSelect2($searchTerm) {
        $this->db->query('SELECT id, fullname, email, phone FROM users
                         WHERE email LIKE :email_search OR fullname LIKE :name_search OR phone LIKE :phone_search
                         ORDER BY fullname ASC
                         LIMIT 20');
        $this->db->bind(':email_search', '%' . $searchTerm . '%');
        $this->db->bind(':name_search', '%' . $searchTerm . '%');
        $this->db->bind(':phone_search', '%' . $searchTerm . '%');
        return $this->db->resultSet();
    }

    // Lấy user theo ID
    public function getUserById($id) {
        $this->db->query('SELECT * FROM users WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    // Lấy user theo email
    public function getUserByEmail($email) {
        $this->db->query('SELECT * FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        return $this->db->single();
    }

    // Đăng nhập
    public function login($email, $password) {
        $user = $this->getUserByEmail($email);

        if ($user && password_verify($password, $user->password)) {
            // Loại bỏ password trước khi trả về thông tin user
            unset($user->password);
            return $user;
        }

        return false;
    }

    // Thêm user mới
    public function create($data) {
        $this->db->query('INSERT INTO users (fullname, phone, email, password, zalo, address, avatar, role)
                         VALUES (:fullname, :phone, :email, :password, :zalo, :address, :avatar, :role)');

        // Bind values với giá trị từ mảng $data hoặc giá trị mặc định
        $this->db->bind(':fullname', $data['fullname']);
        $this->db->bind(':phone', $data['phone'] ?? null);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':password', $data['password']);
        $this->db->bind(':zalo', $data['zalo'] ?? null);
        $this->db->bind(':address', $data['address'] ?? null);
        $this->db->bind(':avatar', $data['avatar'] ?? 'default-avatar.jpg');
        $this->db->bind(':role', $data['role'] ?? 'user');

        // Execute
        if ($this->db->execute()) {
            // Nếu là user thường, gán gói dịch vụ mặc định
            if (($data['role'] ?? 'user') === 'user') {
                $userId = $this->db->lastInsertId();

                require_once __DIR__ . '/UserPackage.php';
                $userPackageModel = new UserPackage();
                $userPackageModel->assignDefaultPackageToUser($userId);
            }
            return true;
        }

        return false;
    }

    // Cập nhật user
    public function update($data) {
        $this->db->query('UPDATE users SET
                         fullname = :fullname,
                         phone = :phone,
                         email = :email,
                         zalo = :zalo,
                         address = :address,
                         avatar = :avatar,
                         role = :role
                         WHERE id = :id');

        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':fullname', $data['fullname']);
        $this->db->bind(':phone', $data['phone'] ?? null);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':zalo', $data['zalo'] ?? null);
        $this->db->bind(':address', $data['address'] ?? null);
        $this->db->bind(':avatar', $data['avatar'] ?? 'default-avatar.jpg');
        $this->db->bind(':role', $data['role'] ?? 'user');

        // Execute
        return $this->db->execute();
    }

    // Cập nhật mật khẩu
    public function updatePassword($userId, $newPassword) {
        $this->db->query('UPDATE users SET password = :password WHERE id = :id');
        $this->db->bind(':id', $userId);
        $this->db->bind(':password', $newPassword);
        return $this->db->execute();
    }

    // Xóa user
    public function delete($id) {
        $this->db->query('DELETE FROM users WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }

    // Kiểm tra email đã tồn tại chưa
    public function findUserByEmail($email) {
        $this->db->query('SELECT * FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        $row = $this->db->single();
        return ($row) ? true : false;
    }

    // Kiểm tra số điện thoại đã tồn tại chưa
    public function findUserByPhone($phone) {
        $this->db->query('SELECT * FROM users WHERE phone = :phone');
        $this->db->bind(':phone', $phone);
        $row = $this->db->single();
        return ($row) ? true : false;
    }

    public function register($data) {
        $this->db->query('INSERT INTO users (email, password, role) VALUES (:email, :password, :role)');
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':password', $data['password']);
        $this->db->bind(':role', 'user');

        if ($this->db->execute()) {
            // Lấy ID của user vừa tạo
            $userId = $this->db->lastInsertId();

            // Gán gói dịch vụ mặc định cho user mới
            require_once __DIR__ . '/UserPackage.php';
            $userPackageModel = new UserPackage();
            $userPackageModel->assignDefaultPackageToUser($userId);

            return true;
        }

        return false;
    }

    public function updateUser($id, $data) {
        $sql = 'UPDATE users SET ';
        $params = [];

        // Remove id from data if it exists to avoid duplicate binding
        if (isset($data['id'])) {
            unset($data['id']);
        }

        foreach ($data as $key => $value) {
            $params[] = "$key = :$key";
        }

        $sql .= implode(', ', $params);
        $sql .= ' WHERE id = :id';

        $this->db->query($sql);

        // Bind all data parameters
        foreach ($data as $key => $value) {
            $this->db->bind(":$key", $value);
        }

        // Bind the id parameter separately
        $this->db->bind(':id', $id);

        return $this->db->execute();
    }
}