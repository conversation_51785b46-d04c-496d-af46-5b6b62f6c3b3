<?php if (empty($extensionRequests)): ?>
    <div class="alert alert-info">
        <i class="bi bi-info-circle me-2"></i>
        <PERSON>h<PERSON>ng có yêu cầu gia hạn nào.
    </div>
<?php else: ?>
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>ID</th>
                    <th>Bất động sản</th>
                    <th>Người yêu cầu</th>
                    <th><PERSON><PERSON><PERSON> yê<PERSON> cầu</th>
                    <th>Ngày gia hạn</th>
                    <th>Trạng thái</th>
                    <th>Admin xử lý</th>
                    <th>Thao tác</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($extensionRequests as $request): ?>
                <tr>
                    <td><?php echo $request->id; ?></td>
                    <td>
                        <div class="d-flex flex-column">
                            <strong class="text-truncate" style="max-width: 200px;" 
                                    title="<?php echo htmlspecialchars($request->property_title); ?>">
                                <?php echo htmlspecialchars($request->property_title); ?>
                            </strong>
                            <?php if ($request->property_slug): ?>
                            <small class="text-muted">
                                <a href="/thuenhadanang/<?php echo $request->property_slug; ?>-<?php echo $request->property_id; ?>/" 
                                   target="_blank" class="text-decoration-none">
                                    <i class="bi bi-box-arrow-up-right me-1"></i>Xem chi tiết
                                </a>
                            </small>
                            <?php endif; ?>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex flex-column">
                            <strong><?php echo htmlspecialchars($request->user_name); ?></strong>
                            <small class="text-muted"><?php echo htmlspecialchars($request->user_email); ?></small>
                        </div>
                    </td>
                    <td>
                        <small><?php echo date('d/m/Y H:i', strtotime($request->request_date)); ?></small>
                    </td>
                    <td>
                        <small><?php echo date('d/m/Y H:i', strtotime($request->requested_expiration_date)); ?></small>
                    </td>
                    <td>
                        <?php
                        $statusClass = '';
                        $statusIcon = '';
                        $statusText = '';
                        
                        switch ($request->status) {
                            case 'pending':
                                $statusClass = 'bg-warning text-dark';
                                $statusIcon = 'bi-clock-history';
                                $statusText = 'Chờ duyệt';
                                break;
                            case 'approved':
                                $statusClass = 'bg-success';
                                $statusIcon = 'bi-check-circle';
                                $statusText = 'Đã duyệt';
                                break;
                            case 'rejected':
                                $statusClass = 'bg-danger';
                                $statusIcon = 'bi-x-circle';
                                $statusText = 'Đã từ chối';
                                break;
                        }
                        ?>
                        <span class="badge <?php echo $statusClass; ?>">
                            <i class="bi <?php echo $statusIcon; ?> me-1"></i>
                            <?php echo $statusText; ?>
                        </span>
                    </td>
                    <td>
                        <?php if ($request->admin_name): ?>
                            <div class="d-flex flex-column">
                                <strong><?php echo htmlspecialchars($request->admin_name); ?></strong>
                                <?php if ($request->processed_at): ?>
                                <small class="text-muted">
                                    <?php echo date('d/m/Y H:i', strtotime($request->processed_at)); ?>
                                </small>
                                <?php endif; ?>
                                <?php if ($request->admin_note): ?>
                                <small class="text-info" title="<?php echo htmlspecialchars($request->admin_note); ?>">
                                    <i class="bi bi-chat-left-text me-1"></i>Có ghi chú
                                </small>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <span class="text-muted">-</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($request->status === 'pending'): ?>
                            <div class="btn-group" role="group">
                                <button type="button" 
                                        class="btn btn-success btn-sm approve-btn"
                                        data-request-id="<?php echo $request->id; ?>"
                                        title="Phê duyệt yêu cầu">
                                    <i class="bi bi-check-lg"></i>
                                </button>
                                <button type="button" 
                                        class="btn btn-danger btn-sm reject-btn"
                                        data-request-id="<?php echo $request->id; ?>"
                                        title="Từ chối yêu cầu">
                                    <i class="bi bi-x-lg"></i>
                                </button>
                            </div>
                        <?php else: ?>
                            <span class="text-muted">Đã xử lý</span>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php endif; ?>
