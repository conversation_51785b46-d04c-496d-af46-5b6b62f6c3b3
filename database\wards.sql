-- <PERSON><PERSON><PERSON> bảng cũ nếu tồn tại
DROP TABLE IF EXISTS `wards`;

-- T<PERSON><PERSON> bảng phường/xã
CREATE TABLE `wards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Thêm dữ liệu mẫu cho phường/xã ở Đà Nẵng
INSERT INTO `wards` (`name`, `slug`, `status`) VALUES
('<PERSON> <PERSON><PERSON><PERSON>', 'an-hai-bac', 1),
('An Hải Đ<PERSON>', 'an-hai-dong', 1),
('An Hải Tây', 'an-hai-tay', 1),
('H<PERSON>i Châu 1', 'hai-chau-1', 1),
('Hải Châu 2', 'hai-chau-2', 1),
('Thạch Thang', 'thach-thang', 1),
('<PERSON>h Bình', 'thanh-binh', 1),
('Thuận Phước', 'thuan-phuoc', 1),
('Mỹ An', 'my-an', 1),
('Khuê Mỹ', 'khue-my', 1),
('Hòa Hải', 'hoa-hai', 1),
('Hòa Quý', 'hoa-quy', 1),
('Hòa Xuân', 'hoa-xuan', 1);
