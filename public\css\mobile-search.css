/* ===========================
   MOBILE SEARCH STYLES
   =========================== */

/* Mobile Search Section */
.mobile-search-section {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 12px 0;
}

.mobile-search-form {
    margin: 0;
    padding: 0;
}

.mobile-search-container {
    width: 100%;
}

.mobile-search-row {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
}

/* Mobile Search Field */
.mobile-search-field {
    flex: 1;
    min-width: 0; /* Allow shrinking */
}

.mobile-search-input {
    height: 45px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0 12px;
    font-size: 14px;
    background: white;
    transition: all 0.2s ease;
    width: 100%;
}

.mobile-search-input::placeholder {
    color: #6c757d;
    font-size: 14px;
}

/* Mobile Filter Button */
.mobile-filter-btn {
    height: 45px;
    min-width: 80px;
    padding: 0 12px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    color: var(--primary-color, #f77316);
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.2s ease;
    flex-shrink: 0;
    cursor: pointer;
}

.mobile-filter-btn:hover {
    background: #f8f9fa;
    border-color: var(--primary-color, #f77316);
}

.mobile-filter-btn:active {
    background: #e9ecef;
}

.mobile-filter-btn i {
    font-size: 16px;
}

.filter-text {
    font-weight: 500;
}

/* Mobile Search Button */
.mobile-search-btn {
    height: 45px;
    width: 45px;
    padding: 0;
    background: var(--primary-color, #f77316);
    border: 1px solid var(--primary-color, #f77316);
    border-radius: 6px;
    color: white;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
    cursor: pointer;
}

.mobile-search-btn:hover {
    background: #e56b17;
    border-color: #e56b17;
}

.mobile-search-btn:active {
    background: #d65d14;
    border-color: #d65d14;
}

/* Badge Styling */
.mobile-filter-btn .badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 4px;
}

/* ===========================
   MOBILE FILTERS MODAL STYLES
   =========================== */

/* Modal Customization */
#mobileFiltersModal .modal-dialog {
    max-width: 90vw;
    margin: 1rem auto;
}

#mobileFiltersModal .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

#mobileFiltersModal .modal-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
    padding: 1rem 1.5rem;
}

#mobileFiltersModal .modal-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

#mobileFiltersModal .modal-body {
    padding: 1.5rem;
}

#mobileFiltersModal .modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

/* Form Elements in Modal */
#mobileFiltersModal .form-label {
    font-weight: 500;
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 14px;
}

#mobileFiltersModal .form-label i {
    color: var(--primary-color, #f77316);
    font-size: 14px;
}

#mobileFiltersModal .form-select {
    height: 45px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0 12px;
    font-size: 14px;
    background: white;
    transition: all 0.2s ease;
}

#mobileFiltersModal .form-select:focus {
    border-color: var(--primary-color, #f77316);
    box-shadow: 0 0 0 0.2rem rgba(247, 115, 22, 0.25);
    outline: none;
}

/* Modal Buttons */
#mobileFiltersModal .btn {
    height: 40px;
    padding: 0 1rem;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.2s ease;
    flex: 1;
    max-width: 48%;
}

#mobileFiltersClear {
    color: #6c757d;
    border-color: #dee2e6;
    background: white;
    order: 1;
}

#mobileFiltersClear:hover {
    color: #495057;
    border-color: #adb5bd;
    background: #f8f9fa;
}

#mobileFiltersApply {
    background: var(--primary-color, #f77316);
    border-color: var(--primary-color, #f77316);
    color: white;
    order: 2;
}

#mobileFiltersApply:hover {
    background: #e56b17;
    border-color: #e56b17;
}

/* ===========================
   RESPONSIVE ADJUSTMENTS
   =========================== */

/* Tablet Adjustments */
@media (max-width: 1024px) and (min-width: 769px) {
    .mobile-search-row {
        gap: 10px;
    }

    .mobile-filter-btn {
        min-width: 90px;
        padding: 0 14px;
    }

    #mobileFiltersModal .modal-dialog {
        max-width: 600px;
    }
}

/* Small Mobile Adjustments */
@media (max-width: 480px) {
    .mobile-search-section {
        padding: 8px 0;
    }

    .mobile-search-row {
        gap: 6px;
    }

    .mobile-search-input {
        height: 42px;
        font-size: 13px;
        padding: 0 10px;
    }

    .mobile-filter-btn {
        height: 42px;
        min-width: 70px;
        padding: 0 10px;
        font-size: 13px;
    }

    .mobile-search-btn {
        height: 42px;
        width: 42px;
        font-size: 15px;
    }

    .filter-text {
        display: none; /* Hide text on very small screens */
    }

    #mobileFiltersModal .modal-dialog {
        max-width: 95vw;
        margin: 0.5rem auto;
    }

    #mobileFiltersModal .modal-body {
        padding: 1rem;
    }

    #mobileFiltersModal .modal-footer {
        padding: 1rem;
        flex-direction: row;
        justify-content: space-between;
    }

    #mobileFiltersModal .btn {
        height: 38px;
        font-size: 13px;
        flex: 1;
        max-width: 48%;
    }
}

/* Very Small Mobile */
@media (max-width: 360px) {
    .mobile-filter-btn {
        min-width: 60px;
        padding: 0 8px;
    }

    .mobile-filter-btn .badge {
        font-size: 9px;
        padding: 1px 4px;
    }
}

/* ===========================
   LOADING STATES
   =========================== */

.mobile-search-loading .mobile-search-btn {
    pointer-events: none;
    opacity: 0.7;
}

.mobile-search-loading .mobile-search-btn i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===========================
   ACCESSIBILITY
   =========================== */


/* High contrast mode support */
@media (prefers-contrast: high) {
    .mobile-search-input,
    .mobile-filter-btn,
    #mobileFiltersModal .form-select {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .mobile-search-input,
    .mobile-filter-btn,
    .mobile-search-btn,
    #mobileFiltersModal .form-select,
    #mobileFiltersModal .btn {
        transition: none;
    }

    .mobile-search-loading .mobile-search-btn i {
        animation: none;
    }
}
