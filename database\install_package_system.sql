-- <PERSON><PERSON>t cài đặt hệ thống gói dịch vụ đăng tin
-- Chạy script này để tạo tất cả các bảng cần thiết cho hệ thống gói dịch vụ

-- 1. Tạo bảng packages (gói dịch vụ)
DROP TABLE IF EXISTS `packages`;
CREATE TABLE `packages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT 'Tên gói dịch vụ',
  `description` text DEFAULT NULL COMMENT '<PERSON>ô tả gói dịch vụ',
  `post_limit` int(11) NOT NULL DEFAULT 0 COMMENT 'Số lượng tin đăng tối đa',
  `price` decimal(15,0) NOT NULL DEFAULT 0 COMMENT 'Gi<PERSON> gói (VNĐ)',
  `duration_days` int(11) NOT NULL DEFAULT 30 COMMENT 'Th<PERSON><PERSON> hạn gói (ngày)',
  `features` text DEFAULT NULL COMMENT 'T<PERSON>h năng của gói (JSON)',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Gói mặc định cho user mới',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Trạng thái hoạt động',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Tạo bảng user_packages (gói dịch vụ của người dùng)
DROP TABLE IF EXISTS `user_packages`;
CREATE TABLE `user_packages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'ID người dùng',
  `package_id` int(11) NOT NULL COMMENT 'ID gói dịch vụ',
  `start_date` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'Ngày bắt đầu',
  `end_date` timestamp NOT NULL COMMENT 'Ngày kết thúc',
  `posts_used` int(11) NOT NULL DEFAULT 0 COMMENT 'Số tin đã sử dụng',
  `posts_remaining` int(11) NOT NULL DEFAULT 0 COMMENT 'Số tin còn lại',
  `status` enum('active','expired','cancelled') NOT NULL DEFAULT 'active' COMMENT 'Trạng thái gói',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `package_id` (`package_id`),
  CONSTRAINT `user_packages_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_packages_ibfk_2` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Tạo bảng posts_count (theo dõi số lượng tin đã đăng)
DROP TABLE IF EXISTS `posts_count`;
CREATE TABLE `posts_count` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'ID người dùng',
  `user_package_id` int(11) NOT NULL COMMENT 'ID gói dịch vụ của người dùng',
  `property_id` int(11) NOT NULL COMMENT 'ID bất động sản',
  `posted_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'Thời gian đăng tin',
  `status` enum('active','deleted','expired') NOT NULL DEFAULT 'active' COMMENT 'Trạng thái tin đăng',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `user_package_id` (`user_package_id`),
  KEY `property_id` (`property_id`),
  CONSTRAINT `posts_count_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `posts_count_ibfk_2` FOREIGN KEY (`user_package_id`) REFERENCES `user_packages` (`id`) ON DELETE CASCADE,
  CONSTRAINT `posts_count_ibfk_3` FOREIGN KEY (`property_id`) REFERENCES `properties` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Thêm dữ liệu mẫu cho 2 gói dịch vụ
INSERT INTO `packages` (`name`, `description`, `post_limit`, `price`, `duration_days`, `features`, `is_default`, `status`) VALUES
('Gói Cơ bản', 'Gói dành cho người dùng cá nhân với nhu cầu đăng tin cơ bản', 5, 0, 30, '["Đăng tối đa 5 tin", "Hiển thị thông tin liên hệ", "Hỗ trợ qua email"]', 1, 1),
('Gói Chuyên nghiệp', 'Gói dành cho môi giới và doanh nghiệp với nhiều tính năng nâng cao', 20, 500000, 30, '["Đăng tối đa 20 tin", "Tin được ưu tiên hiển thị", "Hiển thị thông tin liên hệ", "Hỗ trợ qua Zalo/Phone", "Thống kê chi tiết"]', 0, 1);

-- 5. Gán gói cơ bản cho tất cả user hiện tại (trừ admin)
-- Gói miễn phí sẽ có thời hạn vĩnh viễn (2099-12-31)
INSERT INTO `user_packages` (`user_id`, `package_id`, `start_date`, `end_date`, `posts_used`, `posts_remaining`, `status`)
SELECT
    u.id,
    1, -- ID của gói cơ bản
    NOW(),
    '2099-12-31 23:59:59', -- Vĩnh viễn cho gói miễn phí
    0,
    5, -- Số tin của gói cơ bản
    'active'
FROM users u
WHERE u.role = 'user'
AND u.id NOT IN (SELECT user_id FROM user_packages);

-- Hoàn thành cài đặt
SELECT 'Hệ thống gói dịch vụ đã được cài đặt thành công!' as message;
