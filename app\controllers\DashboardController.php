<?php
require_once 'app/models/User.php';
require_once 'app/models/Property.php';

class DashboardController {
    private $userModel;
    private $propertyModel;

    public function __construct() {
        $this->userModel = new User();
        $this->propertyModel = new Property();
    }

    public function index() {
        // Khởi tạo session nếu chưa có
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Kiểm tra đăng nhập
        if (!isset($_SESSION['user_id'])) {
            header('Location: /thuenhadanang/login');
            exit;
        }

        // Lấy thông tin user
        $user = $this->userModel->getUserById($_SESSION['user_id']);

        // Lấy thống kê bất động sản của user
        $userId = $_SESSION['user_id'];
        $totalProperties = $this->propertyModel->countUserProperties($userId, 'all');
        $pendingProperties = $this->propertyModel->countUserProperties($userId, 'pending');
        $activeProperties = $this->propertyModel->countUserProperties($userId, 'active');
        $expiredProperties = $this->propertyModel->countUserProperties($userId, 'expired');

        // Lấy danh sách bất động sản gần đây của user
        $recentProperties = $this->propertyModel->getUserProperties($userId, 'all', 5);

        // Lấy thông tin gói dịch vụ của user
        require_once 'app/models/UserPackage.php';
        $userPackageModel = new UserPackage();
        $userPackage = $userPackageModel->getUserPackageByUserId($userId);

        // Tạo đối tượng thống kê
        $stats = (object)[
            'total_properties' => $totalProperties,
            'pending_properties' => $pendingProperties,
            'active_properties' => $activeProperties,
            'expired_properties' => $expiredProperties
        ];

        // Thiết lập tiêu đề trang
        $title = 'Quản lý tài khoản - Thuê Nhà Đà Nẵng';

        // Thiết lập view và data
        $view = 'dashboard';
        $data = [
            'user' => $user,
            'stats' => $stats,
            'recent_properties' => $recentProperties,
            'user_package' => $userPackage,
            'error' => $_SESSION['dashboard_error'] ?? '',
            'success' => $_SESSION['dashboard_success'] ?? ''
        ];

        // Xóa thông báo trong session
        unset($_SESSION['dashboard_error']);
        unset($_SESSION['dashboard_success']);

        // Render layout với view
        require 'app/views/layout.php';
    }

    public function updateProfile() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Khởi tạo session nếu chưa có
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            // Kiểm tra đăng nhập
            if (!isset($_SESSION['user_id'])) {
                header('Location: /thuenhadanang/login');
                exit;
            }

            // Lấy và lọc dữ liệu từ form
            $data = [
                'id' => $_SESSION['user_id'],
                'fullname' => filter_input(INPUT_POST, 'fullname', FILTER_SANITIZE_SPECIAL_CHARS),
                'email' => filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL),
                'phone' => filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_SPECIAL_CHARS),
                'zalo' => filter_input(INPUT_POST, 'zalo', FILTER_SANITIZE_SPECIAL_CHARS),
                'address' => filter_input(INPUT_POST, 'address', FILTER_SANITIZE_SPECIAL_CHARS)
            ];

            // Validate dữ liệu
            $errors = [];

            if (empty($data['fullname'])) {
                $errors[] = 'Vui lòng nhập họ tên';
            }

            if (empty($data['email'])) {
                $errors[] = 'Vui lòng nhập email';
            } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Email không hợp lệ';
            }

            // Kiểm tra email đã tồn tại chưa (trừ email hiện tại của user)
            $currentUser = $this->userModel->getUserById($_SESSION['user_id']);
            if ($data['email'] !== $currentUser->email && $this->userModel->findUserByEmail($data['email'])) {
                $errors[] = 'Email đã được sử dụng';
            }

            // Nếu có lỗi
            if (!empty($errors)) {
                $_SESSION['dashboard_error'] = implode('<br>', $errors);
                header('Location: /thuenhadanang/dashboard');
                exit;
            }

            // Cập nhật thông tin user
            if ($this->userModel->update($data)) {
                // Cập nhật session
                $_SESSION['user_name'] = $data['fullname'];
                $_SESSION['user_email'] = $data['email'];

                $_SESSION['dashboard_success'] = 'Cập nhật thông tin thành công';
            } else {
                $_SESSION['dashboard_error'] = 'Có lỗi xảy ra, vui lòng thử lại';
            }

            header('Location: /thuenhadanang/dashboard');
            exit;
        }

        // Nếu không phải POST request, chuyển về trang dashboard
        header('Location: /thuenhadanang/dashboard');
        exit;
    }

    public function updateAvatar() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Khởi tạo session nếu chưa có
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            // Kiểm tra đăng nhập
            if (!isset($_SESSION['user_id'])) {
                header('Location: /thuenhadanang/login');
                exit;
            }

            $userId = $_SESSION['user_id'];

            // Kiểm tra dữ liệu avatar
            if (empty($_POST['avatar_data'])) {
                $_SESSION['dashboard_error'] = 'Vui lòng cắt ảnh trước khi lưu!';
                header('Location: /thuenhadanang/dashboard/profile');
                exit;
            }

            // Lấy thông tin user hiện tại
            $currentUser = $this->userModel->getUserById($userId);

            if (!$currentUser) {
                $_SESSION['dashboard_error'] = 'Không tìm thấy thông tin người dùng!';
                header('Location: /thuenhadanang/dashboard/profile');
                exit;
            }

            // Xử lý dữ liệu base64
            $avatarData = $_POST['avatar_data'];
            $avatarData = str_replace('data:image/jpeg;base64,', '', $avatarData);
            $avatarData = str_replace('data:image/png;base64,', '', $avatarData);
            $avatarData = str_replace(' ', '+', $avatarData);
            $avatarData = base64_decode($avatarData);

            if (!$avatarData) {
                $_SESSION['dashboard_error'] = 'Dữ liệu ảnh không hợp lệ!';
                header('Location: /thuenhadanang/dashboard/profile');
                exit;
            }

            $uploadDir = __DIR__ . '/../../public/uploads/users/';

            // Tạo thư mục nếu chưa tồn tại
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            // Tạo tên file mới
            $fileName = 'avatar_' . time() . '_' . mt_rand(1000, 9999) . '.jpg';
            $targetFile = $uploadDir . $fileName;

            // Xóa avatar cũ nếu có và không phải avatar mặc định
            if (!empty($currentUser->avatar) && $currentUser->avatar != 'default-avatar.jpg') {
                $oldAvatarPath = $uploadDir . $currentUser->avatar;
                if (file_exists($oldAvatarPath)) {
                    unlink($oldAvatarPath);
                    error_log('Deleted old avatar: ' . $currentUser->avatar);
                }
            }

            // Lưu file mới
            if (file_put_contents($targetFile, $avatarData)) {
                // Cập nhật thông tin avatar trong database
                $userData = [
                    'avatar' => $fileName
                ];

                if ($this->userModel->updateUser($userId, $userData)) {
                    // Cập nhật session
                    $_SESSION['user_avatar'] = $fileName;

                    $_SESSION['dashboard_success'] = 'Cập nhật avatar thành công!';
                } else {
                    $_SESSION['dashboard_error'] = 'Có lỗi xảy ra khi cập nhật thông tin avatar!';
                }
            } else {
                $_SESSION['dashboard_error'] = 'Có lỗi xảy ra khi lưu file avatar!';
            }

            header('Location: /thuenhadanang/dashboard/profile');
            exit;
        }

        // Nếu không phải POST request, chuyển về trang profile
        header('Location: /thuenhadanang/dashboard/profile');
        exit;
    }
}