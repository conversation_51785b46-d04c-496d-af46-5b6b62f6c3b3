-- <PERSON><PERSON><PERSON> bảng cũ nếu tồn tại
DROP TABLE IF EXISTS `property_types`;

-- <PERSON><PERSON><PERSON> bảng loại hình bất động sản
CREATE TABLE `property_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Thêm dữ liệu mẫu
INSERT INTO `property_types` (`name`, `slug`, `description`, `status`) VALUES
('<PERSON><PERSON><PERSON> hộ', 'can-ho', '<PERSON><PERSON><PERSON> hộ chung cư', 1),
('Nhà riêng', 'nha-rieng', 'Nhà riêng, nhà phố', 1),
('Nhà mặt phố', 'nha-mat-pho', 'Nhà mặt tiền, mặt phố', 1),
('Nhà trọ - phòng trọ', 'nha-tro-phong-tro', 'Nhà trọ, phòng trọ cho thuê', 1),
('Biệt thự', 'biet-thu', 'Biệt thự, villa', 1),
('Văn phòng', 'van-phong', 'Văn phòng cho thuê', 1);
