<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mobile URL Generation</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .test-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
        }

        .test-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }

        .test-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #f77316;
            padding-bottom: 10px;
        }

        .test-case {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .test-input {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-family: monospace;
            font-size: 13px;
        }

        .test-output {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 13px;
            word-break: break-all;
        }

        .test-expected {
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin-top: 5px;
            font-family: monospace;
            font-size: 13px;
            word-break: break-all;
        }

        .status-success {
            color: #28a745;
            font-weight: bold;
        }

        .status-error {
            color: #dc3545;
            font-weight: bold;
        }

        .test-btn {
            background: #f77316;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }

        .test-btn:hover {
            background: #e56b17;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <div class="test-title">🔍 Mobile URL Generation Test</div>

            <button class="test-btn" onclick="runAllTests()">Run All Tests</button>
            <button class="test-btn" onclick="clearResults()">Clear Results</button>

            <div id="testResults"></div>
        </div>
    </div>

    <script>
        // Mobile Search URL Builder (copied from mobile-search.js)
        class MobileUrlBuilder {
            buildSeoUrl(params) {
                console.log('📱 Building SEO URL with params:', params);

                let url = '/thuenhadanang';
                const { type, ward, price, bedrooms, bathrooms, area, direction, keyword, sort } = params;

                // Build base segment: /cho-thue-{type}-tai-{ward}
                const baseSegment = this.buildBaseSegment(type, ward);
                if (baseSegment) {
                    url += '/' + baseSegment;
                }

                // Build filter segment: gia-X-trieu-dt-Y-m2-Zpn (all connected with dashes)
                const filterSegment = this.buildFilterSegment(price, area, bedrooms);
                if (filterSegment) {
                    url += '/' + filterSegment;
                }

                // Add query parameters for remaining filters
                const queryParams = [];
                if (keyword) queryParams.push(`keyword=${encodeURIComponent(keyword)}`);
                if (bathrooms) queryParams.push(`bathrooms=${encodeURIComponent(bathrooms)}`);
                if (direction) queryParams.push(`direction=${encodeURIComponent(direction)}`);
                if (sort && sort !== 'default') queryParams.push(`sort=${encodeURIComponent(sort)}`);

                if (queryParams.length > 0) {
                    url += '?' + queryParams.join('&');
                }

                console.log('📱 Built SEO URL:', url);
                return url;
            }

            buildBaseSegment(type, ward) {
                // Handle empty type -> use "nha-dat"
                if (!type) {
                    type = 'nha-dat';
                }

                if (ward) {
                    return 'cho-thue-' + type + '-tai-' + ward;
                } else if (type) {
                    return 'cho-thue-' + type;
                }

                return '';
            }

            buildFilterSegment(price, area, bedrooms) {
                const filterParts = [];

                // Add price
                if (price) {
                    filterParts.push('gia-' + this.formatPriceForUrl(price));
                }

                // Add area
                if (area) {
                    filterParts.push('dt-' + this.formatAreaForUrl(area));
                }

                // Add bedrooms
                if (bedrooms) {
                    if (bedrooms === '4+') {
                        filterParts.push('4pn-tro-len');
                    } else {
                        filterParts.push(bedrooms + 'pn');
                    }
                }

                return filterParts.join('-');
            }

            formatPriceForUrl(price) {
                if (price.includes('+')) {
                    return 'tren-' + price.replace('+', '') + '-trieu';
                } else if (price.includes('-')) {
                    return price + '-trieu';
                } else {
                    return price + '-trieu';
                }
            }

            formatAreaForUrl(area) {
                if (area.includes('+')) {
                    return 'tren-' + area.replace('+', '') + '-m2';
                } else if (area.includes('-')) {
                    // Format: dt-50-70m2 (no dash before m2)
                    return area + 'm2';
                } else {
                    return area + 'm2';
                }
            }
        }

        const urlBuilder = new MobileUrlBuilder();

        // Test cases
        const testCases = [
            {
                name: "Basic: Type + Ward + Price + Bedrooms",
                input: {
                    type: 'can-ho',
                    ward: 'an-hai-bac',
                    price: '15+',
                    bedrooms: '2'
                },
                expected: '/thuenhadanang/cho-thue-can-ho-tai-an-hai-bac/gia-tren-15-trieu-2pn'
            },
            {
                name: "Price Range: 5-7 triệu",
                input: {
                    type: 'can-ho',
                    ward: 'an-hai-bac',
                    price: '5-7',
                    bedrooms: '2'
                },
                expected: '/thuenhadanang/cho-thue-can-ho-tai-an-hai-bac/gia-5-7-trieu-2pn'
            },
            {
                name: "With Area: 50-70m2",
                input: {
                    type: 'can-ho',
                    ward: 'an-hai-bac',
                    price: '15+',
                    area: '50-70',
                    bedrooms: '2'
                },
                expected: '/thuenhadanang/cho-thue-can-ho-tai-an-hai-bac/gia-tren-15-trieu-dt-50-70m2-2pn'
            },
            {
                name: "4+ Bedrooms",
                input: {
                    type: 'nha-rieng',
                    ward: 'my-an',
                    price: '10-15',
                    bedrooms: '4+'
                },
                expected: '/thuenhadanang/cho-thue-nha-rieng-tai-my-an/gia-10-15-trieu-4pn-tro-len'
            },
            {
                name: "No Type (default to nha-dat)",
                input: {
                    ward: 'an-hai-bac',
                    price: '15+',
                    bedrooms: '2'
                },
                expected: '/thuenhadanang/cho-thue-nha-dat-tai-an-hai-bac/gia-tren-15-trieu-2pn'
            },
            {
                name: "With Query Parameters",
                input: {
                    type: 'can-ho',
                    ward: 'an-hai-bac',
                    price: '15+',
                    bedrooms: '2',
                    bathrooms: '2',
                    direction: 'tay', // Using slug now
                    keyword: 'test'
                },
                expected: '/thuenhadanang/cho-thue-can-ho-tai-an-hai-bac/gia-tren-15-trieu-2pn?keyword=test&bathrooms=2&direction=tay'
            },
            {
                name: "Only Type",
                input: {
                    type: 'can-ho'
                },
                expected: '/thuenhadanang/cho-thue-can-ho'
            },
            {
                name: "Only Price",
                input: {
                    price: '15+'
                },
                expected: '/thuenhadanang/cho-thue-nha-dat/gia-tren-15-trieu'
            },
            {
                name: "Keyword Only",
                input: {
                    keyword: 'Ngô Quyền'
                },
                expected: '/thuenhadanang/search?keyword=Ng%C3%B4%20Quy%E1%BB%81n'
            },
            {
                name: "Type + Keyword",
                input: {
                    type: 'can-ho',
                    keyword: 'test'
                },
                expected: '/thuenhadanang/cho-thue-can-ho?keyword=test'
            },
            {
                name: "Type + Ward + Keyword",
                input: {
                    type: 'can-ho',
                    ward: 'an-hai-bac',
                    keyword: 'Ngô Quyền'
                },
                expected: '/thuenhadanang/cho-thue-can-ho-tai-an-hai-bac?keyword=Ng%C3%B4%20Quy%E1%BB%81n'
            },
            {
                name: "No Filters No Keyword (empty search)",
                input: {},
                expected: '/thuenhadanang/search'
            },
            {
                name: "Empty Values",
                input: {
                    keyword: '',
                    type: '',
                    ward: '',
                    price: ''
                },
                expected: '/thuenhadanang/search'
            }
        ];

        function runAllTests() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '';

            testCases.forEach((testCase, index) => {
                const result = runSingleTest(testCase, index + 1);
                resultsDiv.appendChild(result);
            });
        }

        function runSingleTest(testCase, index) {
            const testDiv = document.createElement('div');
            testDiv.className = 'test-case';

            const actual = urlBuilder.buildSeoUrl(testCase.input);
            const isMatch = actual === testCase.expected;

            testDiv.innerHTML = `
                <h5>Test ${index}: ${testCase.name}</h5>
                <div class="test-input">
                    <strong>Input:</strong> ${JSON.stringify(testCase.input, null, 2)}
                </div>
                <div class="test-output">
                    <strong>Actual:</strong> ${actual}
                </div>
                <div class="test-expected">
                    <strong>Expected:</strong> ${testCase.expected}
                </div>
                <div class="mt-2">
                    <span class="${isMatch ? 'status-success' : 'status-error'}">
                        ${isMatch ? '✅ PASS' : '❌ FAIL'}
                    </span>
                </div>
            `;

            return testDiv;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        // Auto-run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            runAllTests();
        });
    </script>
</body>
</html>
