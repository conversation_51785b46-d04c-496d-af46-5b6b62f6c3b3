<?php
require_once __DIR__ . '/../libraries/Database.php';

class Property {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    // L<PERSON>y tất cả bất động sản
    public function getAllProperties() {
        $this->db->query('SELECT p.*, pt.name as type_name, u.fullname as owner_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN users u ON p.user_id = u.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          ORDER BY p.created_at DESC');
        return $this->db->resultSet();
    }

    // Lấy bất động sản theo ID
    public function getPropertyById($id) {
        $this->db->query('SELECT p.*, pt.name as type_name, u.fullname as owner_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN users u ON p.user_id = u.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          WHERE p.id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    // Lấy bất động sản theo slug
    public function getPropertyBySlug($slug) {
        $this->db->query('SELECT p.*, pt.name as type_name, u.fullname as owner_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN users u ON p.user_id = u.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          WHERE p.slug = :slug');
        $this->db->bind(':slug', $slug);
        return $this->db->single();
    }

    // Thêm bất động sản mới
    public function create($data) {
        $this->db->query('INSERT INTO properties (user_id, title, slug, description, type_id, address, ward_id, street, city, area, price, price_period, bedrooms, bathrooms, direction, video_url, images, main_image, status, featured, active, expiration_date)
                          VALUES (:user_id, :title, :slug, :description, :type_id, :address, :ward_id, :street, :city, :area, :price, :price_period, :bedrooms, :bathrooms, :direction, :video_url, :images, :main_image, :status, :featured, :active, :expiration_date)');

        // Bind values
        $this->db->bind(':user_id', $data['user_id']);
        $this->db->bind(':title', $data['title']);
        $this->db->bind(':slug', $data['slug']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':type_id', $data['type_id']);
        $this->db->bind(':address', $data['address']);
        $this->db->bind(':ward_id', $data['ward_id'] ?? null);
        $this->db->bind(':street', $data['street'] ?? null);
        $this->db->bind(':city', $data['city'] ?? 'Đà Nẵng');
        $this->db->bind(':area', $data['area'] ?? null);
        $this->db->bind(':price', $data['price']);
        $this->db->bind(':price_period', $data['price_period'] ?? 'month');
        $this->db->bind(':bedrooms', $data['bedrooms'] ?? null);
        $this->db->bind(':bathrooms', $data['bathrooms'] ?? null);
        $this->db->bind(':direction', $data['direction'] ?? null);
        $this->db->bind(':video_url', $data['video_url'] ?? null);
        $this->db->bind(':images', $data['images'] ?? null);
        $this->db->bind(':main_image', $data['main_image'] ?? null);
        $this->db->bind(':status', $data['status'] ?? 'display');
        $this->db->bind(':featured', $data['featured'] ?? 0);
        $this->db->bind(':active', $data['active'] ?? 0);
        $this->db->bind(':expiration_date', $data['expiration_date'] ?? null);

        // Execute
        if ($this->db->execute()) {
            // Lấy ID của bản ghi vừa thêm
            $propertyId = $this->db->lastInsertId();

            // Ghi log để debug
            error_log('Property created with ID: ' . $propertyId);

            return $propertyId;
        } else {
            error_log('Failed to create property');
            return false;
        }
    }



    // Xóa bất động sản
    public function delete($id) {
        $this->db->query('DELETE FROM properties WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }

    // Kiểm tra xem slug đã tồn tại chưa (trừ ID hiện tại)
    public function isSlugExists($slug, $id = 0) {
        $this->db->query('SELECT COUNT(*) as count FROM properties WHERE slug = :slug AND id != :id');
        $this->db->bind(':slug', $slug);
        $this->db->bind(':id', $id);
        $row = $this->db->single();
        return $row->count > 0;
    }

    // Tạo slug từ tiêu đề
    public function createSlug($title) {
        // Chuyển đổi tiếng Việt sang không dấu
        $slug = $this->convertToSlug($title);

        // Kiểm tra xem slug đã tồn tại chưa
        $originalSlug = $slug;
        $count = 1;

        while ($this->isSlugExists($slug)) {
            $slug = $originalSlug . '-' . $count;
            $count++;
        }

        return $slug;
    }

    // Hàm chuyển đổi tiếng Việt sang không dấu
    private function convertToSlug($string) {
        $string = trim($string);
        $string = preg_replace('/[^a-zA-Z0-9ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễếệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ\s]/', '', $string);
        $string = preg_replace('/[\s]+/', ' ', $string);
        $string = str_replace(' ', '-', $string);

        $string = strtolower($string);
        $string = preg_replace('/[àáạảãâầấậẩẫăằắặẳẵ]/u', 'a', $string);
        $string = preg_replace('/[èéẹẻẽêềếệểễ]/u', 'e', $string);
        $string = preg_replace('/[ìíịỉĩ]/u', 'i', $string);
        $string = preg_replace('/[òóọỏõôồốộổỗơờớợởỡ]/u', 'o', $string);
        $string = preg_replace('/[ùúụủũưừứựửữ]/u', 'u', $string);
        $string = preg_replace('/[ỳýỵỷỹ]/u', 'y', $string);
        $string = preg_replace('/đ/u', 'd', $string);

        return $string;
    }

    // Lấy tất cả hình ảnh của bất động sản
    public function getPropertyImages($propertyId) {
        // Lấy thông tin bất động sản
        $this->db->query('SELECT images, main_image FROM properties WHERE id = :id');
        $this->db->bind(':id', $propertyId);
        $property = $this->db->single();

        if ($property && $property->images) {
            // Giải mã JSON thành mảng
            $images = json_decode($property->images, true);

            if (is_array($images)) {
                $result = [];
                foreach ($images as $index => $image) {
                    $isMain = ($property->main_image === $image);
                    $result[] = (object)[
                        'id' => $propertyId . '_' . $index, // Tạo ID có định dạng "propertyId_imageIndex"
                        'property_id' => $propertyId,
                        'image_path' => $image,
                        'is_main' => $isMain ? 1 : 0
                    ];
                }

                // Sắp xếp để ảnh chính hiển thị đầu tiên
                usort($result, function($a, $b) {
                    return $b->is_main - $a->is_main;
                });

                return $result;
            }
        }

        return [];
    }

    // Thêm hình ảnh cho bất động sản
    public function addPropertyImage($propertyId, $imagePath, $isMain = 0) {
        // Lấy thông tin bất động sản
        $this->db->query('SELECT images, main_image FROM properties WHERE id = :id');
        $this->db->bind(':id', $propertyId);
        $property = $this->db->single();

        // Khởi tạo mảng hình ảnh
        $images = [];
        if ($property && $property->images) {
            $images = json_decode($property->images, true) ?? [];
        }

        // Thêm hình ảnh mới vào mảng
        $images[] = $imagePath;

        // Cập nhật main_image nếu cần
        $mainImage = $property->main_image;
        if ($isMain || empty($mainImage)) {
            $mainImage = $imagePath;
        }

        // Cập nhật bảng properties
        $this->db->query('UPDATE properties SET images = :images, main_image = :main_image WHERE id = :id');
        $this->db->bind(':id', $propertyId);
        $this->db->bind(':images', json_encode($images));
        $this->db->bind(':main_image', $mainImage);

        $result = $this->db->execute();

        // Ghi log để debug
        error_log('PropertyImage add result: ' . ($result ? 'success' : 'failed') . ' for property_id: ' . $propertyId . ', image: ' . $imagePath);

        return $result;
    }

    // Lấy tất cả hướng bất động sản
    public function getAllDirections() {
        $this->db->query('SELECT * FROM property_directions WHERE status = 1 ORDER BY name ASC');
        return $this->db->resultSet();
    }

    // Cập nhật bất động sản
    public function update($data) {
        $this->db->query('UPDATE properties SET
                          title = :title,
                          slug = :slug,
                          description = :description,
                          type_id = :type_id,
                          address = :address,
                          ward_id = :ward_id,
                          street = :street,
                          city = :city,
                          area = :area,
                          price = :price,
                          price_period = :price_period,
                          bedrooms = :bedrooms,
                          bathrooms = :bathrooms,
                          direction = :direction,
                          video_url = :video_url,
                          images = :images,
                          main_image = :main_image,
                          status = :status,
                          active = :active,
                          expiration_date = :expiration_date,
                          updated_at = NOW()
                          WHERE id = :id');

        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':title', $data['title']);
        $this->db->bind(':slug', $data['slug']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':type_id', $data['type_id']);
        $this->db->bind(':address', $data['address']);
        $this->db->bind(':ward_id', $data['ward_id'] ?? null);
        $this->db->bind(':street', $data['street'] ?? null);
        $this->db->bind(':city', $data['city'] ?? 'Đà Nẵng');
        $this->db->bind(':area', $data['area'] ?? null);
        $this->db->bind(':price', $data['price']);
        $this->db->bind(':price_period', $data['price_period'] ?? 'month');
        $this->db->bind(':bedrooms', $data['bedrooms'] ?? null);
        $this->db->bind(':bathrooms', $data['bathrooms'] ?? null);
        $this->db->bind(':direction', $data['direction'] ?? null);
        $this->db->bind(':video_url', $data['video_url'] ?? null);
        $this->db->bind(':images', $data['images'] ?? null);
        $this->db->bind(':main_image', $data['main_image'] ?? null);
        $this->db->bind(':status', $data['status'] ?? 'display');
        $this->db->bind(':active', $data['active'] ?? 0);
        $this->db->bind(':expiration_date', $data['expiration_date'] ?? null);

        // Execute
        $result = $this->db->execute();

        // Ghi log để debug
        error_log('Property update result: ' . ($result ? 'success' : 'failed') . ' for ID: ' . $data['id']);
        if (isset($data['expiration_date'])) {
            error_log('Expiration date updated to: ' . $data['expiration_date']);
        }

        return $result;
    }

    // Cập nhật chỉ hình ảnh của bất động sản
    public function updateImages($data) {
        $this->db->query('UPDATE properties SET
                          images = :images,
                          main_image = :main_image
                          WHERE id = :id');

        $this->db->bind(':id', $data['id']);
        $this->db->bind(':images', $data['images']);
        $this->db->bind(':main_image', $data['main_image']);

        $result = $this->db->execute();

        // Ghi log để debug
        error_log('Property images update result: ' . ($result ? 'success' : 'failed') . ' for ID: ' . $data['id']);

        return $result;
    }

    // Lấy bất động sản nổi bật
    public function getFeaturedProperties($limit = 12) {
        $this->db->query('SELECT p.*, pt.name as type_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          WHERE p.featured = 1 AND p.status = "display" AND p.active = 1
                          AND (p.expiration_date IS NULL OR p.expiration_date > NOW())
                          ORDER BY p.created_at DESC
                          LIMIT :limit');
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }

    // Lấy bất động sản theo loại
    public function getPropertiesByType($typeId, $limit = 12) {
        $this->db->query('SELECT p.*, pt.name as type_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          WHERE p.type_id = :type_id AND p.status = "display" AND p.active = 1
                          AND (p.expiration_date IS NULL OR p.expiration_date > NOW())
                          ORDER BY p.created_at DESC
                          LIMIT :limit');
        $this->db->bind(':type_id', $typeId);
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }

    // Lấy bất động sản theo slug loại
    public function getPropertiesByTypeSlug($typeSlug, $limit = 12) {
        $this->db->query('SELECT p.*, pt.name as type_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          WHERE pt.slug = :type_slug AND p.status = "display" AND p.active = 1
                          AND (p.expiration_date IS NULL OR p.expiration_date > NOW())
                          ORDER BY p.created_at DESC
                          LIMIT :limit');
        $this->db->bind(':type_slug', $typeSlug);
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }

    // Lấy các bất động sản tương tự
    public function getSimilarProperties($typeId, $currentId, $limit = 4) {
        $this->db->query('SELECT p.*, pt.name as type_name, u.fullname as owner_name, w.name as ward_name
                          FROM properties p
                          LEFT JOIN property_types pt ON p.type_id = pt.id
                          LEFT JOIN users u ON p.user_id = u.id
                          LEFT JOIN wards w ON p.ward_id = w.id
                          WHERE p.type_id = :type_id AND p.id != :current_id AND p.status = "display" AND p.active = 1
                          AND (p.expiration_date IS NULL OR p.expiration_date > NOW())
                          ORDER BY p.featured DESC, p.created_at DESC
                          LIMIT :limit');

        $this->db->bind(':type_id', $typeId);
        $this->db->bind(':current_id', $currentId);
        $this->db->bind(':limit', $limit);

        return $this->db->resultSet();
    }

    // Lấy bất động sản theo loại và phường/xã (dùng slug)
    public function getPropertiesByTypeAndWard($typeSlug, $wardSlug, $price = '', $area = '', $bedrooms = '', $sort = 'default', $bathrooms = '', $direction = '') {
        // Xây dựng câu truy vấn cơ bản
        $sql = 'SELECT p.*, pt.name as type_name, pt.slug as type_slug, u.fullname as owner_name, w.name as ward_name, w.slug as ward_slug
                FROM properties p
                LEFT JOIN property_types pt ON p.type_id = pt.id
                LEFT JOIN users u ON p.user_id = u.id
                LEFT JOIN wards w ON p.ward_id = w.id
                WHERE p.status = "display" AND p.active = 1
                AND (p.expiration_date IS NULL OR p.expiration_date > NOW())';

        $params = [];

        // Thêm điều kiện tìm kiếm theo loại hình (chỉ khi có type được chọn và không phải là "nha-dat")
        if (!empty($typeSlug) && $typeSlug !== 'nha-dat') {
            $sql .= ' AND LOWER(pt.slug) = LOWER(:type_slug)';
            $params[':type_slug'] = $typeSlug;
            error_log('Filtering by type slug: ' . $typeSlug);
        } else if (!empty($typeSlug) && $typeSlug === 'nha-dat') {
            // Trường hợp đặc biệt cho "nha-dat" - không thêm điều kiện lọc theo loại hình
            error_log('Special case: type is "nha-dat", not filtering by type');
        }

        // Thêm điều kiện tìm kiếm theo phường/xã
        if (!empty($wardSlug)) {
            $sql .= ' AND LOWER(w.slug) = LOWER(:ward_slug)';
            $params[':ward_slug'] = $wardSlug;
            error_log('Filtering by ward slug: ' . $wardSlug);
        }

        // Thêm điều kiện tìm kiếm theo giá
        if (!empty($price)) {
            if (strpos($price, '-') !== false) {
                list($minPrice, $maxPrice) = explode('-', $price);
                // Chuyển đổi từ triệu sang đơn vị tiền tệ
                $minPrice = intval($minPrice) * 1000000;
                $maxPrice = intval($maxPrice) * 1000000;
                $sql .= ' AND p.price BETWEEN :min_price AND :max_price';
                $params[':min_price'] = $minPrice;
                $params[':max_price'] = $maxPrice;
                error_log("Price range: {$minPrice} - {$maxPrice}");
            } else if (strpos($price, '+') !== false) {
                // Xử lý trường hợp giá từ X triệu trở lên
                $minPrice = intval(str_replace('+', '', $price)) * 1000000;
                $sql .= ' AND p.price >= :min_price';
                $params[':min_price'] = $minPrice;
                error_log("Minimum price: {$minPrice}");
            }
        }

        // Thêm điều kiện tìm kiếm theo diện tích
        if (!empty($area)) {
            if (strpos($area, '-') !== false) {
                list($minArea, $maxArea) = explode('-', $area);
                $sql .= ' AND p.area BETWEEN :min_area AND :max_area';
                $params[':min_area'] = floatval($minArea);
                $params[':max_area'] = floatval($maxArea);
                error_log("Area range: {$minArea} - {$maxArea}");
            } else if (strpos($area, '+') !== false) {
                // Xử lý trường hợp diện tích từ X m2 trở lên
                $minArea = floatval(str_replace('+', '', $area));
                $sql .= ' AND p.area >= :min_area';
                $params[':min_area'] = $minArea;
                error_log("Minimum area: {$minArea}");
            }
        }

        // Thêm điều kiện tìm kiếm theo số phòng ngủ
        if (!empty($bedrooms)) {
            if (strpos($bedrooms, '+') !== false) {
                // Xử lý trường hợp từ X phòng ngủ trở lên
                $minBedrooms = intval(str_replace('+', '', $bedrooms));
                $sql .= ' AND p.bedrooms >= :min_bedrooms';
                $params[':min_bedrooms'] = $minBedrooms;
                error_log("Minimum bedrooms: {$minBedrooms}");
            } else {
                $sql .= ' AND p.bedrooms = :bedrooms';
                $params[':bedrooms'] = intval($bedrooms);
                error_log("Exact bedrooms: {$bedrooms}");
            }
        }

        // Thêm điều kiện tìm kiếm theo số phòng tắm
        if (!empty($bathrooms)) {
            if (strpos($bathrooms, '+') !== false) {
                // Xử lý trường hợp từ X phòng tắm trở lên
                $minBathrooms = intval(str_replace('+', '', $bathrooms));
                $sql .= ' AND p.bathrooms >= :min_bathrooms';
                $params[':min_bathrooms'] = $minBathrooms;
                error_log("Minimum bathrooms: {$minBathrooms}");
            } else {
                $sql .= ' AND p.bathrooms = :bathrooms';
                $params[':bathrooms'] = intval($bathrooms);
                error_log("Exact bathrooms: {$bathrooms}");
            }
        }

        // Thêm điều kiện tìm kiếm theo hướng nhà
        if (!empty($direction)) {
            $sql .= ' AND p.direction = :direction';
            $params[':direction'] = $direction;
            error_log("Direction: {$direction}");
        }

        // Thêm sắp xếp
        switch ($sort) {
            case 'price_asc':
                $sql .= ' ORDER BY p.price ASC';
                break;
            case 'price_desc':
                $sql .= ' ORDER BY p.price DESC';
                break;
            case 'area_asc':
                $sql .= ' ORDER BY p.area ASC';
                break;
            case 'area_desc':
                $sql .= ' ORDER BY p.area DESC';
                break;
            default:
                // Mặc định: Ưu tiên tin nổi bật, sau đó sắp xếp theo thời gian tạo mới nhất
                $sql .= ' ORDER BY p.featured DESC, p.created_at DESC';
                break;
        }

        // Thực hiện truy vấn
        $this->db->query($sql);

        // Bind các tham số
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }

        // Trả về kết quả
        return $this->db->resultSet();
    }

    // Lấy bất động sản theo user_id và trạng thái
    public function getUserProperties($userId, $status = 'all', $limit = 100) {
        $sql = 'SELECT p.*, pt.name as type_name, w.name as ward_name
                FROM properties p
                LEFT JOIN property_types pt ON p.type_id = pt.id
                LEFT JOIN wards w ON p.ward_id = w.id
                WHERE p.user_id = :user_id';

        // Thêm điều kiện lọc theo trạng thái
        switch ($status) {
            case 'active':
                // Tin đăng đang hoạt động (đã duyệt, hiển thị và chưa hết hạn)
                $sql .= ' AND p.active = 1 AND p.status = "display" AND (p.expiration_date IS NULL OR p.expiration_date > NOW())';
                break;
            case 'pending':
                // Tin đăng đang chờ duyệt
                $sql .= ' AND p.active = 0';
                break;
            case 'expired':
                // Tin đăng đã hết hạn
                $sql .= ' AND p.expiration_date IS NOT NULL AND p.expiration_date <= NOW()';
                break;
            case 'hidden':
                // Tin đăng đã ẩn hoặc bị từ chối
                $sql .= ' AND (p.status = "hide" OR p.active = 2)';
                break;
            case 'all':
            default:
                // Không thêm điều kiện, lấy tất cả
                break;
        }

        $sql .= ' ORDER BY p.created_at DESC LIMIT :limit';

        $this->db->query($sql);
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }

    // Đếm số lượng tin đăng theo user_id và trạng thái
    public function countUserProperties($userId, $status = 'all') {
        $sql = 'SELECT COUNT(*) as count FROM properties p WHERE p.user_id = :user_id';

        // Thêm điều kiện lọc theo trạng thái
        switch ($status) {
            case 'active':
                // Tin đăng đang hoạt động (đã duyệt, hiển thị và chưa hết hạn)
                $sql .= ' AND p.active = 1 AND p.status = "display" AND (p.expiration_date IS NULL OR p.expiration_date > NOW())';
                break;
            case 'pending':
                // Tin đăng đang chờ duyệt
                $sql .= ' AND p.active = 0';
                break;
            case 'expired':
                // Tin đăng đã hết hạn
                $sql .= ' AND p.expiration_date IS NOT NULL AND p.expiration_date <= NOW()';
                break;
            case 'hidden':
                // Tin đăng đã ẩn hoặc bị từ chối
                $sql .= ' AND (p.status = "hide" OR p.active = 2)';
                break;
            case 'all':
            default:
                // Không thêm điều kiện, lấy tất cả
                break;
        }

        $this->db->query($sql);
        $this->db->bind(':user_id', $userId);
        $result = $this->db->single();
        return $result->count;
    }

    // Lấy số lượng bất động sản theo user_id và trạng thái
    public function countPropertiesByUserIdAndStatus($userId, $status = null) {
        $sql = 'SELECT COUNT(*) as count FROM properties WHERE user_id = :user_id';

        if ($status) {
            $sql .= ' AND status = :status';
        }

        $this->db->query($sql);
        $this->db->bind(':user_id', $userId);

        if ($status) {
            $this->db->bind(':status', $status);
        }

        $result = $this->db->single();
        return $result->count;
    }

    // Cập nhật trạng thái và ngày hết hạn của bất động sản
    public function approveProperty($id) {
        // Tính ngày hết hạn (14 ngày từ ngày phê duyệt)
        $expirationDate = date('Y-m-d H:i:s', strtotime('+14 days'));
        $currentDate = date('Y-m-d H:i:s');

        $this->db->query('UPDATE properties SET
                          status = :status,
                          active = :active,
                          expiration_date = :expiration_date,
                          created_at = :created_at,
                          updated_at = :updated_at
                          WHERE id = :id');

        $this->db->bind(':id', $id);
        $this->db->bind(':status', 'display');
        $this->db->bind(':active', 1);
        $this->db->bind(':expiration_date', $expirationDate);
        $this->db->bind(':created_at', $currentDate);
        $this->db->bind(':updated_at', $currentDate);

        return $this->db->execute();
    }

    // Từ chối duyệt bất động sản
    public function rejectProperty($id) {
        $this->db->query('UPDATE properties SET
                          status = :status,
                          active = :active
                          WHERE id = :id');

        $this->db->bind(':id', $id);
        $this->db->bind(':status', 'hide');
        $this->db->bind(':active', 2); // 2 = từ chối

        return $this->db->execute();
    }

    // Gia hạn tin đăng bất động sản thêm 14 ngày
    public function extendProperty($id) {
        // Lấy thông tin bất động sản hiện tại
        $this->db->query('SELECT expiration_date FROM properties WHERE id = :id');
        $this->db->bind(':id', $id);
        $property = $this->db->single();

        if (!$property) {
            return false;
        }

        // Tính ngày hết hạn mới (thêm 14 ngày)
        $currentExpiration = $property->expiration_date;
        $now = date('Y-m-d H:i:s');

        // Nếu đã hết hạn, tính từ thời điểm hiện tại
        // Nếu chưa hết hạn, tính từ ngày hết hạn cũ
        $baseDate = (strtotime($currentExpiration) < strtotime($now)) ? $now : $currentExpiration;
        $newExpirationDate = date('Y-m-d H:i:s', strtotime($baseDate . ' +14 days'));

        // Cập nhật ngày hết hạn và đảm bảo trạng thái hiển thị
        $this->db->query('UPDATE properties SET
                          status = :status,
                          active = :active,
                          expiration_date = :expiration_date,
                          updated_at = NOW()
                          WHERE id = :id');

        $this->db->bind(':id', $id);
        $this->db->bind(':status', 'display');
        $this->db->bind(':active', 1);
        $this->db->bind(':expiration_date', $newExpirationDate);

        return $this->db->execute();
    }

    // Tìm kiếm bất động sản
    public function searchProperties($keyword = '', $type = '', $ward = '', $price = '', $sort = 'default', $bedrooms = '', $bathrooms = '', $direction = '', $area = '') {
        // Debug ward parameter
        error_log('Ward parameter received in searchProperties: "' . $ward . '"');
        error_log('All search parameters: keyword=' . $keyword . ', type=' . $type . ', ward=' . $ward .
                  ', price=' . $price . ', sort=' . $sort . ', bedrooms=' . $bedrooms .
                  ', bathrooms=' . $bathrooms . ', direction=' . $direction . ', area=' . $area);

        // Xử lý tìm kiếm theo từ khóa (keyword)
        if (!empty($keyword)) {
            error_log('Performing keyword search for: ' . $keyword);

            // Chuẩn bị câu truy vấn tìm kiếm toàn diện
            $sql = "SELECT p.*, pt.name as type_name, pt.slug as type_slug, u.fullname as owner_name, w.name as ward_name, w.slug as ward_slug
                    FROM properties p
                    LEFT JOIN property_types pt ON p.type_id = pt.id
                    LEFT JOIN users u ON p.user_id = u.id
                    LEFT JOIN wards w ON p.ward_id = w.id
                    WHERE p.status = 'display' AND p.active = 1
                    AND (p.expiration_date IS NULL OR p.expiration_date > NOW())";

            // Thêm điều kiện tìm kiếm theo loại hình nếu có
            if (!empty($type) && $type !== 'nha-dat') {
                $sql .= " AND LOWER(pt.slug) = LOWER(?)";
            }

            // Thêm điều kiện tìm kiếm theo phường/xã nếu có
            if (!empty($ward)) {
                $sql .= " AND LOWER(w.slug) = LOWER(?)";
            }

            // Thêm điều kiện tìm kiếm theo từ khóa (tìm trong title, description, street và address)
            $sql .= " AND (p.title LIKE ? OR p.description LIKE ? OR p.street LIKE ? OR p.address LIKE ?)";

            // Thêm điều kiện tìm kiếm theo giá nếu có
            if (!empty($price)) {
                if (strpos($price, '+') !== false) {
                    // Xử lý trường hợp từ X triệu trở lên (ví dụ: 15+)
                    $minPrice = intval(str_replace('+', '', $price)) * 1000000;
                    $sql .= " AND p.price >= ?";
                } else if (strpos($price, '-') !== false) {
                    // Xử lý trường hợp khoảng giá (ví dụ: 5-7)
                    list($minPrice, $maxPrice) = explode('-', $price);
                    $minPrice = intval($minPrice) * 1000000;
                    $maxPrice = intval($maxPrice) * 1000000;
                    $sql .= " AND p.price BETWEEN ? AND ?";
                }
            }

            // Thêm điều kiện tìm kiếm theo diện tích nếu có
            if (!empty($area)) {
                if (strpos($area, '+') !== false) {
                    $minArea = floatval(str_replace('+', '', $area));
                    $sql .= " AND p.area >= ?";
                } else if (strpos($area, '-') !== false) {
                    list($minArea, $maxArea) = explode('-', $area);
                    $sql .= " AND p.area BETWEEN ? AND ?";
                }
            }

            // Thêm điều kiện tìm kiếm theo số phòng ngủ nếu có
            if (!empty($bedrooms)) {
                if (strpos($bedrooms, '+') !== false) {
                    $minBedrooms = intval(str_replace('+', '', $bedrooms));
                    $sql .= " AND p.bedrooms >= ?";
                } else {
                    $sql .= " AND p.bedrooms = ?";
                }
            }

            // Thêm điều kiện tìm kiếm theo số phòng tắm nếu có
            if (!empty($bathrooms)) {
                if (strpos($bathrooms, '+') !== false) {
                    $minBathrooms = intval(str_replace('+', '', $bathrooms));
                    $sql .= " AND p.bathrooms >= ?";
                } else {
                    $sql .= " AND p.bathrooms = ?";
                }
            }

            // Thêm điều kiện tìm kiếm theo hướng nhà nếu có
            if (!empty($direction)) {
                $sql .= " AND p.direction = ?";
            }

            // Thêm sắp xếp
            switch ($sort) {
                case 'price_asc':
                    $sql .= " ORDER BY p.price ASC";
                    break;
                case 'price_desc':
                    $sql .= " ORDER BY p.price DESC";
                    break;
                case 'area_asc':
                    $sql .= " ORDER BY p.area ASC";
                    break;
                case 'area_desc':
                    $sql .= " ORDER BY p.area DESC";
                    break;
                default:
                    // Mặc định: Ưu tiên tin nổi bật, sau đó sắp xếp theo thời gian tạo mới nhất
                    $sql .= " ORDER BY p.featured DESC, p.created_at DESC";
                    break;
            }

            // Thực hiện truy vấn
            $this->db->query($sql);

            // Chuẩn bị tham số
            $paramIndex = 1;

            // Bind tham số cho type nếu có
            if (!empty($type) && $type !== 'nha-dat') {
                $this->db->bind($paramIndex++, $type);
            }

            // Bind tham số cho ward nếu có
            if (!empty($ward)) {
                $this->db->bind($paramIndex++, $ward);
            }

            // Bind tham số cho keyword (4 lần cho 4 trường tìm kiếm)
            $keywordParam = '%' . $keyword . '%';
            $this->db->bind($paramIndex++, $keywordParam);
            $this->db->bind($paramIndex++, $keywordParam);
            $this->db->bind($paramIndex++, $keywordParam);
            $this->db->bind($paramIndex++, $keywordParam);

            // Bind tham số cho price nếu có
            if (!empty($price)) {
                if (strpos($price, '+') !== false) {
                    // Xử lý trường hợp từ X triệu trở lên (ví dụ: 15+)
                    $minPrice = intval(str_replace('+', '', $price)) * 1000000;
                    $this->db->bind($paramIndex++, $minPrice);
                } else if (strpos($price, '-') !== false) {
                    // Xử lý trường hợp khoảng giá (ví dụ: 5-7)
                    list($minPrice, $maxPrice) = explode('-', $price);
                    $minPrice = intval($minPrice) * 1000000;
                    $maxPrice = intval($maxPrice) * 1000000;
                    $this->db->bind($paramIndex++, $minPrice);
                    $this->db->bind($paramIndex++, $maxPrice);
                }
            }

            // Bind tham số cho area nếu có
            if (!empty($area)) {
                if (strpos($area, '+') !== false) {
                    $minArea = floatval(str_replace('+', '', $area));
                    $this->db->bind($paramIndex++, $minArea);
                } else if (strpos($area, '-') !== false) {
                    list($minArea, $maxArea) = explode('-', $area);
                    $this->db->bind($paramIndex++, floatval($minArea));
                    $this->db->bind($paramIndex++, floatval($maxArea));
                }
            }

            // Bind tham số cho bedrooms nếu có
            if (!empty($bedrooms)) {
                if (strpos($bedrooms, '+') !== false) {
                    $minBedrooms = intval(str_replace('+', '', $bedrooms));
                    $this->db->bind($paramIndex++, $minBedrooms);
                } else {
                    $this->db->bind($paramIndex++, intval($bedrooms));
                }
            }

            // Bind tham số cho bathrooms nếu có
            if (!empty($bathrooms)) {
                if (strpos($bathrooms, '+') !== false) {
                    $minBathrooms = intval(str_replace('+', '', $bathrooms));
                    $this->db->bind($paramIndex++, $minBathrooms);
                } else {
                    $this->db->bind($paramIndex++, intval($bathrooms));
                }
            }

            // Bind tham số cho direction nếu có
            if (!empty($direction)) {
                $this->db->bind($paramIndex++, $direction);
            }

            error_log('Comprehensive keyword search SQL: ' . $sql);
            error_log('Total parameters bound: ' . ($paramIndex - 1));

            try {
                // Lấy kết quả
                $results = $this->db->resultSet();
                error_log('Number of results: ' . count($results));
                return $results;
            } catch (Exception $e) {
                error_log('Error executing query: ' . $e->getMessage());
                return [];
            }
        }

        // Xử lý trường hợp không có từ khóa (keyword)
        // Chuẩn bị câu truy vấn cơ bản
        $sql = "SELECT p.*, pt.name as type_name, pt.slug as type_slug, u.fullname as owner_name, w.name as ward_name, w.slug as ward_slug
                FROM properties p
                LEFT JOIN property_types pt ON p.type_id = pt.id
                LEFT JOIN users u ON p.user_id = u.id
                LEFT JOIN wards w ON p.ward_id = w.id
                WHERE p.status = 'display' AND p.active = 1
                AND (p.expiration_date IS NULL OR p.expiration_date > NOW())";

        // Sử dụng tham số dạng ? thay vì tham số có tên
        $paramIndex = 1;

        // Thêm điều kiện tìm kiếm theo loại hình
        if (!empty($type) && $type !== 'nha-dat') {
            $sql .= " AND LOWER(pt.slug) = LOWER(?)";
            $typeParams[] = $type;
            error_log('Searching for type: ' . $type);
        } else if (!empty($type) && $type === 'nha-dat') {
            // Trường hợp đặc biệt cho "nha-dat" - không thêm điều kiện lọc theo loại hình
            error_log('Special case: type is "nha-dat", not filtering by type');
        }

        // Thêm điều kiện tìm kiếm theo phường/xã - Đơn giản hóa logic
        if (!empty($ward)) {
            error_log('Searching for ward with slug: "' . $ward . '"');
            $sql .= " AND LOWER(w.slug) = LOWER(?)";
            $wardParams[] = $ward;
        }

        // Thêm điều kiện tìm kiếm theo giá
        if (!empty($price) && strpos($price, '-') !== false) {
            list($minPrice, $maxPrice) = explode('-', $price);
            $minPrice = intval($minPrice) * 1000000;
            if ($maxPrice === '+') {
                $sql .= " AND p.price >= ?";
                $priceParams[] = $minPrice;
                error_log("Minimum price: {$minPrice}");
            } else {
                $maxPrice = intval($maxPrice) * 1000000;
                $sql .= " AND p.price BETWEEN ? AND ?";
                $priceParams[] = $minPrice;
                $priceParams[] = $maxPrice;
                error_log("Price range: {$minPrice} - {$maxPrice}");
            }
        }

        // Thêm điều kiện tìm kiếm theo diện tích
        if (!empty($area)) {
            if (strpos($area, '+') !== false) {
                // Xử lý trường hợp từ X m2 trở lên
                $minArea = floatval(str_replace('+', '', $area));
                $sql .= " AND p.area >= ?";
                $areaParams[] = $minArea;
                error_log("Minimum area: {$minArea}");
            } else if (strpos($area, '-') !== false) {
                list($minArea, $maxArea) = explode('-', $area);
                $sql .= " AND p.area BETWEEN ? AND ?";
                $areaParams[] = floatval($minArea);
                $areaParams[] = floatval($maxArea);
                error_log("Area range: {$minArea} - {$maxArea}");
            }
        }

        // Thêm điều kiện tìm kiếm theo số phòng ngủ
        if (!empty($bedrooms)) {
            if (strpos($bedrooms, '+') !== false) {
                // Xử lý trường hợp từ X phòng ngủ trở lên
                $minBedrooms = intval(str_replace('+', '', $bedrooms));
                $sql .= " AND p.bedrooms >= ?";
                $bedroomsParams[] = $minBedrooms;
                error_log("Minimum bedrooms: {$minBedrooms}");
            } else {
                $sql .= " AND p.bedrooms = ?";
                $bedroomsParams[] = intval($bedrooms);
                error_log("Exact bedrooms: {$bedrooms}");
            }
        }

        // Thêm điều kiện tìm kiếm theo số phòng tắm
        if (!empty($bathrooms)) {
            if (strpos($bathrooms, '+') !== false) {
                // Xử lý trường hợp từ X phòng tắm trở lên
                $minBathrooms = intval(str_replace('+', '', $bathrooms));
                $sql .= " AND p.bathrooms >= ?";
                $bathroomsParams[] = $minBathrooms;
                error_log("Minimum bathrooms: {$minBathrooms}");
            } else {
                $sql .= " AND p.bathrooms = ?";
                $bathroomsParams[] = intval($bathrooms);
                error_log("Exact bathrooms: {$bathrooms}");
            }
        }

        // Thêm điều kiện tìm kiếm theo hướng nhà
        if (!empty($direction)) {
            $sql .= " AND p.direction = ?";
            $directionParams[] = $direction;
            error_log("Direction: {$direction}");
        }

        // Thêm sắp xếp
        switch ($sort) {
            case 'price_asc':
                $sql .= " ORDER BY p.price ASC";
                break;
            case 'price_desc':
                $sql .= " ORDER BY p.price DESC";
                break;
            case 'area_asc':
                $sql .= " ORDER BY p.area ASC";
                break;
            case 'area_desc':
                $sql .= " ORDER BY p.area DESC";
                break;
            default:
                // Mặc định: Ưu tiên tin nổi bật, sau đó sắp xếp theo thời gian tạo mới nhất
                $sql .= " ORDER BY p.featured DESC, p.created_at DESC";
                break;
        }

        // Thực hiện truy vấn
        $this->db->query($sql);

        // Bind các tham số theo thứ tự
        $paramIndex = 1;

        // Bind tham số cho type nếu có
        if (!empty($type) && $type !== 'nha-dat') {
            $this->db->bind($paramIndex++, $type);
        }

        // Bind tham số cho ward nếu có
        if (!empty($ward)) {
            $this->db->bind($paramIndex++, $ward);
        }

        // Bind tham số cho price nếu có
        if (!empty($price) && strpos($price, '-') !== false) {
            list($minPrice, $maxPrice) = explode('-', $price);
            $minPrice = intval($minPrice) * 1000000;
            if ($maxPrice === '+') {
                $this->db->bind($paramIndex++, $minPrice);
            } else {
                $maxPrice = intval($maxPrice) * 1000000;
                $this->db->bind($paramIndex++, $minPrice);
                $this->db->bind($paramIndex++, $maxPrice);
            }
        }

        // Bind tham số cho area nếu có
        if (!empty($area)) {
            if (strpos($area, '+') !== false) {
                $minArea = floatval(str_replace('+', '', $area));
                $this->db->bind($paramIndex++, $minArea);
            } else if (strpos($area, '-') !== false) {
                list($minArea, $maxArea) = explode('-', $area);
                $this->db->bind($paramIndex++, floatval($minArea));
                $this->db->bind($paramIndex++, floatval($maxArea));
            }
        }

        // Bind tham số cho bedrooms nếu có
        if (!empty($bedrooms)) {
            if (strpos($bedrooms, '+') !== false) {
                $minBedrooms = intval(str_replace('+', '', $bedrooms));
                $this->db->bind($paramIndex++, $minBedrooms);
            } else {
                $this->db->bind($paramIndex++, intval($bedrooms));
            }
        }

        // Bind tham số cho bathrooms nếu có
        if (!empty($bathrooms)) {
            if (strpos($bathrooms, '+') !== false) {
                $minBathrooms = intval(str_replace('+', '', $bathrooms));
                $this->db->bind($paramIndex++, $minBathrooms);
            } else {
                $this->db->bind($paramIndex++, intval($bathrooms));
            }
        }

        // Bind tham số cho direction nếu có
        if (!empty($direction)) {
            $this->db->bind($paramIndex++, $direction);
        }

        error_log('Standard search SQL: ' . $sql);
        error_log('Total parameters bound: ' . ($paramIndex - 1));

        try {
            // Lấy kết quả
            $results = $this->db->resultSet();
            error_log('Number of results: ' . count($results));
            return $results;
        } catch (Exception $e) {
            error_log('Error executing query: ' . $e->getMessage());
            return [];
        }
    }

    // Lấy gợi ý tìm kiếm cho mobile
    public function getSearchSuggestions($query, $limit = 5) {
        $suggestions = [];

        try {
            // Tìm kiếm trong title và street của properties
            $sql = "SELECT DISTINCT p.title, p.street, w.name as ward_name
                    FROM properties p
                    LEFT JOIN wards w ON p.ward_id = w.id
                    WHERE p.status = 'display' AND p.active = 1
                    AND (p.expiration_date IS NULL OR p.expiration_date > NOW())
                    AND (p.title LIKE ? OR p.street LIKE ? OR w.name LIKE ?)
                    ORDER BY p.featured DESC, p.created_at DESC
                    LIMIT ?";

            $this->db->query($sql);
            $searchTerm = '%' . $query . '%';
            $this->db->bind(1, $searchTerm);
            $this->db->bind(2, $searchTerm);
            $this->db->bind(3, $searchTerm);
            $this->db->bind(4, $limit);

            $results = $this->db->resultSet();

            // Format suggestions
            foreach ($results as $result) {
                if (stripos($result->title, $query) !== false) {
                    $suggestions[] = [
                        'text' => $result->title,
                        'type' => 'property'
                    ];
                } else if (stripos($result->street, $query) !== false) {
                    $suggestions[] = [
                        'text' => $result->street . ', ' . $result->ward_name,
                        'type' => 'location'
                    ];
                } else if (stripos($result->ward_name, $query) !== false) {
                    $suggestions[] = [
                        'text' => $result->ward_name,
                        'type' => 'ward'
                    ];
                }
            }

            // Remove duplicates
            $suggestions = array_unique($suggestions, SORT_REGULAR);

            // Limit results
            $suggestions = array_slice($suggestions, 0, $limit);

        } catch (Exception $e) {
            error_log('Error getting search suggestions: ' . $e->getMessage());
        }

        return $suggestions;
    }


}
