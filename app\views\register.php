<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="container mt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/thuenhadanang" class="text-decoration-none">Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page">Đ<PERSON>ng ký</li>
    </ol>
</nav>

<!-- Register Form -->
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card border-0 shadow">
                <div class="card-body p-4">
                    <!-- Logo và tiêu đề -->
                    <div class="text-center mb-4">
                        <h1 class="h3 text-primary fw-bold mb-2">Đăng ký tài khoản</h1>
                        <p class="text-secondary mb-4">Tạo tài khoản để đăng tin và quản lý bất động sản</p>
                    </div>

                    <!-- Hi<PERSON>n thị thông báo lỗi nếu có -->
                    <?php if (!empty($data['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $data['error']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endif; ?>

                    <!-- Form đăng ký -->
                    <form action="/thuenhadanang/register/create" method="POST">
                        <!-- Họ tên -->
                        <div class="mb-3">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="fullname" name="fullname" placeholder="Nguyễn Văn A" required>
                                <label for="fullname">
                                    <i class="bi bi-person me-2"></i>
                                    Họ tên
                                </label>
                            </div>
                        </div>

                        <!-- Email -->
                        <div class="mb-3">
                            <div class="form-floating">
                                <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                                <label for="email">
                                    <i class="bi bi-envelope me-2"></i>
                                    Email
                                </label>
                            </div>
                        </div>

                        <!-- Mật khẩu -->
                        <div class="mb-3">
                            <div class="form-floating">
                                <input type="password" class="form-control" id="password" name="password" placeholder="••••••••" required>
                                <label for="password">
                                    <i class="bi bi-lock me-2"></i>
                                    Mật khẩu
                                </label>
                                <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y text-decoration-none pe-3" onclick="togglePassword('password')">
                                    <i class="bi bi-eye text-muted"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Xác nhận mật khẩu -->
                        <div class="mb-4">
                            <div class="form-floating">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="••••••••" required>
                                <label for="confirm_password">
                                    <i class="bi bi-lock-fill me-2"></i>
                                    Xác nhận mật khẩu
                                </label>
                                <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y text-decoration-none pe-3" onclick="togglePassword('confirm_password')">
                                    <i class="bi bi-eye text-muted"></i>
                                </button>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">Đăng ký</button>

                        <div class="text-center">
                            <p class="text-secondary mb-3">Hoặc đăng ký với</p>
                            <div class="d-flex gap-2 justify-content-center mb-4">
                                <button type="button" class="btn btn-outline-secondary flex-grow-1 d-flex align-items-center justify-content-center">
                                    <i class="bi bi-google me-2"></i>Google
                                </button>
                                <button type="button" class="btn btn-outline-secondary flex-grow-1 d-flex align-items-center justify-content-center">
                                    <i class="bi bi-facebook me-2"></i>Facebook
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Đăng nhập -->
                    <div class="text-center">
                        <p class="mb-0 text-secondary">Đã có tài khoản? 
                            <a href="/thuenhadanang/login" class="text-decoration-none text-primary">Đăng nhập</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Script để toggle password visibility -->
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = event.currentTarget.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
    }
}</script> 