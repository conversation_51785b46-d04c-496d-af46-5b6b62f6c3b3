<?php

// Test sort URL fix
define('BASE_PATH', __DIR__);
define('APP_PATH', BASE_PATH . '/app');

// Load required files
require_once APP_PATH . '/libraries/Database.php';
require_once APP_PATH . '/libraries/UrlHandler.php';
require_once APP_PATH . '/models/PropertyType.php';
require_once APP_PATH . '/models/Ward.php';
require_once APP_PATH . '/models/Property.php';
require_once APP_PATH . '/controllers/SearchController.php';

echo "<h1>Test Sort URL Fix</h1>\n";

try {
    // Simulate the problematic scenario
    echo "<h2>1. Simulating Problematic URL</h2>\n";
    
    // Simulate $_SERVER and $_GET for the problematic case
    $_SERVER['REQUEST_URI'] = '/thuenhadanang/cho-thue-can-ho?url=cho-thue-can-ho&sort=price_desc';
    $_GET = [
        'url' => 'cho-thue-can-ho',
        'sort' => 'price_desc'
    ];
    
    echo "<p><strong>Current URL:</strong> " . $_SERVER['REQUEST_URI'] . "</p>\n";
    echo "<p><strong>Current \$_GET:</strong> " . json_encode($_GET) . "</p>\n";
    
    // Create SearchController instance
    $searchController = new SearchController();
    
    echo "<h2>2. Testing buildSortUrl Method</h2>\n";
    
    // Test different sort options
    $sortOptions = [
        'default' => 'Phổ biến nhất',
        'price_asc' => 'Giá thấp đến cao',
        'price_desc' => 'Giá cao đến thấp',
        'area_asc' => 'Diện tích nhỏ đến lớn',
        'area_desc' => 'Diện tích lớn đến nhỏ'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Sort Option</th><th>Generated URL</th><th>Contains 'url='?</th><th>Status</th></tr>\n";
    
    foreach ($sortOptions as $sortKey => $sortLabel) {
        $generatedUrl = $searchController->buildSortUrl($sortKey);
        $containsUrl = (strpos($generatedUrl, 'url=') !== false);
        $status = $containsUrl ? '❌ FAIL' : '✅ PASS';
        $statusColor = $containsUrl ? 'red' : 'green';
        
        echo "<tr>\n";
        echo "<td>$sortLabel ($sortKey)</td>\n";
        echo "<td><code>" . htmlspecialchars($generatedUrl) . "</code></td>\n";
        echo "<td>" . ($containsUrl ? 'Yes' : 'No') . "</td>\n";
        echo "<td style='color: $statusColor; font-weight: bold;'>$status</td>\n";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    echo "<h2>3. Expected vs Actual Results</h2>\n";
    
    $expectedUrl = '/thuenhadanang/cho-thue-can-ho?sort=price_desc';
    $actualUrl = $searchController->buildSortUrl('price_desc');
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<h4>Expected URL:</h4>\n";
    echo "<code style='background: #e9ecef; padding: 5px; border-radius: 3px;'>" . htmlspecialchars($expectedUrl) . "</code>\n";
    echo "</div>\n";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<h4>Actual URL:</h4>\n";
    echo "<code style='background: #e9ecef; padding: 5px; border-radius: 3px;'>" . htmlspecialchars($actualUrl) . "</code>\n";
    echo "</div>\n";
    
    $isCorrect = ($actualUrl === $expectedUrl);
    $resultColor = $isCorrect ? 'green' : 'red';
    $resultText = $isCorrect ? '✅ CORRECT' : '❌ INCORRECT';
    
    echo "<div style='background: " . ($isCorrect ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($isCorrect ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h4 style='color: $resultColor;'>Result: $resultText</h4>\n";
    if (!$isCorrect) {
        echo "<p style='color: $resultColor;'>The URL still contains unwanted parameters.</p>\n";
    } else {
        echo "<p style='color: $resultColor;'>The URL is clean and correct!</p>\n";
    }
    echo "</div>\n";
    
    echo "<h2>4. Testing Different Scenarios</h2>\n";
    
    $testScenarios = [
        [
            'name' => 'Clean URL (no extra params)',
            'request_uri' => '/thuenhadanang/cho-thue-can-ho',
            'get_params' => []
        ],
        [
            'name' => 'URL with keyword',
            'request_uri' => '/thuenhadanang/cho-thue-can-ho?keyword=test',
            'get_params' => ['keyword' => 'test']
        ],
        [
            'name' => 'URL with bathrooms',
            'request_uri' => '/thuenhadanang/cho-thue-can-ho?bathrooms=2',
            'get_params' => ['bathrooms' => '2']
        ],
        [
            'name' => 'URL with multiple valid params',
            'request_uri' => '/thuenhadanang/cho-thue-can-ho?keyword=test&bathrooms=2',
            'get_params' => ['keyword' => 'test', 'bathrooms' => '2']
        ],
        [
            'name' => 'URL with unwanted params',
            'request_uri' => '/thuenhadanang/cho-thue-can-ho?url=cho-thue-can-ho&action=search&controller=SearchController',
            'get_params' => ['url' => 'cho-thue-can-ho', 'action' => 'search', 'controller' => 'SearchController']
        ]
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Scenario</th><th>Input</th><th>Generated Sort URL</th><th>Clean?</th></tr>\n";
    
    foreach ($testScenarios as $scenario) {
        // Set up the scenario
        $_SERVER['REQUEST_URI'] = $scenario['request_uri'];
        $_GET = $scenario['get_params'];
        
        // Create new controller instance for each test
        $testController = new SearchController();
        $sortUrl = $testController->buildSortUrl('price_desc');
        
        // Check if URL is clean (no unwanted params)
        $unwantedParams = ['url=', 'action=', 'controller='];
        $isClean = true;
        foreach ($unwantedParams as $param) {
            if (strpos($sortUrl, $param) !== false) {
                $isClean = false;
                break;
            }
        }
        
        $cleanStatus = $isClean ? '✅ Yes' : '❌ No';
        $cleanColor = $isClean ? 'green' : 'red';
        
        echo "<tr>\n";
        echo "<td>{$scenario['name']}</td>\n";
        echo "<td><code>" . htmlspecialchars($scenario['request_uri']) . "</code></td>\n";
        echo "<td><code>" . htmlspecialchars($sortUrl) . "</code></td>\n";
        echo "<td style='color: $cleanColor; font-weight: bold;'>$cleanStatus</td>\n";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    echo "<h2>5. Summary</h2>\n";
    
    // Count how many scenarios passed
    $passedCount = 0;
    $totalCount = count($testScenarios);
    
    foreach ($testScenarios as $scenario) {
        $_SERVER['REQUEST_URI'] = $scenario['request_uri'];
        $_GET = $scenario['get_params'];
        
        $testController = new SearchController();
        $sortUrl = $testController->buildSortUrl('price_desc');
        
        $unwantedParams = ['url=', 'action=', 'controller='];
        $isClean = true;
        foreach ($unwantedParams as $param) {
            if (strpos($sortUrl, $param) !== false) {
                $isClean = false;
                break;
            }
        }
        
        if ($isClean) {
            $passedCount++;
        }
    }
    
    $successRate = ($passedCount / $totalCount) * 100;
    $summaryColor = ($passedCount === $totalCount) ? 'green' : 'red';
    $summaryStatus = ($passedCount === $totalCount) ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED';
    
    echo "<div style='background: " . (($passedCount === $totalCount) ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . (($passedCount === $totalCount) ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h4 style='color: $summaryColor;'>$summaryStatus</h4>\n";
    echo "<p style='color: $summaryColor;'><strong>Success Rate:</strong> $passedCount/$totalCount ($successRate%)</p>\n";
    
    if ($passedCount === $totalCount) {
        echo "<p style='color: $summaryColor;'>🎉 All sort URLs are now clean and free of unwanted parameters!</p>\n";
    } else {
        echo "<p style='color: $summaryColor;'>⚠️ Some URLs still contain unwanted parameters. Please check the implementation.</p>\n";
    }
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

?>
