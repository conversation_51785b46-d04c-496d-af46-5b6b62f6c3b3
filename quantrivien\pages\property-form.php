<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mt-2"><?php echo $action == 'add' ? 'Thêm mới' : 'Cập nhật'; ?> bất động sản</h1>
        <a href="index.php?page=properties" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-1"></i> Quay lại
        </a>
    </div>

    <?php
    // Lấy thông báo từ session (nếu có)
    if (isset($_SESSION['message']) && isset($_SESSION['messageType'])) {
        $message = $_SESSION['message'];
        $messageType = $_SESSION['messageType'];

        // Xóa thông báo khỏi session sau khi đã lấy
        unset($_SESSION['message']);
        unset($_SESSION['messageType']);
    }

    // Hiển thị thông báo
    if (isset($message) && !empty($message)):
    ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-house-fill me-1"></i>
            Thông tin bất động sản
        </div>
        <div class="card-body">
            <form action="index.php?page=properties&action=<?php echo $action; ?><?php echo $action == 'edit' ? '&id=' . $id : ''; ?>" method="POST" enctype="multipart/form-data">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="title" class="form-label">Tiêu đề <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" required
                               value="<?php echo $action == 'edit' ? htmlspecialchars($editProperty->title) : ''; ?>">
                        <div class="form-text">Slug sẽ được tự động tạo từ tiêu đề khi lưu.</div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Mô tả <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="description" name="description" rows="5" required><?php echo $action == 'edit' ? htmlspecialchars($editProperty->description) : ''; ?></textarea>
                </div>

                <h5 class="mt-4 mb-3">Khu vực</h5>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="type_id" class="form-label">Loại hình <span class="text-danger">*</span></label>
                        <select class="form-select" id="type_id" name="type_id" required>
                            <option value="">-- Chọn loại hình --</option>
                            <?php foreach ($propertyTypes as $type): ?>
                                <option value="<?php echo $type->id; ?>" <?php echo ($action == 'edit' && $editProperty->type_id == $type->id) ? 'selected' : ''; ?>>
                                    <?php echo $type->name; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="ward_id" class="form-label">Phường / Xã <span class="text-danger">*</span></label>
                        <select class="form-select ward-select" id="ward_id" name="ward_id" required>
                            <option value="">-- Chọn phường / xã --</option>
                            <?php foreach ($wards as $ward): ?>
                                <option value="<?php echo $ward->id; ?>" <?php echo ($action == 'edit' && $editProperty->ward_id == $ward->id) ? 'selected' : ''; ?>>
                                    <?php echo $ward->name; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="street" class="form-label">Đường phố <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="street" name="street" required
                               value="<?php echo $action == 'edit' ? htmlspecialchars($editProperty->street ?? '') : ''; ?>">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="address" class="form-label">Địa chỉ đầy đủ <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="address" name="address" required
                           value="<?php echo $action == 'edit' ? htmlspecialchars($editProperty->address) : ''; ?>">
                </div>

                <h5 class="mt-4 mb-3">Thông tin chi tiết</h5>
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="price" class="form-label">Giá cho thuê (VNĐ) <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="price" name="price" required
                               value="<?php echo $action == 'edit' ? $editProperty->price : ''; ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="price_period" class="form-label">Thời gian <span class="text-danger">*</span></label>
                        <select class="form-select" id="price_period" name="price_period" required>
                            <option value="day" <?php echo ($action == 'edit' && $editProperty->price_period == 'day') ? 'selected' : ''; ?>>Ngày</option>
                            <option value="week" <?php echo ($action == 'edit' && $editProperty->price_period == 'week') ? 'selected' : ''; ?>>Tuần</option>
                            <option value="month" <?php echo ($action == 'edit' && $editProperty->price_period == 'month') ? 'selected' : ''; ?><?php echo $action == 'add' ? 'selected' : ''; ?>>Tháng</option>
                            <option value="quarter" <?php echo ($action == 'edit' && $editProperty->price_period == 'quarter') ? 'selected' : ''; ?>>Quý</option>
                            <option value="year" <?php echo ($action == 'edit' && $editProperty->price_period == 'year') ? 'selected' : ''; ?>>Năm</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="area" class="form-label">Diện tích (m²)</label>
                        <input type="number" step="0.01" class="form-control" id="area" name="area"
                               value="<?php echo $action == 'edit' ? $editProperty->area : ''; ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="direction" class="form-label">Hướng chính</label>
                        <select class="form-select" id="direction" name="direction">
                            <option value="">-- Chọn hướng --</option>
                            <?php foreach ($directions as $direction): ?>
                                <option value="<?php echo $direction->slug; ?>" <?php echo ($action == 'edit' && $editProperty->direction == $direction->slug) ? 'selected' : ''; ?>>
                                    <?php echo $direction->name; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <!-- Thông tin trạng thái và thời gian -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Thông tin trạng thái</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="active" class="form-label">Trạng thái kích hoạt <span class="text-danger">*</span></label>
                                <select class="form-select" id="active" name="active" required>
                                    <option value="0" <?php echo ($action == 'edit' && $editProperty->active == 0) ? 'selected' : ''; ?><?php echo $action == 'add' ? 'selected' : ''; ?>>Chưa kích hoạt (đang chờ duyệt)</option>
                                    <option value="1" <?php echo ($action == 'edit' && $editProperty->active == 1) ? 'selected' : ''; ?>>Đã kích hoạt (đã được duyệt)</option>
                                    <option value="2" <?php echo ($action == 'edit' && $editProperty->active == 2) ? 'selected' : ''; ?>>Từ chối (bị từ chối duyệt)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="status" class="form-label">Trạng thái hiển thị <span class="text-danger">*</span></label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="display" <?php echo ($action == 'edit' && $editProperty->status == 'display') ? 'selected' : ''; ?><?php echo ($action == 'edit' && $editProperty->status == 'available') ? 'selected' : ''; ?><?php echo $action == 'add' ? 'selected' : ''; ?>>Hiển thị</option>
                                    <option value="hide" <?php echo ($action == 'edit' && $editProperty->status == 'hide') ? 'selected' : ''; ?><?php echo ($action == 'edit' && ($editProperty->status == 'rented' || $editProperty->status == 'pending')) ? 'selected' : ''; ?>>Ẩn</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="created_at" class="form-label">Ngày đăng</label>
                                <input type="datetime-local" class="form-control" id="created_at" name="created_at"
                                       value="<?php echo $action == 'edit' && $editProperty->created_at ? date('Y-m-d\TH:i', strtotime($editProperty->created_at)) : date('Y-m-d\TH:i'); ?>">
                                <div class="form-text">Để trống sẽ sử dụng thời gian hiện tại.</div>
                            </div>
                            <div class="col-md-6">
                                <label for="expiration_date" class="form-label">Ngày hết hạn</label>
                                <input type="datetime-local" class="form-control" id="expiration_date" name="expiration_date"
                                       value="<?php echo $action == 'edit' && $editProperty->expiration_date ? date('Y-m-d\TH:i', strtotime($editProperty->expiration_date)) : date('Y-m-d\TH:i', strtotime('+14 days')); ?>">
                                <div class="form-text">Để trống sẽ tự động tính ngày hết hạn là 14 ngày kể từ ngày đăng.</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6" id="user_id_container">
                                <label for="user_id" class="form-label">Chủ sở hữu <span class="text-danger user-required">*</span></label>
                                <select class="form-select user-select" id="user_id" name="user_id">
                                    <option value="">-- Chọn chủ sở hữu --</option>
                                    <?php if ($action == 'edit' && $editProperty->user_id == 1): ?>
                                    <option value="1" selected>
                                        Guest (Đăng tin free)
                                    </option>
                                    <?php endif; ?>
                                    <?php foreach ($users as $user): ?>
                                        <?php if ($user->id != 1): // Bỏ qua user Guest ?>
                                            <option value="<?php echo $user->id; ?>" <?php echo ($action == 'edit' && $editProperty->user_id == $user->id) ? 'selected' : ''; ?>>
                                                <?php echo $user->fullname; ?> (<?php echo $user->email; ?>)
                                            </option>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="featured" name="featured" <?php echo ($action == 'edit' && $editProperty->featured) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="featured">
                                        Đánh dấu là bất động sản nổi bật
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_guest" name="is_guest" <?php echo ($action == 'edit' && $editProperty->user_id == 1) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_guest">
                                    Đăng tin free (không hiển thị chủ sở hữu)
                                </label>
                                <div class="form-text">Khi chọn đăng tin free, tin đăng sẽ được đăng dưới dạng Guest và cần nhập thông tin liên hệ.</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="bedrooms" class="form-label">Số phòng ngủ</label>
                        <input type="number" class="form-control" id="bedrooms" name="bedrooms"
                               value="<?php echo $action == 'edit' ? $editProperty->bedrooms : ''; ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="bathrooms" class="form-label">Số toilet</label>
                        <input type="number" class="form-control" id="bathrooms" name="bathrooms"
                               value="<?php echo $action == 'edit' ? $editProperty->bathrooms : ''; ?>">
                    </div>
                    <div class="col-md-6">
                        <label for="video_url" class="form-label">Video URL (YouTube)</label>
                        <input type="url" class="form-control" id="video_url" name="video_url"
                               value="<?php echo $action == 'edit' ? htmlspecialchars($editProperty->video_url ?? '') : ''; ?>"
                               placeholder="Ví dụ: https://www.youtube.com/watch?v=...">
                        <div class="form-text">Nhập đường dẫn video YouTube.</div>
                    </div>
                </div>

                <!-- Thông tin liên hệ cho tin đăng free -->
                <div id="contact_info_container" style="display: none;">
                    <h5 class="mt-4 mb-3">Thông tin liên hệ</h5>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="contact_name" class="form-label">Tên liên hệ <span class="text-danger contact-required">*</span></label>
                            <input type="text" class="form-control" id="contact_name" name="contact_name"
                                   value="<?php echo isset($contactInfo) ? htmlspecialchars($contactInfo->name) : ''; ?>">
                        </div>
                        <div class="col-md-6">
                            <label for="contact_phone" class="form-label">Số điện thoại <span class="text-danger contact-required">*</span></label>
                            <input type="text" class="form-control" id="contact_phone" name="contact_phone"
                                   value="<?php echo isset($contactInfo) ? htmlspecialchars($contactInfo->phone) : ''; ?>">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="contact_zalo" class="form-label">Zalo</label>
                            <input type="text" class="form-control" id="contact_zalo" name="contact_zalo"
                                   value="<?php echo isset($contactInfo) ? htmlspecialchars($contactInfo->zalo) : ''; ?>">
                        </div>
                        <div class="col-md-6">
                            <label for="contact_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="contact_email" name="contact_email"
                                   value="<?php echo isset($contactInfo) ? htmlspecialchars($contactInfo->email) : ''; ?>">
                        </div>
                    </div>
                </div>

                <!-- Phần upload hình ảnh đã được chuyển sang mục "Quản lý hình ảnh" -->

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" name="save_property" class="btn btn-primary">
                        <i class="bi bi-save me-1"></i> <?php echo $action == 'add' ? 'Thêm mới' : 'Cập nhật'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <?php if ($action == 'edit'): ?>
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-images me-1"></i>
                Quản lý hình ảnh
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h5>Upload hình ảnh</h5>
                        <p class="text-muted">
                            <small>
                                <i class="bi bi-info-circle me-1"></i>
                                Upload tối đa 10 file, giới hạn mỗi file 1MB, kích thước ảnh phải lớn hơn 300x300 pixels.
                            </small>
                        </p>
                        <div class="alert alert-success">
                            <i class="bi bi-lightning-charge-fill me-1"></i>
                            <strong>Tối ưu hóa tự động:</strong> Hình ảnh sẽ được nén tự động với chất lượng 80%, chiều rộng tối đa 1000px trước khi upload để tiết kiệm dung lượng và tăng tốc độ tải trang.
                        </div>
                        <form action="index.php?page=properties&action=edit&id=<?php echo $id; ?>" method="POST" enctype="multipart/form-data" id="image-upload-form">
                            <div class="mb-3">
                                <input type="file" class="form-control" id="property_images" name="property_images[]" multiple accept="image/jpeg, image/jpg, image/png">
                                <div class="form-text mt-2">
                                    <i class="bi bi-info-circle"></i> Hình ảnh sẽ được nén tự động trước khi upload. Vui lòng đợi quá trình nén hoàn tất.
                                </div>
                            </div>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="submit" name="upload_images" class="btn btn-success">
                                    <i class="bi bi-upload me-1"></i> Upload
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-md-12">
                        <h5>Danh sách hình ảnh</h5>
                        <?php if (empty($propertyImages)): ?>
                            <div class="alert alert-info">
                                Chưa có hình ảnh nào. Vui lòng upload hình ảnh.
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i> Kéo thả các hình ảnh để sắp xếp thứ tự. Hình ảnh đầu tiên sẽ được đặt làm ảnh chính. <strong>Nhớ nhấn nút "Lưu thứ tự hình ảnh" sau khi sắp xếp xong!</strong>
                                    </div>
                                </div>
                                <div class="col-12 mb-3" id="order-changed-alert" style="display: none;">
                                    <div class="alert alert-warning">
                                        <i class="bi bi-exclamation-triangle"></i> Thứ tự hình ảnh đã thay đổi. Vui lòng nhấn nút <strong>"Lưu thứ tự hình ảnh"</strong> ở cuối danh sách để lưu lại thay đổi.
                                    </div>
                                </div>
                            </div>
                            <div class="row sortable-images" id="sortable-property-images">
                                <?php foreach ($propertyImages as $image): ?>
                                    <div class="col-md-3 mb-4" data-image-id="<?php echo $image->id; ?>">
                                        <div class="card h-100" style="cursor: move;">
                                            <div class="card-img-container position-relative">
                                                <img src="<?php echo BASE_URL . '/public/uploads/properties/' . $image->image_path; ?>"
                                                     class="card-img-top image-preview-trigger" alt="Property Image"
                                                     style="height: 200px; object-fit: cover; cursor: pointer;"
                                                     data-image-path="<?php echo BASE_URL . '/public/uploads/properties/' . $image->image_path; ?>">
                                                <div class="position-absolute top-0 start-0 p-2">
                                                    <span class="badge bg-secondary"><i class="bi bi-arrows-move"></i> Kéo để di chuyển</span>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <?php if ($image->is_main): ?>
                                                        <span class="badge bg-primary">Ảnh chính</span>
                                                    <?php endif; ?>
                                                </h6>
                                                <div class="d-flex justify-content-between">
                                                    <?php if (!$image->is_main): ?>
                                                        <button type="button" class="btn btn-sm btn-outline-primary set-main-image-btn"
                                                                data-image-id="<?php echo $image->id; ?>"
                                                                data-property-id="<?php echo $id; ?>">
                                                            <i class="bi bi-star-fill"></i> Đặt làm ảnh chính
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-sm btn-outline-secondary" disabled>
                                                            <i class="bi bi-star-fill"></i> Ảnh chính
                                                        </button>
                                                    <?php endif; ?>
                                                    <button type="button" class="btn btn-sm btn-outline-danger delete-image-btn"
                                                            data-image-id="<?php echo $image->id; ?>"
                                                            data-property-id="<?php echo $id; ?>">
                                                        <i class="bi bi-trash"></i> Xóa
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Thêm input ẩn để lưu thứ tự hình ảnh -->
                            <input type="hidden" name="image_order" id="image-order-input" value="">

                            <!-- Form để lưu thứ tự hình ảnh -->
                            <form action="index.php?page=properties&action=edit&id=<?php echo $id; ?>" method="POST" id="save-image-order-form" class="mt-3">
                                <input type="hidden" name="image_order_data" id="image-order-data-input" value="">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="button" id="reset-order-btn" class="btn btn-outline-secondary me-2" style="display: none;">
                                        <i class="bi bi-arrow-counterclockwise me-1"></i> Hủy thay đổi
                                    </button>
                                    <button type="submit" name="save_image_order" class="btn btn-primary">
                                        <i class="bi bi-save me-1"></i> Lưu thứ tự hình ảnh
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Thêm jQuery UI cho lightbox đơn giản -->
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">

<!-- Thêm CSS tùy chỉnh cho dialog -->
<style>
    .ui-dialog {
        z-index: 9999 !important;
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        width: 80% !important;
        max-width: 1000px !important;
    }
    .ui-widget-overlay {
        opacity: 0.8;
        background: #000;
    }
    .ui-dialog .ui-dialog-titlebar {
        background: #212529;
        color: white;
        border: none;
    }
    .ui-dialog .ui-dialog-content {
        padding: 15px;
        text-align: center;
        max-height: 80vh !important;
        overflow: auto;
    }
    .ui-button {
        background: #0d6efd;
        color: white;
        border: none;
        padding: 5px 15px;
        border-radius: 4px;
    }
    .ui-button:hover {
        background: #0b5ed7;
        color: white;
    }
    /* Đảm bảo hình ảnh không vượt quá kích thước dialog */
    .ui-dialog img {
        max-width: 100%;
        max-height: 70vh;
        object-fit: contain;
    }
</style>

<!-- Thêm CSS cho Select2 -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<!-- CSS cho datetime-local picker -->
<style>
    input[type="datetime-local"] {
        padding: 0.375rem 0.75rem;
    }
    .card-header.bg-light {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0,0,0,.125);
    }
    .card-header.bg-light h5 {
        margin-bottom: 0;
        font-weight: 500;
    }
</style>

<!-- Thêm CSS cho tính năng upload hình ảnh tùy chỉnh -->
<style>
    .image-upload-container {
        border: 2px dashed #28a745;
        border-radius: 5px;
        background: #f8f9fa;
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
    }
    .image-upload-message {
        font-weight: 400;
        margin: 1em 0;
    }
    .image-preview-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }
    .image-preview-item {
        position: relative;
        width: 150px;
        height: 150px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        overflow: hidden;
        cursor: move;
    }
    .image-preview-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    .image-preview-item .remove-image {
        position: absolute;
        top: 5px;
        right: 5px;
        background: rgba(0, 0, 0, 0.5);
        color: white;
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .image-preview-item .main-image-badge {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(40, 167, 69, 0.8);
        color: white;
        padding: 2px 5px;
        font-size: 12px;
        text-align: center;
    }
    .custom-file-input {
        display: none;
    }
    .custom-file-label {
        display: inline-block;
        padding: 8px 20px;
        background-color: #28a745;
        color: white;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 10px;
    }
    .custom-file-label:hover {
        background-color: #218838;
    }
    .drag-placeholder {
        border: 2px dashed #6c757d;
        background-color: #f8f9fa;
    }

    /* CSS cho hiệu ứng kéo thả */
    .sortable-drag {
        opacity: 0.8;
    }

    /* Hiệu ứng nhấp nháy cho nút lưu thứ tự */
    @keyframes pulse-warning {
        0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
        100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
    }

    .btn-warning {
        animation: pulse-warning 1.5s infinite;
    }
</style>

<!-- Thêm jQuery trước Select2 -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Thêm jQuery UI -->
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"></script>
<!-- Thêm Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
<!-- JavaScript để tự động tạo slug từ tiêu đề và xử lý các chức năng khác -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- Thêm thư viện Sortable.js để hỗ trợ kéo thả -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<!-- Thêm thư viện Compressor.js để nén ảnh -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/compressorjs/1.2.1/compressor.min.js"></script>
<!-- Thêm script xử lý nén ảnh -->
<script src="<?php echo BASE_URL; ?>/public/js/image-compressor.js"></script>
<script>
$(document).ready(function() {
    // Không cần biến titleInput và slugInput nữa
    const isGuestCheckbox = document.getElementById('is_guest');
    const userIdContainer = document.getElementById('user_id_container');
    const userIdSelect = document.getElementById('user_id');
    const userRequiredSpan = document.querySelector('.user-required');

    // Biến để theo dõi trạng thái thay đổi thứ tự
    let orderChanged = false;
    let originalOrder = [];

    // Xử lý tự động tính ngày hết hạn khi thay đổi ngày đăng
    function updateExpirationDate() {
        var createdAt = $('#created_at').val();
        if (createdAt) {
            // Tính ngày hết hạn là 14 ngày sau ngày đăng
            var expirationDate = new Date(createdAt);
            expirationDate.setDate(expirationDate.getDate() + 14);

            // Format lại ngày giờ theo định dạng datetime-local
            var year = expirationDate.getFullYear();
            var month = (expirationDate.getMonth() + 1).toString().padStart(2, '0');
            var day = expirationDate.getDate().toString().padStart(2, '0');
            var hours = expirationDate.getHours().toString().padStart(2, '0');
            var minutes = expirationDate.getMinutes().toString().padStart(2, '0');

            var formattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
            $('#expiration_date').val(formattedDate);
        }
    }

    // Gọi hàm khi ngày đăng thay đổi
    $('#created_at').change(function() {
        updateExpirationDate();
    });

    // Khởi tạo Select2 cho phường/xã
    $('.ward-select').select2({
        theme: 'bootstrap-5',
        placeholder: '-- Chọn phường / xã --',
        allowClear: true,
        width: '100%',
        ajax: {
            url: '/thuenhadanang/quantrivien/ajax_select2.php?action=search_wards',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term // search term
                };
            },
            processResults: function (data) {
                return {
                    results: data
                };
            },
            cache: true,
            error: function(xhr, status, error) {
                console.log('Select2 Ward AJAX error:', status, error, xhr.responseText);
            }
        }
    });

    // Khởi tạo Select2 cho chủ sở hữu với tìm kiếm
    $('.user-select').select2({
        theme: 'bootstrap-5',
        placeholder: '-- Chọn chủ sở hữu --',
        allowClear: true,
        width: '100%',
        minimumInputLength: 2,
        ajax: {
            url: '/thuenhadanang/quantrivien/ajax_select2.php?action=search_users',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term // search term
                };
            },
            processResults: function (data) {
                // Không thêm tùy chọn Guest vào kết quả tìm kiếm
                // Guest sẽ được thêm tự động khi tick vào checkbox "Đăng tin free"
                return {
                    results: data
                };
            },
            cache: true,
            error: function(xhr, status, error) {
                console.log('Select2 AJAX error:', status, error, xhr.responseText);
            }
        }
    });

    // Đã xóa code xử lý slug - sẽ xử lý ở phía server

// Đã xóa phần xử lý upload hình ảnh tùy chỉnh vì đã chuyển sang mục "Quản lý hình ảnh"

    // Xử lý checkbox đăng tin free
    if (isGuestCheckbox && userIdContainer && userIdSelect) {
        // Xử lý khi trang được tải
        handleGuestCheckbox();

        // Xử lý khi checkbox thay đổi
        $(isGuestCheckbox).on('change', handleGuestCheckbox);
    }

    // Khởi tạo Sortable.js cho danh sách hình ảnh
    const sortableImages = document.getElementById('sortable-property-images');
    if (sortableImages) {
        // Lưu thứ tự ban đầu khi trang được tải
        saveOriginalOrder();

        // Cập nhật thứ tự hình ảnh ban đầu
        updateImageOrder();

        new Sortable(sortableImages, {
            animation: 150,
            handle: '.card', // Chỉ cho phép kéo thả bằng cách kéo card
            ghostClass: 'bg-light', // Class cho phần tử "ma" khi kéo
            chosenClass: 'bg-warning', // Làm nổi bật phần tử đang được kéo
            dragClass: 'sortable-drag', // Class cho phần tử đang được kéo
            onStart: function(evt) {
                // Thêm hiệu ứng khi bắt đầu kéo
                $(evt.item).addClass('shadow-lg');
            },
            onEnd: function(evt) {
                // Xóa hiệu ứng khi kết thúc kéo
                $(evt.item).removeClass('shadow-lg');
                // Cập nhật thứ tự hình ảnh sau khi kéo thả
                updateImageOrder();
            }
        });

        // Khởi tạo form lưu thứ tự hình ảnh
        const saveOrderForm = document.getElementById('save-image-order-form');
        if (saveOrderForm) {
            // Xử lý sự kiện click cho nút hủy thay đổi
            const resetOrderBtn = document.getElementById('reset-order-btn');
            if (resetOrderBtn) {
                resetOrderBtn.addEventListener('click', function() {
                    resetImageOrder();
                });
            }

            // Biến để theo dõi trạng thái submit form
            let isSubmitting = false;

            // Xử lý sự kiện submit form
            saveOrderForm.addEventListener('submit', function(e) {
                // Ngăn chặn hành vi mặc định của form
                e.preventDefault();

                // Đánh dấu form đang được submit
                isSubmitting = true;

                // Cập nhật thứ tự hình ảnh trước khi submit
                updateImageOrder();

                // Debug thông tin
                console.log('Submit form - orderChanged:', orderChanged);
                console.log('Current image order:', $('#image-order-data-input').val());

                // Nếu thứ tự không thay đổi, hiển thị thông báo và ngăn chặn submit
                if (!orderChanged) {
                    alert('Thứ tự hình ảnh chưa thay đổi. Không cần lưu lại.');
                    isSubmitting = false;
                    return;
                }

                // Lấy dữ liệu thứ tự hình ảnh
                const imageOrderData = $('#image-order-data-input').val();

                // Kiểm tra xem dữ liệu có hợp lệ không
                if (!imageOrderData) {
                    alert('Không có dữ liệu thứ tự hình ảnh. Vui lòng thử lại.');
                    isSubmitting = false;
                    return;
                }

                // Hiển thị thông báo đang xử lý
                const submitButton = this.querySelector('button[type="submit"]');
                if (submitButton) {
                    submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xử lý...';
                    submitButton.disabled = true;
                }

                // Tắt cảnh báo beforeunload khi form được submit
                window.removeEventListener('beforeunload', beforeUnloadHandler);

                // Gửi dữ liệu bằng AJAX thay vì submit form
                $.ajax({
                    url: '../quantrivien/ajax_property.php',
                    type: 'POST',
                    data: {
                        action: 'save_image_order',
                        property_id: <?php echo $id; ?>,
                        image_order_data: imageOrderData
                    },
                    dataType: 'json',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function(response) {
                        console.log('AJAX success:', response);

                        // Hiển thị thông báo thành công
                        if (response && response.success) {
                            showNotification('Cập nhật thứ tự hình ảnh thành công!', 'success');

                            // Cập nhật lại thứ tự ban đầu
                            saveOriginalOrder();

                            // Đặt lại trạng thái
                            orderChanged = false;

                            // Ẩn thông báo thay đổi
                            $('#order-changed-alert').fadeOut();

                            // Khôi phục nút lưu thứ tự
                            $('#save-image-order-form button[type="submit"]').addClass('btn-primary').removeClass('btn-warning');

                            // Ẩn nút hủy thay đổi
                            $('#reset-order-btn').hide();

                            // Làm mới trang sau 1 giây
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            showNotification('Có lỗi xảy ra! Không thể cập nhật thứ tự hình ảnh.', 'danger');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', status, error);
                        console.error('Response Text:', xhr.responseText);

                        // Hiển thị thông báo lỗi
                        showNotification('Có lỗi xảy ra! Không thể cập nhật thứ tự hình ảnh. Vui lòng thử lại sau.', 'danger');
                    },
                    complete: function() {
                        // Khôi phục nút submit
                        if (submitButton) {
                            submitButton.innerHTML = '<i class="bi bi-save me-1"></i> Lưu thứ tự hình ảnh';
                            submitButton.disabled = false;
                        }

                        // Đặt lại trạng thái submit
                        isSubmitting = false;
                    }
                });
            });

            // Hàm xử lý sự kiện beforeunload
            function beforeUnloadHandler(e) {
                // Nếu form đang được submit, không hiển thị cảnh báo
                if (isSubmitting) return;

                if (orderChanged) {
                    // Hiển thị thông báo xác nhận
                    const confirmationMessage = 'Thứ tự hình ảnh đã thay đổi nhưng chưa được lưu. Bạn có chắc chắn muốn rời khỏi trang?';
                    e.returnValue = confirmationMessage;
                    return confirmationMessage;
                }
            }

            // Thêm sự kiện window.beforeunload để cảnh báo khi người dùng rời trang mà chưa lưu thay đổi
            window.addEventListener('beforeunload', beforeUnloadHandler);
        }
    }



    // Lưu thứ tự ban đầu khi trang được tải
    function saveOriginalOrder() {
        originalOrder = [];
        $('#sortable-property-images .col-md-3').each(function() {
            originalOrder.push($(this).data('image-id'));
        });
        console.log('Original order saved:', originalOrder);
    }

    // Hàm cập nhật thứ tự hình ảnh
    function updateImageOrder() {
        const imageOrder = [];
        $('#sortable-property-images .col-md-3').each(function() {
            imageOrder.push($(this).data('image-id'));
        });

        // Cập nhật cả hai input ẩn
        const orderJson = JSON.stringify(imageOrder);
        $('#image-order-input').val(orderJson);
        $('#image-order-data-input').val(orderJson);

        console.log('Image order updated:', imageOrder);

        // Kiểm tra xem thứ tự có thay đổi không
        if (originalOrder.length > 0) {
            let changed = false;
            if (originalOrder.length !== imageOrder.length) {
                changed = true;
            } else {
                for (let i = 0; i < originalOrder.length; i++) {
                    if (originalOrder[i] !== imageOrder[i]) {
                        changed = true;
                        break;
                    }
                }
            }

            // Nếu thứ tự đã thay đổi, hiển thị thông báo
            if (changed !== orderChanged) {
                orderChanged = changed;
                if (orderChanged) {
                    $('#order-changed-alert').fadeIn();
                    // Làm nổi bật nút lưu thứ tự
                    $('#save-image-order-form button[type="submit"]').addClass('btn-warning').removeClass('btn-primary');
                    // Hiển thị nút hủy thay đổi
                    $('#reset-order-btn').show();
                } else {
                    $('#order-changed-alert').fadeOut();
                    // Khôi phục nút lưu thứ tự
                    $('#save-image-order-form button[type="submit"]').addClass('btn-primary').removeClass('btn-warning');
                    // Ẩn nút hủy thay đổi
                    $('#reset-order-btn').hide();
                }
            }
        }
    }

    // Hàm khôi phục thứ tự ban đầu
    function resetImageOrder() {
        if (!originalOrder.length) return;

        // Lấy container
        const container = $('#sortable-property-images');
        if (!container.length) return;

        // Tạo mảng các phần tử hiện tại
        const currentItems = {};
        container.find('.col-md-3').each(function() {
            const id = $(this).data('image-id');
            currentItems[id] = $(this).detach();
        });

        // Thêm lại các phần tử theo thứ tự ban đầu
        originalOrder.forEach(function(id) {
            if (currentItems[id]) {
                container.append(currentItems[id]);
            }
        });

        // Cập nhật thứ tự hình ảnh
        updateImageOrder();

        // Hiển thị thông báo
        showNotification('Đã khôi phục thứ tự hình ảnh ban đầu.', 'info');
    }

    // Hàm đặt ảnh chính
    function setMainImage(imageId, propertyId, button) {
        // Hiển thị loading
        if (button) {
            button.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xử lý...');
            button.prop('disabled', true);
        }

        // Gửi yêu cầu đặt ảnh chính
        $.ajax({
            url: '../quantrivien/ajax_property.php',
            type: 'POST',
            data: {
                action: 'set_main_image',
                image_id: imageId,
                property_id: propertyId
            },
            dataType: 'json',
            timeout: 10000, // 10 giây timeout
            cache: false, // Tránh cache
            success: function(response) {
                if (response.success) {
                    // Cập nhật giao diện
                    $('.badge.bg-primary').remove();

                    // Tìm card chứa ảnh được đặt làm ảnh chính
                    const card = $('[data-image-id="' + imageId + '"]').find('.card');
                    card.find('.card-title').html('<span class="badge bg-primary">Ảnh chính</span>');

                    // Cập nhật tất cả các nút
                    $('.set-main-image-btn').show();
                    $('.btn-outline-secondary[disabled]').closest('.d-flex').html(
                        '<button type="button" class="btn btn-sm btn-outline-primary set-main-image-btn" ' +
                        'data-image-id="' + imageId + '" data-property-id="' + propertyId + '">' +
                        '<i class="bi bi-star-fill"></i> Đặt làm ảnh chính</button>'
                    );

                    // Cập nhật nút của ảnh chính
                    card.find('.set-main-image-btn').hide().after(
                        '<button class="btn btn-sm btn-outline-secondary" disabled>' +
                        '<i class="bi bi-star-fill"></i> Ảnh chính</button>'
                    );

                    // Hiển thị thông báo thành công
                    showNotification('Đặt ảnh chính thành công!', 'success');
                } else {
                    // Khôi phục nút
                    if (button) {
                        button.html('<i class="bi bi-star-fill"></i> Đặt làm ảnh chính');
                        button.prop('disabled', false);
                    }

                    // Hiển thị thông tin debug
                    console.error('Set main image failed:', response);

                    // Hiển thị thông báo lỗi với thông tin debug
                    let errorMsg = 'Có lỗi xảy ra! Không thể đặt ảnh chính.';
                    if (response.debug) {
                        errorMsg += ' (Image ID: ' + response.debug.image_id + ', Property ID: ' + response.debug.property_id + ')';
                    }
                    showNotification(errorMsg, 'danger');
                }
            },
            error: function(xhr, status, error) {
                // Khôi phục nút
                if (button) {
                    button.html('<i class="bi bi-star-fill"></i> Đặt làm ảnh chính');
                    button.prop('disabled', false);
                }

                // Ghi log lỗi để debug
                console.error('AJAX Error:', status, error);
                console.error('Response Text:', xhr.responseText);

                // Hiển thị thông báo lỗi chi tiết hơn
                let errorMsg = 'Có lỗi xảy ra! ';
                if (status === 'timeout') {
                    errorMsg += 'Kết nối bị timeout.';
                } else if (status === 'error') {
                    errorMsg += 'Không thể kết nối đến server.';
                } else if (status === 'parsererror') {
                    errorMsg += 'Lỗi xử lý dữ liệu từ server.';
                } else {
                    errorMsg += 'Lỗi: ' + error;
                }

                showNotification(errorMsg + ' Vui lòng thử lại sau.', 'danger');
            }
        });
    }

    // Xử lý xem hình ảnh trong modal - sử dụng jQuery UI Dialog
    $(document).on('click', '.image-preview-trigger', function(e) {
        // Ngăn chặn sự kiện click lan truyền đến phần tử cha (để không ảnh hưởng đến kéo thả)
        e.stopPropagation();

        console.log('Image clicked!');
        const imagePath = $(this).data('image-path');
        console.log('Image path:', imagePath);

        // Tạo dialog đơn giản với jQuery UI
        const $dialog = $('<div>').dialog({
            modal: true,
            resizable: false,
            draggable: true,
            width: '80%',
            maxWidth: '1000px',
            title: 'Xem hình ảnh',
            position: { my: "center", at: "center", of: window },
            closeOnEscape: true, // Cho phép đóng bằng phím Esc
            open: function(event, ui) {
                $(this).html('<img src="' + imagePath + '" />');

                // Đảm bảo dialog nằm ở giữa màn hình ban đầu, nhưng vẫn có thể kéo thả
                const $dialogParent = $(this).parent();
                $dialogParent.css({
                    'position': 'fixed',
                    'top': '50%',
                    'left': '50%',
                    'transform': 'translate(-50%, -50%)'
                });

                // Thêm sự kiện để xóa transform khi bắt đầu kéo thả
                $dialogParent.on('dragstart', function() {
                    $(this).css('transform', 'none');
                });

                // Đảm bảo overlay phủ toàn màn hình
                $('.ui-widget-overlay').css({
                    'position': 'fixed',
                    'top': '0',
                    'left': '0',
                    'width': '100%',
                    'height': '100%'
                });

                // Thêm sự kiện click cho overlay để đóng dialog khi click bên ngoài
                $('.ui-widget-overlay').on('click', function() {
                    $dialog.dialog('close');
                });
            },
            close: function(event, ui) {
                // Xóa sự kiện click khi dialog đóng để tránh memory leak
                $('.ui-widget-overlay').off('click');
                // Xóa sự kiện dragstart
                $(this).parent().off('dragstart');
            },
            buttons: {
                "Đóng": function() {
                    $(this).dialog("close");
                }
            }
        });
    });

    // Xử lý xóa hình ảnh bằng AJAX
    $(document).on('click', '.delete-image-btn', function() {
        if (!confirm('Bạn có chắc chắn muốn xóa hình ảnh này?')) {
            return;
        }

        const imageId = $(this).data('image-id');
        const propertyId = $(this).data('property-id');
        const imageCard = $(this).closest('.col-md-3');

        // Hiển thị loading
        imageCard.append('<div class="position-absolute top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-light bg-opacity-75"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');

        // Gửi yêu cầu xóa hình ảnh
        $.ajax({
            url: '../quantrivien/ajax_property.php',
            type: 'POST',
            data: {
                action: 'delete_image',
                image_id: imageId,
                property_id: propertyId
            },
            dataType: 'json',
            timeout: 10000, // 10 giây timeout
            cache: false, // Tránh cache
            success: function(response) {
                if (response.success) {
                    // Xóa card hình ảnh khỏi DOM với hiệu ứng fade out
                    imageCard.fadeOut(300, function() {
                        $(this).remove();

                        // Kiểm tra nếu không còn hình ảnh nào
                        if ($('.col-md-3').length === 0) {
                            $('.row').html('<div class="alert alert-info">Chưa có hình ảnh nào. Vui lòng upload hình ảnh.</div>');
                        }
                    });

                    // Hiển thị thông báo thành công
                    showNotification('Xóa hình ảnh thành công!', 'success');

                    // Làm mới trang sau 1 giây để tránh vấn đề duplicate
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    // Xóa loading
                    imageCard.find('.position-absolute').remove();

                    // Hiển thị thông tin debug
                    console.error('Delete image failed:', response);

                    // Hiển thị thông báo lỗi với thông tin debug
                    let errorMsg = 'Có lỗi xảy ra! Không thể xóa hình ảnh.';
                    if (response.debug) {
                        errorMsg += ' (Image ID: ' + response.debug.image_id + ', Property ID: ' + response.debug.property_id + ')';
                    }
                    showNotification(errorMsg, 'danger');
                }
            },
            error: function(xhr, status, error) {
                // Xóa loading
                imageCard.find('.position-absolute').remove();

                // Ghi log lỗi để debug
                console.error('AJAX Error:', status, error);
                console.error('Response Text:', xhr.responseText);

                // Hiển thị thông báo lỗi chi tiết hơn
                let errorMsg = 'Có lỗi xảy ra! ';
                if (status === 'timeout') {
                    errorMsg += 'Kết nối bị timeout.';
                } else if (status === 'error') {
                    errorMsg += 'Không thể kết nối đến server.';
                } else if (status === 'parsererror') {
                    errorMsg += 'Lỗi xử lý dữ liệu từ server.';
                } else {
                    errorMsg += 'Lỗi: ' + error;
                }

                showNotification(errorMsg + ' Vui lòng thử lại sau.', 'danger');
            }
        });
    });

    // Xử lý đặt ảnh chính bằng AJAX
    $(document).on('click', '.set-main-image-btn', function() {
        const imageId = $(this).data('image-id');
        const propertyId = $(this).data('property-id');
        const button = $(this);

        // Sử dụng hàm setMainImage đã định nghĩa
        setMainImage(imageId, propertyId, button);
    });

    // Hàm hiển thị thông báo
    function showNotification(message, type) {
        const notification = $('<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
            message +
            '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
            '</div>');

        // Thêm thông báo vào đầu phần quản lý hình ảnh
        $('.card-header:contains("Quản lý hình ảnh")').next('.card-body').prepend(notification);

        // Tự động ẩn thông báo sau 3 giây
        setTimeout(function() {
            try {
                // Sử dụng Bootstrap 5 Alert API nếu có
                if (typeof bootstrap !== 'undefined' && bootstrap.Alert) {
                    const bsAlert = new bootstrap.Alert(notification[0]);
                    bsAlert.close();
                } else {
                    // Fallback nếu không có Bootstrap Alert API
                    notification.alert('close');
                }
            } catch (e) {
                console.error('Error closing alert:', e);
                // Fallback đơn giản
                notification.fadeOut(300, function() {
                    $(this).remove();
                });
            }
        }, 3000);
    }

    // Hàm xử lý checkbox đăng tin free
    function handleGuestCheckbox() {
        const contactInfoContainer = document.getElementById('contact_info_container');
        const contactName = document.getElementById('contact_name');
        const contactPhone = document.getElementById('contact_phone');

        if (isGuestCheckbox.checked) {
            // Ẩn trường chủ sở hữu
            $(userIdContainer).hide();

            // Hiển thị form thông tin liên hệ
            $(contactInfoContainer).show();

            // Đặt thuộc tính required cho các trường thông tin liên hệ bắt buộc
            $(contactName).attr('required', 'required');
            $(contactPhone).attr('required', 'required');

            // Tạo hoặc cập nhật tùy chọn Guest
            let guestOption = new Option('Guest (Đăng tin free)', '1', true, true);

            // Xóa tùy chọn Guest cũ nếu có
            $(userIdSelect).find('option[value="1"]').remove();

            // Thêm tùy chọn Guest mới
            $(userIdSelect).append(guestOption).trigger('change');

            // Bỏ thuộc tính required
            $(userIdSelect).removeAttr('required');
        } else {
            // Hiển thị trường chủ sở hữu
            $(userIdContainer).show();

            // Ẩn form thông tin liên hệ
            $(contactInfoContainer).hide();

            // Bỏ thuộc tính required cho các trường thông tin liên hệ
            $(contactName).removeAttr('required');
            $(contactPhone).removeAttr('required');

            // Đặt lại thuộc tính required
            $(userIdSelect).attr('required', 'required');

            // Nếu giá trị hiện tại là 1 (Guest), xóa giá trị và xóa tùy chọn Guest
            if ($(userIdSelect).val() === '1') {
                $(userIdSelect).find('option[value="1"]').remove();
                $(userIdSelect).val('').trigger('change');
            }
        }
    }

    // Đã xóa hàm createSlug - sẽ xử lý ở phía server
});
</script>
