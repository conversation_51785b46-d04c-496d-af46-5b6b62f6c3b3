<?php
require_once BASE_PATH . '/app/controllers/BaseController.php';
require_once BASE_PATH . '/app/models/PropertyType.php';
require_once BASE_PATH . '/app/models/Ward.php';

class HomeController extends BaseController {
    private $propertyTypeModel;
    private $wardModel;

    public function __construct() {
        $this->propertyTypeModel = new PropertyType();
        $this->wardModel = new Ward();
    }

    public function index() {
        // Lấy danh sách loại hình bất động sản
        $propertyTypes = $this->propertyTypeModel->getAllPropertyTypes();

        // Lấy danh sách phường/xã
        $wards = $this->wardModel->getAllWards();

        $data = [
            'title' => 'Trang chủ - Thuê N<PERSON> Đ<PERSON> Nẵng',
            'view' => 'home',
            'propertyTypes' => $propertyTypes,
            'wards' => $wards
        ];
        $this->view('home', $data);
    }

    public function about() {
        $data = [
            'title' => 'Về chúng tôi - <PERSON><PERSON><PERSON> Nẵng',
            'view' => 'about'
        ];
        $this->view('about', $data);
    }

    public function contact() {
        $data = [
            'title' => 'Liên hệ - Thuê Nhà Đà Nẵng',
            'view' => 'contact'
        ];
        $this->view('contact', $data);
    }
}
