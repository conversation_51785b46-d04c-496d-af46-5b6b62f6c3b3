<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="container mt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/thuenhadanang" class="text-decoration-none"><PERSON>rang chủ</a></li>
        <li class="breadcrumb-item"><a href="/thuenhadanang/dashboard" class="text-decoration-none">Bảng điều khiển</a></li>
        <li class="breadcrumb-item active" aria-current="page">Quản lý tin đăng</li>
    </ol>
</nav>

<div class="container py-4">
    <div class="row">
        <!-- Include Sidebar -->
        <?php require_once 'app/views/partials/dashboard-sidebar.php'; ?>

        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3 border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Quản lý tin đăng</h5>
                    <a href="/thuenhadanang/property-listing" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus-circle me-1"></i> Đăng tin mới
                    </a>
                </div>
                <div class="card-body">
                    <?php if (!empty($data['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $data['error']; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($data['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $data['success']; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Tab Navigation -->
                    <ul class="nav nav-tabs mb-4" id="propertyTabs">
                        <li class="nav-item">
                            <a class="nav-link <?php echo $data['status'] == 'all' ? 'active' : ''; ?>"
                               href="#" data-status="all">
                                Tất cả <span class="badge ms-1"><?php echo $data['counts']['all']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $data['status'] == 'active' ? 'active' : ''; ?>"
                               href="#" data-status="active">
                                Kích hoạt <span class="badge ms-1"><?php echo $data['counts']['active']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $data['status'] == 'pending' ? 'active' : ''; ?>"
                               href="#" data-status="pending">
                                Đợi duyệt <span class="badge ms-1"><?php echo $data['counts']['pending']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $data['status'] == 'expired' ? 'active' : ''; ?>"
                               href="#" data-status="expired">
                                Hết hạn <span class="badge ms-1"><?php echo $data['counts']['expired']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $data['status'] == 'hidden' ? 'active' : ''; ?>"
                               href="#" data-status="hidden">
                                Ẩn <span class="badge ms-1"><?php echo $data['counts']['hidden']; ?></span>
                            </a>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div id="propertiesTableContainer">
                        <!-- Skeleton Loading Template -->
                        <div class="skeleton-loader">
                            <?php for ($i = 0; $i < 3; $i++): ?>
                            <div class="card mb-3 border-0 shadow-sm skeleton-card">
                                <div class="row g-0">
                                    <!-- Cột bên trái: Hình ảnh -->
                                    <div class="col-md-3">
                                        <div class="skeleton-item skeleton-image"></div>
                                    </div>

                                    <!-- Cột giữa: Thông tin tin đăng -->
                                    <div class="col-md-6">
                                        <div class="card-body">
                                            <div class="skeleton-item skeleton-title mb-2"></div>
                                            <div class="d-flex gap-2 mb-2">
                                                <div class="skeleton-item skeleton-badge"></div>
                                                <div class="skeleton-item skeleton-badge"></div>
                                            </div>
                                            <div class="skeleton-item skeleton-text medium mb-2"></div>
                                            <div class="skeleton-item skeleton-text medium mb-3"></div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="skeleton-item skeleton-text medium"></div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="skeleton-item skeleton-text short"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Cột bên phải: Các nút hành động -->
                                    <div class="col-md-3">
                                        <div class="card-body d-flex flex-column justify-content-center h-100">
                                            <div class="d-grid gap-2">
                                                <div class="skeleton-item skeleton-button"></div>
                                                <div class="skeleton-item skeleton-button"></div>
                                                <div class="skeleton-item skeleton-button"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endfor; ?>
                        </div>

                        <!-- Actual Content -->
                        <div class="fade-content">
                            <?php require 'app/views/user-properties/ajax-properties-table.php'; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSS cho tab điều hướng -->
<style>
/* CSS cho tab điều hướng */
#propertyTabs {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    border-bottom: none;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
}

#propertyTabs .nav-item {
    margin-bottom: 8px;
}

#propertyTabs .nav-link {
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: 500;
    color: #495057;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

#propertyTabs .nav-link:hover {
    background-color: #e9ecef;
}

#propertyTabs .nav-link.active {
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

#propertyTabs .badge {
    font-size: 0.75rem;
    padding: 0.25em 0.6em;
    border-radius: 10px;
    transition: all 0.3s ease;
    position: relative;
    top: -2px;
}

/* Màu sắc cho các badge */
#propertyTabs .nav-link[data-status="all"] .badge {
    background-color: #6c757d;
}

#propertyTabs .nav-link[data-status="active"] .badge {
    background-color: #198754;
}

#propertyTabs .nav-link[data-status="pending"] .badge {
    background-color: #ffc107;
    color: #212529;
}

#propertyTabs .nav-link[data-status="expired"] .badge {
    background-color: #dc3545;
}

#propertyTabs .nav-link[data-status="hidden"] .badge {
    background-color: #6c757d;
}

/* Màu sắc cho badge khi tab active */
#propertyTabs .nav-link.active .badge {
    background-color: rgba(255, 255, 255, 0.8);
    color: #0d6efd;
}

/* CSS for smooth tab content transitions */
#propertiesTableContainer {
    position: relative;
    min-height: 400px; /* Default minimum height */
    transition: height 0.3s ease-in-out;
}

#propertiesTableContainer .fade-content {
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
}

#propertiesTableContainer.loading .fade-content {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

/* Skeleton Loading Animation */
@keyframes skeleton-pulse {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Staggered animation for multiple skeleton items */
.skeleton-card:nth-child(1) .skeleton-item {
    animation-delay: 0s;
}
.skeleton-card:nth-child(2) .skeleton-item {
    animation-delay: 0.1s;
}
.skeleton-card:nth-child(3) .skeleton-item {
    animation-delay: 0.2s;
}
.skeleton-card .skeleton-title {
    animation-delay: 0.05s;
}
.skeleton-card .skeleton-badge {
    animation-delay: 0.1s;
}
.skeleton-card .skeleton-text:nth-of-type(1) {
    animation-delay: 0.15s;
}
.skeleton-card .skeleton-text:nth-of-type(2) {
    animation-delay: 0.2s;
}
.skeleton-card .skeleton-text:nth-of-type(3) {
    animation-delay: 0.25s;
}

.skeleton-loader {
    display: none;
}

#propertiesTableContainer.loading .skeleton-loader {
    display: block;
}

.skeleton-item {
    background-color: #f0f0f0;
    background-image: linear-gradient(90deg,
                      rgba(255, 255, 255, 0),
                      rgba(255, 255, 255, 0.6),
                      rgba(255, 255, 255, 0));
    background-size: 200px 100%;
    background-repeat: no-repeat;
    border-radius: 4px;
    animation: skeleton-pulse 1.5s infinite linear;
}

.skeleton-card {
    margin-bottom: 1rem;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    background-color: #fff;
    padding: 0;
}

.skeleton-row {
    display: flex;
    flex-wrap: wrap;
}

.skeleton-image {
    height: 180px;
    width: 100%;
    border-radius: 4px 0 0 4px;
}

.skeleton-title {
    height: 24px;
    width: 80%;
    margin-bottom: 12px;
}

.skeleton-badge {
    height: 20px;
    width: 80px;
    margin-bottom: 8px;
    border-radius: 10px;
}

.skeleton-text {
    height: 16px;
    margin-bottom: 8px;
}

.skeleton-text.short {
    width: 40%;
}

.skeleton-text.medium {
    width: 60%;
}

.skeleton-text.long {
    width: 90%;
}

.skeleton-button {
    height: 32px;
    margin-bottom: 8px;
    border-radius: 4px;
}

/* Đảm bảo skeleton card có cùng bố cục với card thực tế */
.skeleton-card .col-md-3 {
    display: flex;
    align-items: stretch;
}

.skeleton-card .col-md-3 .skeleton-image {
    width: 100%;
    height: 100%;
    min-height: 180px;
    object-fit: cover;
}

.skeleton-card .col-md-3:last-child .card-body {
    height: 100%;
}

/* Responsive */
@media (max-width: 767.98px) {
    #propertyTabs {
        justify-content: center;
    }

    #propertyTabs .nav-link {
        padding: 6px 12px;
        font-size: 0.9rem;
    }

    #propertiesTableContainer {
        min-height: 600px; /* Taller on mobile due to stacked layout */
    }

    /* Adjust skeleton for mobile */
    .skeleton-card .row {
        flex-direction: column;
    }

    .skeleton-card .col-md-3,
    .skeleton-card .col-md-6 {
        width: 100%;
        max-width: 100%;
    }

    .skeleton-image {
        height: 200px; /* Taller images on mobile */
        border-radius: 4px 4px 0 0;
    }

    .skeleton-card .col-md-3:last-child {
        order: 3; /* Đảm bảo nút bấm ở dưới cùng trên mobile */
    }

    .skeleton-card .col-md-6 {
        order: 2; /* Đảm bảo thông tin ở giữa trên mobile */
    }
}
</style>

<!-- JavaScript cho tab và AJAX -->
<script>
// Thiết lập CSRF token
window.csrfToken = '<?php echo $_SESSION['csrf_token'] ?? ''; ?>';

document.addEventListener('DOMContentLoaded', function() {
    // Xử lý chuyển đổi tab
    const tabLinks = document.querySelectorAll('#propertyTabs .nav-link');
    const tableContainer = document.getElementById('propertiesTableContainer');

    // Set initial container height
    const setInitialHeight = () => {
        // Store the initial height of the container
        if (tableContainer.querySelector('.fade-content')) {
            const contentHeight = tableContainer.querySelector('.fade-content').offsetHeight;
            // Set a minimum height based on content or default
            tableContainer.style.height = Math.max(contentHeight, 400) + 'px';
        }
    };

    // Set initial height after a short delay to ensure content is rendered
    setTimeout(setInitialHeight, 100);

    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Don't do anything if this tab is already active
            if (this.classList.contains('active')) {
                return;
            }

            // Xóa class active từ tất cả các tab
            tabLinks.forEach(tab => tab.classList.remove('active'));

            // Thêm class active cho tab được chọn
            this.classList.add('active');

            // Lấy trạng thái từ data attribute
            const status = this.getAttribute('data-status');

            // Store current height before loading new content
            const currentHeight = tableContainer.offsetHeight;
            tableContainer.style.height = currentHeight + 'px';

            // Add loading class for visual feedback which will show the skeleton loader
            tableContainer.classList.add('loading');

            // Record the start time of the request for minimum display time calculation
            const requestStartTime = Date.now();

            // Gửi request AJAX
            fetch(`/thuenhadanang/dashboard/properties?status=${status}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                // Create a temporary div to measure the height of the new content
                const tempDiv = document.createElement('div');
                tempDiv.style.position = 'absolute';
                tempDiv.style.visibility = 'hidden';
                tempDiv.style.width = tableContainer.clientWidth + 'px';
                tempDiv.innerHTML = html;
                document.body.appendChild(tempDiv);

                // Get the height of the new content
                const newHeight = tempDiv.offsetHeight;

                // Remove the temporary div
                document.body.removeChild(tempDiv);

                // Animate to the new height
                tableContainer.style.height = Math.max(newHeight, 400) + 'px';

                // Store the HTML and request time
                const responseData = {
                    html: html,
                    time: Date.now()
                };

                // Ensure skeleton loader is shown for at least 800ms for better UX
                const minLoadingTime = 800; // milliseconds
                const timeElapsed = Date.now() - requestStartTime;
                const remainingTime = Math.max(0, minLoadingTime - timeElapsed);

                // After a delay, update the content
                setTimeout(() => {
                    // Wrap the new content in fade-content div
                    tableContainer.innerHTML = `
                        <!-- Skeleton Loading Template -->
                        <div class="skeleton-loader">
                            <?php for ($i = 0; $i < 3; $i++): ?>
                            <div class="card mb-3 border-0 shadow-sm skeleton-card">
                                <div class="row g-0">
                                    <!-- Cột bên trái: Hình ảnh -->
                                    <div class="col-md-3">
                                        <div class="skeleton-item skeleton-image"></div>
                                    </div>

                                    <!-- Cột giữa: Thông tin tin đăng -->
                                    <div class="col-md-6">
                                        <div class="card-body">
                                            <div class="skeleton-item skeleton-title mb-2"></div>
                                            <div class="d-flex gap-2 mb-2">
                                                <div class="skeleton-item skeleton-badge"></div>
                                                <div class="skeleton-item skeleton-badge"></div>
                                            </div>
                                            <div class="skeleton-item skeleton-text medium mb-2"></div>
                                            <div class="skeleton-item skeleton-text medium mb-3"></div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="skeleton-item skeleton-text medium"></div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="skeleton-item skeleton-text short"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Cột bên phải: Các nút hành động -->
                                    <div class="col-md-3">
                                        <div class="card-body d-flex flex-column justify-content-center h-100">
                                            <div class="d-grid gap-2">
                                                <div class="skeleton-item skeleton-button"></div>
                                                <div class="skeleton-item skeleton-button"></div>
                                                <div class="skeleton-item skeleton-button"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endfor; ?>
                        </div>
                        <div class="fade-content">${responseData.html}</div>
                    `;

                    // Remove loading class after a short delay to allow fade-in transition
                    setTimeout(() => {
                        tableContainer.classList.remove('loading');

                        // Cập nhật URL mà không reload trang
                        history.pushState({status: status}, '', `/thuenhadanang/dashboard/properties?status=${status}`);
                    }, 100);
                }, remainingTime);
            })
            .catch(error => {
                console.error('Error:', error);
                tableContainer.innerHTML = `<div class="fade-content"><div class="alert alert-danger">Có lỗi xảy ra khi tải dữ liệu. Vui lòng thử lại sau.</div></div>`;
                tableContainer.classList.remove('loading');
                tableContainer.style.height = 'auto';
            });
        });
    });

    // Handle window resize to adjust container height
    window.addEventListener('resize', () => {
        if (!tableContainer.classList.contains('loading')) {
            tableContainer.style.height = 'auto';
            setTimeout(setInitialHeight, 100);
        }
    });

    // Xử lý gia hạn bất động sản
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('extend-property-btn') || e.target.closest('.extend-property-btn')) {
            e.preventDefault();

            const button = e.target.classList.contains('extend-property-btn') ? e.target : e.target.closest('.extend-property-btn');
            const propertyId = button.getAttribute('data-property-id');
            const propertyTitle = button.getAttribute('data-property-title');

            // Hiển thị modal xác nhận
            if (confirm(`Bạn có chắc chắn muốn gửi yêu cầu gia hạn cho bất động sản "${propertyTitle}"?\n\nYêu cầu sẽ được gửi đến quản trị viên để phê duyệt.`)) {
                // Disable button và hiển thị loading
                button.disabled = true;
                button.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> Đang gửi...';

                // Kiểm tra CSRF token
                if (!window.csrfToken) {
                    showToast('error', 'Lỗi bảo mật: Vui lòng tải lại trang');
                    button.disabled = false;
                    button.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i> Gia hạn';
                    return;
                }

                // Gửi AJAX request
                fetch(`/thuenhadanang/property/extend/${propertyId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `csrf_token=${window.csrfToken}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Hiển thị thông báo thành công
                        showToast('success', data.message);

                        // Cập nhật button thành trạng thái chờ duyệt
                        button.outerHTML = `
                            <span class="badge bg-warning text-dark">
                                <i class="bi bi-clock me-1"></i> Chờ duyệt gia hạn
                            </span>
                        `;
                    } else {
                        // Hiển thị thông báo lỗi
                        showToast('error', data.message);

                        // Khôi phục button
                        button.disabled = false;
                        button.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i> Gia hạn';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('error', 'Có lỗi xảy ra khi gửi yêu cầu gia hạn');

                    // Khôi phục button
                    button.disabled = false;
                    button.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i> Gia hạn';
                });
            }
        }
    });
});

// Hàm tạo CSRF token
function generateCSRFToken() {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Hàm hiển thị toast notification
function showToast(type, message) {
    // Tạo toast container nếu chưa có
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        `;
        document.body.appendChild(toastContainer);
    }

    // Tạo toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    toast.style.cssText = `
        min-width: 300px;
        margin-bottom: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    `;

    toast.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Thêm toast vào container
    toastContainer.appendChild(toast);

    // Tự động ẩn sau 5 giây
    setTimeout(() => {
        if (toast.parentNode) {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 150);
        }
    }, 5000);
}</script>