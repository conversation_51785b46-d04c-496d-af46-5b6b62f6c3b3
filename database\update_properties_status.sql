-- Cậ<PERSON> nhật cấu trúc cột status trong bảng properties
-- Thay đổi các giá trị hiện tại (pending, available, expired) thành chỉ 2 tùy chọn: display, hide

-- <PERSON><PERSON><PERSON> tiên, thay đổi kiểu dữ liệu của cột status từ enum sang varchar
ALTER TABLE `properties` MODIFY COLUMN `status` VARCHAR(20) NOT NULL DEFAULT 'display';

-- Cậ<PERSON> nhật các giá trị hiện tại
-- 'available' -> 'display'
-- 'pending' -> 'hide'
-- 'rented' -> 'hide'
UPDATE `properties` SET `status` = 'display' WHERE `status` = 'available';
UPDATE `properties` SET `status` = 'hide' WHERE `status` IN ('pending', 'rented');

-- Cậ<PERSON> nhật cột active để phản ánh trạng thái kích hoạt
-- 0: <PERSON><PERSON><PERSON> k<PERSON>ch hoạt (đang chờ duyệt)
-- 1: <PERSON><PERSON> kích ho<PERSON>t (đã được duy<PERSON>t)
-- 2: <PERSON>ừ chối (bị từ chối duyệt)
UPDATE `properties` SET `active` = 0 WHERE `status` = 'hide' AND `active` = 0;
UPDATE `properties` SET `active` = 1 WHERE `status` = 'display';
