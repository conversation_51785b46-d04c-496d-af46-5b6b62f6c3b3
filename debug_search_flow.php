<?php

// Debug the complete search flow for the specific URL
define('BASE_PATH', __DIR__);
define('APP_PATH', BASE_PATH . '/app');

// Load required files
require_once APP_PATH . '/libraries/Database.php';
require_once APP_PATH . '/libraries/UrlHandler.php';
require_once APP_PATH . '/models/PropertyType.php';
require_once APP_PATH . '/models/Ward.php';
require_once APP_PATH . '/models/Property.php';
require_once APP_PATH . '/controllers/BaseController.php';
require_once APP_PATH . '/controllers/SearchController.php';

echo "<h1>Debug Search Flow</h1>\n";

// Simulate the exact URL and flow
$testUrl = 'cho-thue-can-ho-tai-an-hai-bac/gia-tren-15-trieu-dt-50-70m2-1pn';

echo "<h2>1. URL Handler Processing</h2>\n";
$urlHandler = new UrlHandler();
$parsedParams = $urlHandler->parseUrl($testUrl);

echo "<p><strong>Parsed Parameters:</strong></p>\n";
echo "<pre>" . print_r($parsedParams, true) . "</pre>\n";

if ($parsedParams['matched']) {
    echo "<h2>2. SearchController Call Simulation</h2>\n";

    // Simulate what index.php does
    $typeSlug = $parsedParams['type'];
    $wardSlug = $parsedParams['ward'];

    $additionalParams = [
        'price' => $parsedParams['price'],
        'area' => $parsedParams['area'],
        'bedrooms' => $parsedParams['bedrooms'],
        'bathrooms' => $parsedParams['bathrooms'],
        'direction' => $parsedParams['direction']
    ];

    echo "<p><strong>Parameters passed to SearchController:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>typeSlug: '$typeSlug'</li>\n";
    echo "<li>wardSlug: '$wardSlug'</li>\n";
    echo "<li>additionalParams:</li>\n";
    echo "<ul>\n";
    foreach ($additionalParams as $key => $value) {
        echo "<li>$key: '$value'</li>\n";
    }
    echo "</ul>\n";
    echo "</ul>\n";

    echo "<h2>3. SearchController Internal Processing</h2>\n";

    // Simulate SearchController->filterByTypeAndWard processing
    $price = isset($additionalParams['price']) ? $additionalParams['price'] : '';
    $area = isset($additionalParams['area']) ? $additionalParams['area'] : '';
    $bedrooms = isset($additionalParams['bedrooms']) ? $additionalParams['bedrooms'] : '';
    $bathrooms = isset($additionalParams['bathrooms']) ? $additionalParams['bathrooms'] : '';
    $direction = isset($additionalParams['direction']) ? $additionalParams['direction'] : '';
    $sort = 'default';
    $keyword = '';

    echo "<p><strong>SearchController internal variables:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>price: '$price'</li>\n";
    echo "<li>area: '$area'</li>\n";
    echo "<li>bedrooms: '$bedrooms'</li>\n";
    echo "<li>bathrooms: '$bathrooms'</li>\n";
    echo "<li>direction: '$direction'</li>\n";
    echo "<li>sort: '$sort'</li>\n";
    echo "<li>keyword: '$keyword'</li>\n";
    echo "</ul>\n";

    echo "<h2>4. Property Model Call</h2>\n";

    $propertyModel = new Property();

    // Check which method would be called
    if (!empty($keyword)) {
        echo "<p><strong>Would call:</strong> searchProperties()</p>\n";
        $methodCall = "searchProperties('$keyword', '$typeSlug', '$wardSlug', '$price', '$sort', '$bedrooms', '$bathrooms', '$direction', '$area')";
    } else {
        echo "<p><strong>Would call:</strong> getPropertiesByTypeAndWard()</p>\n";
        $methodCall = "getPropertiesByTypeAndWard('$typeSlug', '$wardSlug', '$price', '$area', '$bedrooms', '$sort', '$bathrooms', '$direction')";
    }

    echo "<p><strong>Method call:</strong> $methodCall</p>\n";

    // Actually call the method
    echo "<h2>5. Actual Property Model Execution</h2>\n";

    if (!empty($keyword)) {
        $properties = $propertyModel->searchProperties($keyword, $typeSlug, $wardSlug, $price, $sort, $bedrooms, $bathrooms, $direction, $area);
    } else {
        $properties = $propertyModel->getPropertiesByTypeAndWard($typeSlug, $wardSlug, $price, $area, $bedrooms, $sort, $bathrooms, $direction);
    }

    echo "<p><strong>Results:</strong> " . count($properties) . " properties found</p>\n";

    if (count($properties) > 0) {
        echo "<h3>Found Properties:</h3>\n";
        foreach ($properties as $prop) {
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>\n";
            echo "<strong>ID:</strong> {$prop->id}<br>\n";
            echo "<strong>Title:</strong> " . htmlspecialchars($prop->title) . "<br>\n";
            echo "<strong>Type:</strong> {$prop->type_name}<br>\n";
            echo "<strong>Ward:</strong> {$prop->ward_name}<br>\n";
            echo "<strong>Price:</strong> " . number_format($prop->price) . " VND<br>\n";
            echo "<strong>Area:</strong> {$prop->area} m²<br>\n";
            echo "<strong>Bedrooms:</strong> {$prop->bedrooms}<br>\n";
            echo "<strong>Bathrooms:</strong> {$prop->bathrooms}<br>\n";
            echo "</div>\n";
        }
    } else {
        echo "<p style='color: red;'>No properties found with the specified criteria.</p>\n";

        // Test without filters to see what's available
        echo "<h3>Testing without filters:</h3>\n";
        $allProperties = $propertyModel->getPropertiesByTypeAndWard($typeSlug, $wardSlug, '', '', '', 'default', '', '');
        echo "<p><strong>Without any filters:</strong> " . count($allProperties) . " properties</p>\n";

        if (count($allProperties) > 0) {
            echo "<h4>Available Properties (no filters):</h4>\n";
            foreach ($allProperties as $prop) {
                $meetsCriteria = [
                    'price' => empty($price) || ($price == '15+' && $prop->price >= 15000000),
                    'area' => empty($area) || ($area == '50-70' && $prop->area >= 50 && $prop->area <= 70),
                    'bedrooms' => empty($bedrooms) || ($bedrooms == '1' && $prop->bedrooms == 1)
                ];

                $allMatch = array_reduce($meetsCriteria, function($carry, $item) { return $carry && $item; }, true);
                $bgColor = $allMatch ? '#e8f5e8' : '#ffe8e8';

                echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px; background-color: $bgColor;'>\n";
                echo "<strong>ID:</strong> {$prop->id}<br>\n";
                echo "<strong>Title:</strong> " . htmlspecialchars($prop->title) . "<br>\n";
                echo "<strong>Price:</strong> " . number_format($prop->price) . " VND ";
                echo "(" . ($meetsCriteria['price'] ? '✅' : '❌') . " ≥15M)<br>\n";
                echo "<strong>Area:</strong> {$prop->area} m² ";
                echo "(" . ($meetsCriteria['area'] ? '✅' : '❌') . " 50-70m²)<br>\n";
                echo "<strong>Bedrooms:</strong> {$prop->bedrooms} ";
                echo "(" . ($meetsCriteria['bedrooms'] ? '✅' : '❌') . " =1)<br>\n";
                echo "<strong>Overall Match:</strong> " . ($allMatch ? '✅ YES' : '❌ NO') . "<br>\n";
                echo "</div>\n";
            }
        }
    }

    echo "<h2>6. Potential Issues Analysis</h2>\n";
    echo "<ul>\n";

    // Check if parameters are being passed correctly
    if (empty($bedrooms)) {
        echo "<li style='color: red;'>❌ <strong>Issue:</strong> bedrooms parameter is empty</li>\n";
    } else {
        echo "<li style='color: green;'>✅ bedrooms parameter is set: '$bedrooms'</li>\n";
    }

    if (empty($area)) {
        echo "<li style='color: red;'>❌ <strong>Issue:</strong> area parameter is empty</li>\n";
    } else {
        echo "<li style='color: green;'>✅ area parameter is set: '$area'</li>\n";
    }

    if (empty($price)) {
        echo "<li style='color: red;'>❌ <strong>Issue:</strong> price parameter is empty</li>\n";
    } else {
        echo "<li style='color: green;'>✅ price parameter is set: '$price'</li>\n";
    }

    // Check if there's any fallback logic
    echo "<li>🔍 Check if SearchController has fallback logic when no results found</li>\n";
    echo "<li>🔍 Check if frontend is making additional AJAX calls</li>\n";
    echo "<li>🔍 Check if there are cached results being displayed</li>\n";

    echo "</ul>\n";

    echo "<h2>7. Recommendations</h2>\n";
    echo "<ul>\n";
    echo "<li>✅ UrlHandler is parsing correctly</li>\n";
    echo "<li>✅ Parameters are being passed to SearchController correctly</li>\n";
    echo "<li>✅ Property model is receiving correct parameters</li>\n";

    if (count($properties) == 0) {
        echo "<li>❌ <strong>Root cause:</strong> No properties in database match the criteria (1 bedroom, 50-70m², ≥15M VND)</li>\n";
        echo "<li>💡 <strong>Solution:</strong> Either add test data that matches criteria, or check if there's fallback logic showing all properties when no matches found</li>\n";
    }

    echo "</ul>\n";

} else {
    echo "<p style='color: red;'>URL was not matched by UrlHandler</p>\n";
}

?>
