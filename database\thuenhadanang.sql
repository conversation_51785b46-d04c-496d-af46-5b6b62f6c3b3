-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- <PERSON><PERSON><PERSON> chủ: 127.0.0.1
-- Th<PERSON><PERSON> gian đã tạo: Th5 07, 2025 lúc 06:51 AM
-- <PERSON><PERSON><PERSON> bản m<PERSON> phục vụ: 10.4.32-MariaDB
-- <PERSON><PERSON><PERSON> bản PHP: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- <PERSON><PERSON> sở dữ liệu: `thuenhadanang`
--

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `properties`
--

CREATE TABLE `properties` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `type_id` int(11) NOT NULL,
  `address` varchar(255) NOT NULL,
  `ward_id` int(11) DEFAULT NULL,
  `street` varchar(100) DEFAULT NULL,
  `city` varchar(100) DEFAULT 'Đà Nẵng',
  `area` float DEFAULT NULL,
  `price` decimal(15,0) NOT NULL,
  `price_period` enum('day','week','month','quarter','year') NOT NULL DEFAULT 'month',
  `bedrooms` int(11) DEFAULT NULL,
  `bathrooms` int(11) DEFAULT NULL,
  `direction` varchar(20) DEFAULT NULL,
  `video_url` varchar(255) DEFAULT NULL,
  `status` enum('available','rented','pending') NOT NULL DEFAULT 'available',
  `featured` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `property_images`
--

CREATE TABLE `property_images` (
  `id` int(11) NOT NULL,
  `property_id` int(11) NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `is_main` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `property_types`
--

CREATE TABLE `property_types` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Đang đổ dữ liệu cho bảng `property_types`
--

INSERT INTO `property_types` (`id`, `name`, `slug`, `description`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Căn hộ', 'can-ho', 'Căn hộ chung cư', 1, '2025-05-07 04:23:49', '2025-05-07 04:23:49'),
(2, 'Nhà riêng', 'nha-rieng', 'Nhà riêng, nhà phố', 1, '2025-05-07 04:23:49', '2025-05-07 04:23:49'),
(3, 'Nhà mặt phố', 'nha-mat-pho', 'Nhà mặt tiền, mặt phố', 1, '2025-05-07 04:23:49', '2025-05-07 04:23:49'),
(4, 'Nhà trọ - phòng trọ', 'nha-tro-phong-tro', 'Nhà trọ, phòng trọ cho thuê', 1, '2025-05-07 04:23:49', '2025-05-07 04:23:49'),
(5, 'Biệt thự', 'biet-thu', 'Biệt thự, villa', 1, '2025-05-07 04:23:49', '2025-05-07 04:33:04'),
(6, 'Văn phòng', 'van-phong', 'Văn phòng cho thuê', 1, '2025-05-07 04:23:49', '2025-05-07 04:23:49');

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `fullname` varchar(100) NOT NULL,
  `phone` varchar(15) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `zalo` varchar(15) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `avatar` varchar(255) DEFAULT 'default-avatar.jpg',
  `role` enum('admin','user') NOT NULL DEFAULT 'user',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Đang đổ dữ liệu cho bảng `users`
--

INSERT INTO `users` (`id`, `fullname`, `phone`, `email`, `password`, `zalo`, `address`, `avatar`, `role`, `created_at`, `updated_at`) VALUES
(1, 'Administrator', '0944170391', '<EMAIL>', '$2y$10$LwSYBaiaVCYm4lWhxl5PXuIDLWj2gugSPn3jUhofe6DyKexglLwTK', '0944170391', 'Phường Hòa Cường, Quận Hải Châu, Đà Nẵng', 'admin_1746590332.png', 'admin', '2025-04-27 15:37:04', '2025-05-07 03:59:21'),
(2, 'Khoa Thắng', '0944170391', '<EMAIL>', '$2y$10$/Aedv0hkost4118SmyLfaetFb8mTmbr4d4UgaOpX6nhr3kIEb/8bS', '0944170391', 'An Hải Bắc, Sơn Trà, Đà Nẵng', 'avatar_1746554303_9527.jpg', 'user', '2025-04-27 15:37:37', '2025-05-06 17:58:23'),
(3, 'Thùy Duyên', '0905909961', '<EMAIL>', '$2y$10$LwSYBaiaVCYm4lWhxl5PXuIDLWj2gugSPn3jUhofe6DyKexglLwTK', '0905909961', 'Khối phố Ngân Giang, Điện Ngọc, Quảng Nam', 'avatar_1746554269_8911.jpg', 'user', '2025-04-27 15:37:57', '2025-05-07 03:41:00'),
(4, 'Hương Lan', '0948976055', '<EMAIL>', '$2y$10$xi7zz7DHIyMII92R70QxJuNIvS0Y24b5vmx9G13ay3Sv7mwHc2Tae', '0948976055', 'Phường Hòa Xuân, Đà Nẵng', 'avatar_1746591049_3525.jpg', 'user', '2025-05-07 03:48:17', '2025-05-07 04:11:27');

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `wards`
--

CREATE TABLE `wards` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Đang đổ dữ liệu cho bảng `wards`
--

INSERT INTO `wards` (`id`, `name`, `slug`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Phường An Hải Bắc', 'an-hai-bac', 1, '2025-05-07 04:38:41', '2025-05-07 04:46:26'),
(2, 'Phường An Hải Đông', 'an-hai-dong', 1, '2025-05-07 04:38:41', '2025-05-07 04:46:35'),
(3, 'Phường An Hải Tây', 'an-hai-tay', 1, '2025-05-07 04:38:41', '2025-05-07 04:46:43'),
(4, 'Phường Hải Châu 1', 'hai-chau-1', 1, '2025-05-07 04:38:41', '2025-05-07 04:46:48'),
(5, 'Hải Châu 2', 'hai-chau-2', 1, '2025-05-07 04:38:41', '2025-05-07 04:38:41'),
(6, 'Thạch Thang', 'thach-thang', 1, '2025-05-07 04:38:41', '2025-05-07 04:38:41'),
(7, 'Thanh Bình', 'thanh-binh', 1, '2025-05-07 04:38:41', '2025-05-07 04:38:41'),
(8, 'Thuận Phước', 'thuan-phuoc', 1, '2025-05-07 04:38:41', '2025-05-07 04:38:41'),
(9, 'Mỹ An', 'my-an', 1, '2025-05-07 04:38:41', '2025-05-07 04:38:41'),
(10, 'Khuê Mỹ', 'khue-my', 1, '2025-05-07 04:38:41', '2025-05-07 04:38:41'),
(11, 'Hòa Hải', 'hoa-hai', 1, '2025-05-07 04:38:41', '2025-05-07 04:38:41'),
(12, 'Hòa Quý', 'hoa-quy', 1, '2025-05-07 04:38:41', '2025-05-07 04:38:41'),
(13, 'Hòa Xuân', 'hoa-xuan', 1, '2025-05-07 04:38:41', '2025-05-07 04:38:41');

--
-- Chỉ mục cho các bảng đã đổ
--

--
-- Chỉ mục cho bảng `properties`
--
ALTER TABLE `properties`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Chỉ mục cho bảng `property_images`
--
ALTER TABLE `property_images`
  ADD PRIMARY KEY (`id`),
  ADD KEY `property_id` (`property_id`);

--
-- Chỉ mục cho bảng `property_types`
--
ALTER TABLE `property_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- Chỉ mục cho bảng `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Chỉ mục cho bảng `wards`
--
ALTER TABLE `wards`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- AUTO_INCREMENT cho các bảng đã đổ
--

--
-- AUTO_INCREMENT cho bảng `properties`
--
ALTER TABLE `properties`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `property_images`
--
ALTER TABLE `property_images`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `property_types`
--
ALTER TABLE `property_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT cho bảng `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT cho bảng `wards`
--
ALTER TABLE `wards`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- Các ràng buộc cho các bảng đã đổ
--

--
-- Các ràng buộc cho bảng `properties`
--

--
-- Cấu trúc bảng cho bảng `property_directions`
--

CREATE TABLE `property_directions` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `slug` varchar(50) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Đang đổ dữ liệu cho bảng `property_directions`
--

INSERT INTO `property_directions` (`id`, `name`, `slug`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Bắc', 'bac', 1, '2025-05-07 04:23:49', '2025-05-07 04:23:49'),
(2, 'Nam', 'nam', 1, '2025-05-07 04:23:49', '2025-05-07 04:23:49'),
(3, 'Đông', 'dong', 1, '2025-05-07 04:23:49', '2025-05-07 04:23:49'),
(4, 'Tây', 'tay', 1, '2025-05-07 04:23:49', '2025-05-07 04:23:49'),
(5, 'Đông Bắc', 'dong-bac', 1, '2025-05-07 04:23:49', '2025-05-07 04:23:49'),
(6, 'Tây Bắc', 'tay-bac', 1, '2025-05-07 04:23:49', '2025-05-07 04:23:49'),
(7, 'Đông Nam', 'dong-nam', 1, '2025-05-07 04:23:49', '2025-05-07 04:23:49'),
(8, 'Tây Nam', 'tay-nam', 1, '2025-05-07 04:23:49', '2025-05-07 04:23:49');

--
-- Chỉ mục cho bảng `property_directions`
--
ALTER TABLE `property_directions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- AUTO_INCREMENT cho bảng `property_directions`
--
ALTER TABLE `property_directions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- Các ràng buộc cho bảng `properties`
--
ALTER TABLE `properties`
  ADD CONSTRAINT `properties_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `properties_ibfk_2` FOREIGN KEY (`type_id`) REFERENCES `property_types` (`id`),
  ADD CONSTRAINT `properties_ibfk_3` FOREIGN KEY (`ward_id`) REFERENCES `wards` (`id`);

--
-- Cấu trúc bảng cho bảng `property_contacts`
--

CREATE TABLE `property_contacts` (
  `id` int(11) NOT NULL,
  `property_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `zalo` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Chỉ mục cho bảng `property_contacts`
--
ALTER TABLE `property_contacts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `property_id` (`property_id`);

--
-- AUTO_INCREMENT cho bảng `property_contacts`
--
ALTER TABLE `property_contacts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Các ràng buộc cho bảng `property_contacts`
--
ALTER TABLE `property_contacts`
  ADD CONSTRAINT `property_contacts_ibfk_1` FOREIGN KEY (`property_id`) REFERENCES `properties` (`id`) ON DELETE CASCADE;

--
-- Các ràng buộc cho bảng `property_images`
--
ALTER TABLE `property_images`
  ADD CONSTRAINT `property_images_ibfk_1` FOREIGN KEY (`property_id`) REFERENCES `properties` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
