/**
 * AJAX Search Handler for T<PERSON><PERSON> Nẵng
 * Handles real-time search without page reloads
 */

class AjaxSearch {
    constructor() {
        this.isLoading = false;
        this.isPageLoading = false;
        this.currentRequest = null;
        this.searchForm = null;
        this.resultsContainer = null;
        this.loadingOverlay = null;

        // Hybrid approach flags
        this.isInitialPageLoad = true;
        this.hasServerData = false;
        this.initialUrl = window.location.href;

        this.init();
    }

    /**
     * Initialize AJAX search functionality
     */
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    /**
     * Setup event listeners and DOM references
     */
    setup() {
        // Get DOM elements
        this.searchForm = document.getElementById('searchPageForm');
        this.resultsContainer = document.querySelector('.properties-grid');
        this.resultsHeader = document.querySelector('.results-header');
        this.resultsInfo = document.querySelector('.results-info');
        this.resultsTitle = document.querySelector('.results-title');
        this.resultsCount = document.querySelector('.results-count');

        // Check if page has server-rendered data
        this.checkServerData();

        // Check if we have no-results page (properties-grid might not exist)
        const noResultsContainer = document.querySelector('.no-results-container');
        if (noResultsContainer && !this.resultsContainer) {
            console.log('AJAX Search: No results page detected, creating properties-grid container');
            // Create properties-grid container for AJAX updates
            this.createPropertiesGridContainer();
        }

        if (!this.searchForm) {
            console.warn('AJAX Search: Search form not found');
            return;
        }

        if (!this.resultsContainer) {
            console.warn('AJAX Search: Results container not found and could not be created');
            return;
        }

        // Create loading overlay
        this.createLoadingOverlay();

        // Setup event listeners
        this.setupEventListeners();

        // Setup history management
        this.setupHistoryManagement();

        // Initialize advanced filters modal
        this.initializeAdvancedFiltersModal();

        // Hybrid approach: Only show skeleton if no server data
        this.initializeHybridPageLoad();

        console.log('AJAX Search initialized successfully');
        console.log('Hybrid mode - Has server data:', this.hasServerData);
    }

    /**
     * Check if page has server-rendered data
     */
    checkServerData() {
        // Check for data attribute indicating server-rendered content
        const mainContent = document.querySelector('.main-content-section');
        if (mainContent && mainContent.dataset.serverRendered === 'true') {
            this.hasServerData = true;
            console.log('✅ Hybrid: Server-rendered data detected');
            return;
        }

        // Fallback: Check if results container has content
        if (this.resultsContainer && this.resultsContainer.children.length > 0) {
            this.hasServerData = true;
            console.log('✅ Hybrid: Server data detected via content check');
            return;
        }

        // Check if results header exists with content
        if (this.resultsHeader && this.resultsTitle && this.resultsTitle.textContent.trim()) {
            this.hasServerData = true;
            console.log('✅ Hybrid: Server data detected via header check');
            return;
        }

        console.log('❌ Hybrid: No server data detected');
        this.hasServerData = false;
    }

    /**
     * Check if user came from homepage search to prevent double-loading
     */
    checkReferrerSource() {
        const referrer = document.referrer;
        const currentHost = window.location.hostname;

        if (referrer) {
            try {
                const referrerUrl = new URL(referrer);
                // Check if referrer is from same domain and is homepage
                if (referrerUrl.hostname === currentHost &&
                    (referrerUrl.pathname === '/thuenhadanang/' ||
                     referrerUrl.pathname === '/thuenhadanang/index.php' ||
                     referrerUrl.pathname === '/')) {
                    console.log('✅ Hybrid: User came from homepage search, skipping skeleton');
                    return true;
                }
            } catch (e) {
                console.warn('Could not parse referrer URL:', referrer);
            }
        }

        return false;
    }

    /**
     * Initialize hybrid page load behavior
     */
    initializeHybridPageLoad() {
        // Check if user came from homepage search
        const cameFromHomepage = this.checkReferrerSource();

        if (this.hasServerData) {
            // Page has server data - don't show skeleton, just mark as loaded
            this.isInitialPageLoad = false;
            console.log('✅ Hybrid: Using server-rendered data, skipping skeleton');
        } else if (cameFromHomepage) {
            // User came from homepage search - skip skeleton to prevent double-loading
            this.isInitialPageLoad = false;
            console.log('✅ Hybrid: User came from homepage, skipping skeleton');
        } else {
            // No server data and not from homepage - use original skeleton behavior
            this.initializePageLoadSkeleton();
            console.log('⚠️ Hybrid: No server data, showing skeleton');
        }
    }

    /**
     * Initialize skeleton on page load to prevent text jumping (legacy method)
     */
    initializePageLoadSkeleton() {
        // Only show skeleton if this is a search page with results
        if (this.resultsContainer && this.resultsContainer.children.length > 0) {
            // Mark that we're in page load mode
            this.isPageLoading = true;

            // Show skeleton briefly to prevent text jumping on reload
            this.showSkeletonResults();

            // Hide skeleton after a short delay to show real content
            setTimeout(() => {
                this.removeSkeletonResults();
                this.isPageLoading = false;
            }, 300);
        }
    }

    /**
     * Create properties-grid container when it doesn't exist (no-results page)
     */
    createPropertiesGridContainer() {
        const noResultsContainer = document.querySelector('.no-results-container');
        if (noResultsContainer) {
            console.log('🔧 AJAX Search: Creating properties-grid container for no-results page');

            // Create properties-grid div and hide it initially
            const propertiesGrid = document.createElement('div');
            propertiesGrid.className = 'properties-grid';
            propertiesGrid.style.display = 'none';
            propertiesGrid.innerHTML = ''; // Ensure it's empty

            // Insert after no-results container
            noResultsContainer.parentNode.insertBefore(propertiesGrid, noResultsContainer.nextSibling);

            // Update reference
            this.resultsContainer = propertiesGrid;
            console.log('✅ AJAX Search: Created empty properties-grid container (hidden)');

            // Debug: Check if any content gets added unexpectedly
            setTimeout(() => {
                if (propertiesGrid.children.length > 0) {
                    console.error('❌ UNEXPECTED: Properties-grid container has content!', propertiesGrid.children);
                    console.error('❌ This should not happen on no-results page');
                } else {
                    console.log('✅ Properties-grid container remains empty as expected');
                }
            }, 1000);
        }
    }

    /**
     * Setup all event listeners
     */
    setupEventListeners() {
        // Form submission
        this.searchForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.performSearch();
        });

        // Bỏ hoàn toàn auto-search - chỉ search khi user bấm nút "Tìm kiếm"
        // Không có auto-search cho bất kỳ field nào (keyword, dropdown, etc.)

        // Sort dropdown (override default behavior)
        const sortSelect = document.querySelector('.sort-select');
        if (sortSelect) {
            // Remove existing onchange attribute
            sortSelect.removeAttribute('onchange');

            sortSelect.addEventListener('change', (e) => {
                e.preventDefault();
                console.log('Sort changed:', sortSelect.value);
                this.performSearchWithSort();
            });
        }

        // Advanced filters button (open modal)
        const advancedFiltersBtn = document.getElementById('advancedFiltersBtn');
        if (advancedFiltersBtn) {
            advancedFiltersBtn.addEventListener('click', () => {
                console.log('Advanced filters button clicked');
                this.openAdvancedFiltersModal();
            });
        }

        // Note: Search button is handled by form submission above
        // No need for separate button click handler to avoid double execution
    }

    /**
     * Setup browser history management
     */
    setupHistoryManagement() {
        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.ajaxSearch) {
                this.loadSearchFromState(e.state);
            } else {
                // Check if we're back to initial URL with server data
                if (window.location.href === this.initialUrl && this.hasServerData) {
                    console.log('✅ Hybrid: Back to initial URL with server data, skipping AJAX');
                    return;
                }
            }
        });
    }

    /**
     * Create loading overlay
     */
    createLoadingOverlay() {
        this.loadingOverlay = document.createElement('div');
        this.loadingOverlay.className = 'ajax-search-loading';
        this.loadingOverlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>Đang tìm kiếm...</p>
            </div>
        `;

        // Add CSS for loading overlay
        if (!document.getElementById('ajax-search-styles')) {
            const styles = document.createElement('style');
            styles.id = 'ajax-search-styles';
            styles.textContent = `
                .ajax-search-loading {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.9);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                    border-radius: 8px;
                }

                .loading-content {
                    text-align: center;
                    color: var(--modern-gray-600);
                }

                .loading-spinner {
                    width: 40px;
                    height: 40px;
                    border: 3px solid var(--modern-gray-200);
                    border-top: 3px solid var(--modern-primary);
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 1rem;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                .results-container {
                    position: relative;
                    min-height: 200px;
                }

                .fade-in {
                    animation: fadeIn 0.3s ease-in;
                }

                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(10px); }
                    to { opacity: 1; transform: translateY(0); }
                }

                /* Smooth transitions for results header */
                .results-title,
                .results-count {
                    transition: opacity 0.3s ease;
                }

                /* Prevent layout shift during text updates */
                .results-info {
                    min-height: 60px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                }

                .results-title {
                    min-height: 32px;
                    display: flex;
                    align-items: center;
                }

                .results-count {
                    min-height: 24px;
                    display: flex;
                    align-items: center;
                }

                /* Facebook-style Skeleton Loading */
                .results-skeleton {
                    margin-top: 2rem;
                }

                .skeleton-header {
                    margin-bottom: 2rem;
                }

                .skeleton-title {
                    height: 32px;
                    width: 60%;
                    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                    background-size: 200% 100%;
                    animation: skeleton-shimmer 1.5s ease-in-out infinite;
                    border-radius: 6px;
                    margin-bottom: 0.5rem;
                }

                .skeleton-count {
                    height: 20px;
                    width: 40%;
                    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                    background-size: 200% 100%;
                    animation: skeleton-shimmer 1.5s ease-in-out infinite;
                    border-radius: 6px;
                }

                .skeleton-properties-grid {
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                    gap: 1.5rem;
                    margin-top: 2rem;
                }

                .skeleton-property-card {
                    background: white;
                    border-radius: 12px;
                    overflow: hidden;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                }

                .skeleton-image {
                    height: 200px;
                    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                    background-size: 200% 100%;
                    animation: skeleton-shimmer 1.5s ease-in-out infinite;
                }

                .skeleton-content {
                    padding: 1rem;
                }

                .skeleton-title-line {
                    height: 20px;
                    width: 85%;
                    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                    background-size: 200% 100%;
                    animation: skeleton-shimmer 1.5s ease-in-out infinite;
                    border-radius: 4px;
                    margin-bottom: 0.5rem;
                }

                .skeleton-location-line {
                    height: 16px;
                    width: 70%;
                    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                    background-size: 200% 100%;
                    animation: skeleton-shimmer 1.5s ease-in-out infinite;
                    border-radius: 4px;
                    margin-bottom: 1rem;
                }

                .skeleton-features {
                    display: flex;
                    gap: 0.5rem;
                    margin-bottom: 1rem;
                }

                .skeleton-feature {
                    height: 16px;
                    width: 60px;
                    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                    background-size: 200% 100%;
                    animation: skeleton-shimmer 1.5s ease-in-out infinite;
                    border-radius: 4px;
                }

                .skeleton-price-line {
                    height: 18px;
                    width: 50%;
                    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                    background-size: 200% 100%;
                    animation: skeleton-shimmer 1.5s ease-in-out infinite;
                    border-radius: 4px;
                }

                @keyframes skeleton-shimmer {
                    0% {
                        background-position: 200% 0;
                    }
                    100% {
                        background-position: -200% 0;
                    }
                }

                /* Responsive skeleton */
                @media (max-width: 1200px) {
                    .skeleton-properties-grid {
                        grid-template-columns: repeat(3, 1fr);
                    }
                }

                @media (max-width: 768px) {
                    .skeleton-properties-grid {
                        grid-template-columns: repeat(2, 1fr);
                        gap: 1rem;
                    }

                    .skeleton-image {
                        height: 150px;
                    }
                }

                @media (max-width: 480px) {
                    .skeleton-properties-grid {
                        grid-template-columns: 1fr;
                    }
                }
            `;
            document.head.appendChild(styles);
        }
    }

    /**
     * Get current search parameters from form
     */
    getSearchParams() {
        const params = {};

        // Get form fields by ID for better control
        const keyword = document.getElementById('searchKeyword');
        const type = document.getElementById('searchType');
        const ward = document.getElementById('searchWard');
        const price = document.getElementById('searchPrice');
        const area = document.getElementById('searchArea');
        const bedrooms = document.getElementById('searchBedrooms');
        const bathrooms = document.getElementById('searchBathrooms');
        const direction = document.getElementById('searchDirection');

        // Add non-empty values
        if (keyword && keyword.value.trim()) params.keyword = keyword.value.trim();
        if (type && type.value.trim()) params.type = type.value.trim();
        if (ward && ward.value.trim()) params.ward = ward.value.trim();
        if (price && price.value.trim()) params.price = price.value.trim();
        if (area && area.value.trim()) params.area = area.value.trim();
        if (bedrooms && bedrooms.value.trim()) params.bedrooms = bedrooms.value.trim();
        if (bathrooms && bathrooms.value.trim()) params.bathrooms = bathrooms.value.trim();
        if (direction && direction.value.trim()) params.direction = direction.value.trim();

        return params;
    }

    /**
     * Get search parameters including sort
     */
    getSearchParamsWithSort() {
        const params = this.getSearchParams();

        // Get sort parameter from sort dropdown
        const sortSelect = document.querySelector('.sort-select');
        if (sortSelect && sortSelect.value) {
            // Extract sort value from URL or use direct value
            const sortUrl = sortSelect.value;
            const sortMatch = sortUrl.match(/[?&]sort=([^&]+)/);
            if (sortMatch) {
                params.sort = sortMatch[1];
            } else {
                // Fallback: try to determine sort from selected option
                const selectedOption = sortSelect.options[sortSelect.selectedIndex];
                if (selectedOption && selectedOption.value.includes('sort=')) {
                    const fallbackMatch = selectedOption.value.match(/[?&]sort=([^&]+)/);
                    if (fallbackMatch) {
                        params.sort = fallbackMatch[1];
                    }
                }
            }
        }

        return params;
    }

    /**
     * Perform AJAX search
     */
    async performSearch() {
        await this._performSearch(this.getSearchParams());
    }

    /**
     * Perform AJAX search with sort
     */
    async performSearchWithSort() {
        await this._performSearch(this.getSearchParamsWithSort());
    }

    /**
     * Internal method to perform AJAX search
     */
    async _performSearch(params) {
        if (this.isLoading) {
            // Cancel previous request
            if (this.currentRequest) {
                this.currentRequest.abort();
            }
        }

        // Mark that this is no longer initial page load
        this.isInitialPageLoad = false;

        // Show skeleton immediately to prevent data flash
        this.showLoading();
        this.isLoading = true;

        try {
            console.log('Performing AJAX search with params:', params);

            // Build API URL with cache busting
            const searchParams = new URLSearchParams(params);
            searchParams.append('_t', Date.now()); // Cache busting
            const apiUrl = '/thuenhadanang/api/search.php?' + searchParams.toString();

            // Create AbortController for request cancellation
            const controller = new AbortController();
            this.currentRequest = controller;

            // Make AJAX request
            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                signal: controller.signal
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                const text = await response.text();
                console.error('Non-JSON response:', text.substring(0, 200));
                throw new Error('Server returned non-JSON response');
            }

            const data = await response.json();

            if (data.success) {
                // Update results
                this.updateResults(data.data);

                // Update URL without page reload
                this.updateUrl(data.data.metadata.url, params);

                console.log('Search completed successfully:', data.data.count, 'properties found');
            } else {
                throw new Error(data.error.message || 'Search failed');
            }

        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('AJAX Search Error:', error);
                this.showError(error.message);
            }
        } finally {
            this.isLoading = false;
            this.hideLoading();
            this.currentRequest = null;
        }
    }

    /**
     * Update search results in DOM - only show when data is ready
     */
    updateResults(data) {
        // Prepare all content first before showing anything
        this.prepareNewContent(data);

        // Add minimum delay to ensure skeleton was visible
        const minSkeletonTime = 300; // Minimum time to show skeleton
        setTimeout(() => {
            // Only show content when everything is ready
            this.showNewContent();
        }, minSkeletonTime);
    }

    /**
     * Prepare new content without showing it
     */
    prepareNewContent(data) {
        // Store data for later display
        this.pendingData = data;

        // Pre-render content in hidden state
        this.updateResultsHeader(data);
        this.updatePropertiesGrid(data.properties);
    }

    /**
     * Show the prepared content
     */
    showNewContent() {
        // Remove skeleton and show real content
        this.removeSkeletonResults();
    }

    /**
     * Update results header (title and count) - no animation, just update
     */
    updateResultsHeader(data) {
        // Update title
        if (this.resultsTitle) {
            this.resultsTitle.textContent = data.metadata.title;
        }

        // Update count
        if (this.resultsCount) {
            this.resultsCount.textContent = `Tìm thấy ${data.count} bất động sản phù hợp`;
        }

        // Update page title
        document.title = data.metadata.title + ' - Thuê Nhà Đà Nẵng';
    }

    /**
     * Update properties grid
     */
    updatePropertiesGrid(properties) {
        if (properties.length === 0) {
            this.showNoResults();
            return;
        }

        // Hide any existing no-results container when we have results
        const existingNoResults = document.querySelector('.no-results-container');
        if (existingNoResults) {
            existingNoResults.style.display = 'none';
        }

        // Show properties grid container
        if (this.resultsContainer) {
            this.resultsContainer.style.display = '';
        }

        let html = '';
        properties.forEach(property => {
            html += this.renderPropertyCard(property);
        });

        this.resultsContainer.innerHTML = html;
    }

    /**
     * Render individual property card
     */
    renderPropertyCard(property) {
        const featuredBadge = property.featured ? '<span class="property-badge featured-badge">Nổi bật</span>' : '';

        return `
            <div class="property-card" data-featured="${property.featured}" data-new="${property.isNew}">
                <div class="property-image-container">
                    <a href="${property.url}">
                        <img src="${property.imagePath}" class="property-image" alt="${property.title}">
                    </a>
                    <div class="property-badges">
                        ${featuredBadge}
                    </div>
                    <button class="wishlist-btn" data-property-id="${property.id}">
                        <i class="bi bi-heart"></i>
                    </button>
                </div>
                <div class="property-content">
                    <div class="property-header">
                        <h3 class="property-title">
                            <a href="${property.url}">${property.title}</a>
                        </h3>
                        <div class="property-location">
                            <i class="bi bi-geo-alt"></i>
                            <span>${property.wardName}</span>
                        </div>
                    </div>
                    <div class="property-features">
                        <span class="feature-item">
                            <i class="bi bi-rulers"></i>
                            ${property.area}m²
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-door-closed"></i>
                            ${property.bedrooms} PN
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-droplet"></i>
                            ${property.bathrooms} WC
                        </span>
                    </div>
                    <div class="property-footer">
                        <div class="property-price">
                            <span class="current-price">${property.priceDisplay}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Show no results message with suggestions (like search.php)
     */
    showNoResults() {
        console.log('🚫 AJAX Search: Showing no results, clearing any existing content');

        // CRITICAL: Clear any existing content in properties grid
        if (this.resultsContainer) {
            this.resultsContainer.innerHTML = ''; // Clear all property cards
            this.resultsContainer.style.display = 'none'; // Hide properties grid
            console.log('✅ AJAX Search: Cleared properties-grid content and hid container');
        }

        // Get current search parameters to generate suggestions
        const urlParams = new URLSearchParams(window.location.search);
        const currentPath = window.location.pathname;

        // Extract type and ward from URL path
        const typeMatch = currentPath.match(/cho-thue-([^-]+)(?:-tai-|$)/);
        const wardMatch = currentPath.match(/cho-thue-[^-]+-tai-([^\/]+)/);

        const selectedType = typeMatch ? typeMatch[1] : urlParams.get('type');
        const selectedWard = wardMatch ? wardMatch[1] : urlParams.get('ward');

        // Generate suggestions HTML
        let suggestionsHTML = '';
        if (selectedType && selectedWard) {
            suggestionsHTML = `
                <div class="suggestions">
                    <p>Bạn có thể thử:</p>
                    <ul>
                        <li><a href="/thuenhadanang/cho-thue-${selectedType}">Xem tất cả ${selectedType} cho thuê</a></li>
                        <li><a href="/thuenhadanang/cho-thue-nha-dat-tai-${selectedWard}">Xem tất cả bất động sản tại ${selectedWard}</a></li>
                        <li><a href="/thuenhadanang/cho-thue-nha-dat">Xem tất cả bất động sản cho thuê</a></li>
                    </ul>
                </div>
            `;
        } else if (selectedWard) {
            suggestionsHTML = `
                <div class="suggestions">
                    <p>Hiện tại chưa có bất động sản nào tại khu vực này. Vui lòng thử tìm kiếm ở khu vực khác hoặc
                    <a href="/thuenhadanang/cho-thue-nha-dat">xem tất cả bất động sản</a>.</p>
                </div>
            `;
        }

        // Find or create no-results container
        let noResultsContainer = document.querySelector('.no-results-container');
        if (!noResultsContainer) {
            // Create new no-results container
            noResultsContainer = document.createElement('div');
            noResultsContainer.className = 'no-results-container';

            // Insert after properties grid
            if (this.resultsContainer && this.resultsContainer.parentNode) {
                this.resultsContainer.parentNode.insertBefore(noResultsContainer, this.resultsContainer.nextSibling);
            }
            console.log('🔧 AJAX Search: Created new no-results container');
        }

        // Update no-results content
        noResultsContainer.innerHTML = `
            <div class="no-results-content">
                <div class="no-results-icon">
                    <i class="bi bi-house-x"></i>
                </div>
                <h3 class="no-results-title">Không tìm thấy kết quả phù hợp</h3>
                <p class="no-results-text">Không tìm thấy bất động sản phù hợp với tiêu chí tìm kiếm của bạn.</p>
                ${suggestionsHTML}
            </div>
        `;

        // Show no-results container
        noResultsContainer.style.display = '';
        console.log('✅ AJAX Search: No-results message displayed with suggestions');
    }

    /**
     * Show loading state with Facebook-style skeleton
     */
    showLoading() {
        // Don't show skeleton if we're already in page loading mode
        if (this.isPageLoading) {
            return;
        }

        // Don't show skeleton if this is initial page load with server data
        if (this.isInitialPageLoad && this.hasServerData) {
            console.log('✅ Hybrid: Skipping skeleton for initial load with server data');
            return;
        }

        // Hide real content and show skeleton
        this.showSkeletonResults();
    }

    /**
     * Show Facebook-style skeleton loading for entire results section
     */
    showSkeletonResults() {
        // Hide results header content immediately
        if (this.resultsHeader) {
            this.resultsHeader.style.display = 'none';
        }

        // Hide results container immediately
        if (this.resultsContainer) {
            this.resultsContainer.style.display = 'none';
        }

        // Remove any existing skeleton first
        const existingSkeleton = document.querySelector('.results-skeleton');
        if (existingSkeleton) {
            existingSkeleton.remove();
        }

        // Create and show skeleton immediately
        this.createResultsSkeleton();
    }

    /**
     * Create Facebook-style skeleton for results
     */
    createResultsSkeleton() {
        const skeletonHTML = `
            <div class="results-skeleton">
                <!-- Skeleton Header -->
                <div class="skeleton-header">
                    <div class="skeleton-title"></div>
                    <div class="skeleton-count"></div>
                </div>

                <!-- Skeleton Properties Grid -->
                <div class="skeleton-properties-grid">
                    ${this.generateSkeletonCards(8)}
                </div>
            </div>
        `;

        // Insert skeleton after results header with immediate display
        const mainContentSection = document.querySelector('.main-content-section .container');
        if (mainContentSection) {
            mainContentSection.insertAdjacentHTML('beforeend', skeletonHTML);

            // Force immediate render
            const skeleton = document.querySelector('.results-skeleton');
            if (skeleton) {
                skeleton.style.display = 'block';
                // Force browser to render immediately
                skeleton.offsetHeight;
            }
        }
    }

    /**
     * Generate skeleton property cards
     */
    generateSkeletonCards(count) {
        let cards = '';
        for (let i = 0; i < count; i++) {
            cards += `
                <div class="skeleton-property-card">
                    <div class="skeleton-image"></div>
                    <div class="skeleton-content">
                        <div class="skeleton-title-line"></div>
                        <div class="skeleton-location-line"></div>
                        <div class="skeleton-features">
                            <div class="skeleton-feature"></div>
                            <div class="skeleton-feature"></div>
                            <div class="skeleton-feature"></div>
                        </div>
                        <div class="skeleton-price-line"></div>
                    </div>
                </div>
            `;
        }
        return cards;
    }

    /**
     * Remove skeleton and show real content
     */
    removeSkeletonResults() {
        // Remove skeleton first
        const skeleton = document.querySelector('.results-skeleton');
        if (skeleton) {
            skeleton.remove();
        }

        // Show real content immediately and synchronously
        if (this.resultsHeader) {
            this.resultsHeader.style.display = '';
            // Force immediate render
            this.resultsHeader.offsetHeight;
        }

        if (this.resultsContainer) {
            this.resultsContainer.style.display = '';
            // Force immediate render
            this.resultsContainer.offsetHeight;
        }
    }

    /**
     * Hide loading state and show real content
     */
    hideLoading() {
        // Remove skeleton and show real content
        this.removeSkeletonResults();

        // Also remove old loading overlay if exists
        const loadingElement = document.querySelector('.ajax-search-loading');
        if (loadingElement) {
            loadingElement.remove();
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        this.resultsContainer.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong>Lỗi:</strong> ${message}
            </div>
        `;
    }

    /**
     * Update URL without page reload
     */
    updateUrl(newUrl, params) {
        const state = {
            ajaxSearch: true,
            params: params,
            url: newUrl
        };

        history.pushState(state, '', newUrl);
    }

    /**
     * Load search from browser history state
     */
    loadSearchFromState(state) {
        // Update form fields
        Object.keys(state.params).forEach(key => {
            const field = this.searchForm.querySelector(`[name="${key}"]`);
            if (field) {
                field.value = state.params[key];
            }
        });

        // Perform search
        this.performSearch();
    }

    /**
     * Initialize Advanced Filters Modal
     */
    initializeAdvancedFiltersModal() {
        // Wait for AdvancedFiltersModal to be available
        if (typeof AdvancedFiltersModal !== 'undefined') {
            this.advancedFiltersModal = new AdvancedFiltersModal();

            // Set callback for when filters are applied từ modal
            this.advancedFiltersModal.setOnApplyCallback((filters) => {
                console.log('Advanced filters applied from modal:', filters);
                this.updateAdvancedFiltersBadge();
                // Giữ auto-search cho modal - khi user bấm "Xem kết quả" trong modal
                this.performSearch();
            });

            console.log('✅ Advanced Filters Modal integrated');
        } else {
            console.warn('⚠️ AdvancedFiltersModal not available');
        }
    }

    /**
     * Open Advanced Filters Modal
     */
    openAdvancedFiltersModal() {
        if (this.advancedFiltersModal) {
            this.advancedFiltersModal.open();
        } else {
            console.warn('⚠️ Advanced Filters Modal not initialized');
        }
    }

    /**
     * Update Advanced Filters Badge
     */
    updateAdvancedFiltersBadge() {
        const advancedFiltersBtn = document.getElementById('advancedFiltersBtn');
        const existingBadge = document.getElementById('advancedFiltersBadge');

        if (!advancedFiltersBtn) return;

        // Get current filter values
        const bedrooms = document.getElementById('searchBedrooms')?.value || '';
        const bathrooms = document.getElementById('searchBathrooms')?.value || '';
        const area = document.getElementById('searchArea')?.value || '';
        const direction = document.getElementById('searchDirection')?.value || '';

        // Count active filters
        const activeFiltersCount = [bedrooms, bathrooms, area, direction].filter(value => value !== '').length;

        if (activeFiltersCount > 0) {
            if (existingBadge) {
                existingBadge.textContent = activeFiltersCount;
            } else {
                const badge = document.createElement('span');
                badge.className = 'badge bg-primary ms-1';
                badge.id = 'advancedFiltersBadge';
                badge.textContent = activeFiltersCount;
                advancedFiltersBtn.appendChild(badge);
            }
        } else {
            if (existingBadge) {
                existingBadge.remove();
            }
        }
    }
}

// Initialize AJAX search when script loads
const ajaxSearch = new AjaxSearch();
