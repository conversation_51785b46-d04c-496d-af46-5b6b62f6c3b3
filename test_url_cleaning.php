<?php

// Simple test for URL cleaning logic
echo "<h1>Test URL Cleaning Logic</h1>\n";

// Simulate the problematic $_GET array
$testCases = [
    [
        'name' => 'Problematic case with url parameter',
        'get_params' => [
            'url' => 'cho-thue-can-ho',
            'sort' => 'price_desc'
        ],
        'expected_clean' => ['sort' => 'price_desc']
    ],
    [
        'name' => 'Clean case with valid parameters',
        'get_params' => [
            'keyword' => 'test',
            'bathrooms' => '2',
            'sort' => 'price_asc'
        ],
        'expected_clean' => [
            'keyword' => 'test',
            'bathrooms' => '2',
            'sort' => 'price_asc'
        ]
    ],
    [
        'name' => 'Mixed case with unwanted parameters',
        'get_params' => [
            'keyword' => 'test',
            'url' => 'cho-thue-can-ho',
            'action' => 'search',
            'controller' => 'SearchController',
            'bathrooms' => '2'
        ],
        'expected_clean' => [
            'keyword' => 'test',
            'bathrooms' => '2'
        ]
    ]
];

// Function to clean query parameters (extracted from SearchController logic)
function cleanQueryParams($queryParams) {
    // Loại bỏ các tham số không mong muốn
    $unwantedParams = ['url', 'action', 'controller'];
    foreach ($unwantedParams as $param) {
        unset($queryParams[$param]);
    }
    return $queryParams;
}

echo "<h2>Testing URL Cleaning Logic</h2>\n";

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr><th>Test Case</th><th>Input</th><th>Expected</th><th>Actual</th><th>Status</th></tr>\n";

foreach ($testCases as $testCase) {
    $input = $testCase['get_params'];
    $expected = $testCase['expected_clean'];
    $actual = cleanQueryParams($input);
    
    $isCorrect = ($actual == $expected);
    $status = $isCorrect ? '✅ PASS' : '❌ FAIL';
    $statusColor = $isCorrect ? 'green' : 'red';
    
    echo "<tr>\n";
    echo "<td>{$testCase['name']}</td>\n";
    echo "<td><code>" . json_encode($input) . "</code></td>\n";
    echo "<td><code>" . json_encode($expected) . "</code></td>\n";
    echo "<td><code>" . json_encode($actual) . "</code></td>\n";
    echo "<td style='color: $statusColor; font-weight: bold;'>$status</td>\n";
    echo "</tr>\n";
}

echo "</table>\n";

// Test URL building
echo "<h2>Testing URL Building</h2>\n";

function buildCleanSortUrl($baseUrl, $queryParams, $newSort) {
    // Clean the query parameters
    $cleanParams = cleanQueryParams($queryParams);
    
    // Update sort parameter
    if ($newSort === 'default') {
        unset($cleanParams['sort']);
    } else {
        $cleanParams['sort'] = $newSort;
    }
    
    // Build URL
    if (empty($cleanParams)) {
        return $baseUrl;
    } else {
        return $baseUrl . '?' . http_build_query($cleanParams);
    }
}

$urlTests = [
    [
        'name' => 'Original problematic case',
        'base_url' => '/thuenhadanang/cho-thue-can-ho',
        'query_params' => ['url' => 'cho-thue-can-ho', 'sort' => 'price_desc'],
        'new_sort' => 'price_asc',
        'expected' => '/thuenhadanang/cho-thue-can-ho?sort=price_asc'
    ],
    [
        'name' => 'With keyword and unwanted params',
        'base_url' => '/thuenhadanang/cho-thue-can-ho',
        'query_params' => ['keyword' => 'test', 'url' => 'cho-thue-can-ho', 'action' => 'search'],
        'new_sort' => 'price_desc',
        'expected' => '/thuenhadanang/cho-thue-can-ho?keyword=test&sort=price_desc'
    ],
    [
        'name' => 'Reset to default sort',
        'base_url' => '/thuenhadanang/cho-thue-can-ho',
        'query_params' => ['keyword' => 'test', 'sort' => 'price_desc'],
        'new_sort' => 'default',
        'expected' => '/thuenhadanang/cho-thue-can-ho?keyword=test'
    ]
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr><th>Test Case</th><th>Input Params</th><th>New Sort</th><th>Expected URL</th><th>Actual URL</th><th>Status</th></tr>\n";

foreach ($urlTests as $test) {
    $actualUrl = buildCleanSortUrl($test['base_url'], $test['query_params'], $test['new_sort']);
    $isCorrect = ($actualUrl === $test['expected']);
    $status = $isCorrect ? '✅ PASS' : '❌ FAIL';
    $statusColor = $isCorrect ? 'green' : 'red';
    
    echo "<tr>\n";
    echo "<td>{$test['name']}</td>\n";
    echo "<td><code>" . json_encode($test['query_params']) . "</code></td>\n";
    echo "<td><code>{$test['new_sort']}</code></td>\n";
    echo "<td><code>" . htmlspecialchars($test['expected']) . "</code></td>\n";
    echo "<td><code>" . htmlspecialchars($actualUrl) . "</code></td>\n";
    echo "<td style='color: $statusColor; font-weight: bold;'>$status</td>\n";
    echo "</tr>\n";
}

echo "</table>\n";

// Summary
$totalTests = count($testCases) + count($urlTests);
$passedTests = 0;

foreach ($testCases as $testCase) {
    $actual = cleanQueryParams($testCase['get_params']);
    if ($actual == $testCase['expected_clean']) {
        $passedTests++;
    }
}

foreach ($urlTests as $test) {
    $actualUrl = buildCleanSortUrl($test['base_url'], $test['query_params'], $test['new_sort']);
    if ($actualUrl === $test['expected']) {
        $passedTests++;
    }
}

$successRate = ($passedTests / $totalTests) * 100;

echo "<h2>Summary</h2>\n";

if ($passedTests === $totalTests) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<h4 style='color: green;'>🎉 ALL TESTS PASSED!</h4>\n";
    echo "<p style='color: green;'><strong>Success Rate:</strong> $passedTests/$totalTests (100%)</p>\n";
    echo "<p style='color: green;'>The URL cleaning logic is working correctly. The 'url=' parameter will be removed from sort URLs.</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<h4 style='color: red;'>❌ SOME TESTS FAILED</h4>\n";
    echo "<p style='color: red;'><strong>Success Rate:</strong> $passedTests/$totalTests ($successRate%)</p>\n";
    echo "<p style='color: red;'>Please check the implementation.</p>\n";
    echo "</div>\n";
}

echo "<h2>Expected Behavior</h2>\n";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
echo "<h4 style='color: #0066cc;'>Before Fix:</h4>\n";
echo "<p><code>cho-thue-can-ho?url=cho-thue-can-ho&sort=price_desc</code></p>\n";
echo "<h4 style='color: #0066cc;'>After Fix:</h4>\n";
echo "<p><code>cho-thue-can-ho?sort=price_desc</code></p>\n";
echo "<p style='color: #0066cc;'>The unwanted 'url=' parameter should be removed from all generated URLs.</p>\n";
echo "</div>\n";

?>
