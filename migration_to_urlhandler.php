<?php

/**
 * Migration script to help transition from old URL handling to UrlHandler
 * This script helps identify and test URL patterns that need to be supported
 */

define('BASE_PATH', __DIR__);
define('APP_PATH', BASE_PATH . '/app');

// Load required files
require_once APP_PATH . '/libraries/Database.php';
require_once APP_PATH . '/libraries/UrlHandler.php';
require_once APP_PATH . '/models/PropertyType.php';
require_once APP_PATH . '/models/Ward.php';

echo "<h1>Migration to UrlHandler</h1>\n";

// Common URL patterns found in the old system
$legacyUrls = [
    // Basic patterns
    'cho-thue-nha-dat',
    'cho-thue-can-ho',
    'cho-thue-nha-rieng',
    'cho-thue-phong-tro',
    
    // Type + Ward patterns
    'cho-thue-can-ho-tai-hai-chau-1',
    'cho-thue-nha-rieng-tai-thanh-khe',
    'cho-thue-phong-tro-tai-son-tra',
    
    // <PERSON> only patterns
    'cho-thue-nha-dat-tai-hai-chau-1',
    'cho-thue-nha-dat-tai-thanh-khe',
    
    // With price
    'cho-thue-can-ho/gia-5-7-trieu',
    'cho-thue-nha-dat/gia-tren-10-trieu',
    'cho-thue-can-ho-tai-hai-chau-1/gia-3-5-trieu',
    
    // With area
    'cho-thue-nha-dat/dt-50-70m2',
    'cho-thue-can-ho/dt-tren-90m2',
    
    // With bedrooms
    'cho-thue-can-ho/2pn',
    'cho-thue-nha-rieng/4pn-tro-len',
    
    // Complex combinations
    'cho-thue-can-ho-tai-hai-chau-1/gia-5-7-trieu-dt-50-70m2-2pn',
    'cho-thue-nha-rieng/gia-tren-10-trieu-dt-tren-90m2-3pn',
    'cho-thue-nha-dat-tai-thanh-khe/gia-3-5-trieu-1pn',
    
    // Special cases
    'cho-thue-can-ho-an-hai-bac/gia-5-7-trieu-dt-50-70m2-2pn',
    'thue-can-ho',
    'thue-nha-dat-gia-tu-5-7-trieu'
];

try {
    $urlHandler = new UrlHandler();
    
    echo "<h2>Testing Legacy URL Patterns</h2>\n";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Legacy URL</th><th>Parsed Successfully</th><th>Type</th><th>Ward</th><th>Price</th><th>Area</th><th>Bedrooms</th><th>Generated URL</th></tr>\n";
    
    $successCount = 0;
    $totalCount = count($legacyUrls);
    
    foreach ($legacyUrls as $url) {
        $result = $urlHandler->parseUrl($url);
        $success = $result['matched'];
        
        if ($success) {
            $successCount++;
        }
        
        // Try to generate URL back
        $generatedUrl = '';
        if ($success) {
            try {
                $params = [
                    'type' => $result['type'],
                    'ward' => $result['ward'],
                    'price' => $result['price'],
                    'area' => $result['area'],
                    'bedrooms' => $result['bedrooms']
                ];
                $generatedUrl = $urlHandler->buildUrl($params);
                $generatedUrl = str_replace('/thuenhadanang/', '', $generatedUrl);
            } catch (Exception $e) {
                $generatedUrl = 'Error: ' . $e->getMessage();
            }
        }
        
        $successIcon = $success ? '✅' : '❌';
        $rowColor = $success ? '#e8f5e8' : '#ffe8e8';
        
        echo "<tr style='background-color: $rowColor;'>\n";
        echo "<td>$url</td>\n";
        echo "<td>$successIcon</td>\n";
        echo "<td>" . ($result['type'] ?: '-') . "</td>\n";
        echo "<td>" . ($result['ward'] ?: '-') . "</td>\n";
        echo "<td>" . ($result['price'] ?: '-') . "</td>\n";
        echo "<td>" . ($result['area'] ?: '-') . "</td>\n";
        echo "<td>" . ($result['bedrooms'] ?: '-') . "</td>\n";
        echo "<td>$generatedUrl</td>\n";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    $successRate = round(($successCount / $totalCount) * 100, 2);
    echo "<h3>Summary</h3>\n";
    echo "<p><strong>Success Rate:</strong> $successCount/$totalCount ($successRate%)</p>\n";
    
    if ($successRate < 100) {
        echo "<p style='color: red;'><strong>Warning:</strong> Some legacy URLs are not being handled correctly. Please review the failed cases above.</p>\n";
    } else {
        echo "<p style='color: green;'><strong>Success:</strong> All legacy URL patterns are supported!</p>\n";
    }
    
    // Test round-trip conversion
    echo "<h2>Round-trip Testing</h2>\n";
    echo "<p>Testing if URLs can be parsed and then rebuilt correctly...</p>\n";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Original URL</th><th>Rebuilt URL</th><th>Match</th></tr>\n";
    
    $roundTripSuccess = 0;
    $roundTripTotal = 0;
    
    foreach ($legacyUrls as $originalUrl) {
        $parsed = $urlHandler->parseUrl($originalUrl);
        
        if ($parsed['matched']) {
            $roundTripTotal++;
            
            $params = [
                'type' => $parsed['type'],
                'ward' => $parsed['ward'],
                'price' => $parsed['price'],
                'area' => $parsed['area'],
                'bedrooms' => $parsed['bedrooms']
            ];
            
            $rebuiltUrl = str_replace('/thuenhadanang/', '', $urlHandler->buildUrl($params));
            
            // Normalize URLs for comparison
            $normalizedOriginal = trim($originalUrl, '/');
            $normalizedRebuilt = trim($rebuiltUrl, '/');
            
            $match = ($normalizedOriginal === $normalizedRebuilt);
            if ($match) {
                $roundTripSuccess++;
            }
            
            $matchIcon = $match ? '✅' : '❌';
            $rowColor = $match ? '#e8f5e8' : '#ffe8e8';
            
            echo "<tr style='background-color: $rowColor;'>\n";
            echo "<td>$originalUrl</td>\n";
            echo "<td>$rebuiltUrl</td>\n";
            echo "<td>$matchIcon</td>\n";
            echo "</tr>\n";
        }
    }
    
    echo "</table>\n";
    
    if ($roundTripTotal > 0) {
        $roundTripRate = round(($roundTripSuccess / $roundTripTotal) * 100, 2);
        echo "<p><strong>Round-trip Success Rate:</strong> $roundTripSuccess/$roundTripTotal ($roundTripRate%)</p>\n";
    }
    
    // Performance test
    echo "<h2>Performance Test</h2>\n";
    $testUrl = 'cho-thue-can-ho-tai-hai-chau-1/gia-5-7-trieu-dt-50-70m2-2pn';
    $iterations = 1000;
    
    $startTime = microtime(true);
    for ($i = 0; $i < $iterations; $i++) {
        $urlHandler->parseUrl($testUrl);
    }
    $parseTime = microtime(true) - $startTime;
    
    $testParams = [
        'type' => 'can-ho',
        'ward' => 'hai-chau-1',
        'price' => '5-7',
        'area' => '50-70',
        'bedrooms' => '2'
    ];
    
    $startTime = microtime(true);
    for ($i = 0; $i < $iterations; $i++) {
        $urlHandler->buildUrl($testParams);
    }
    $buildTime = microtime(true) - $startTime;
    
    echo "<p><strong>Parse Performance:</strong> " . round($parseTime * 1000, 2) . "ms for $iterations iterations (" . round(($parseTime / $iterations) * 1000, 4) . "ms per call)</p>\n";
    echo "<p><strong>Build Performance:</strong> " . round($buildTime * 1000, 2) . "ms for $iterations iterations (" . round(($buildTime / $iterations) * 1000, 4) . "ms per call)</p>\n";
    
    // Recommendations
    echo "<h2>Migration Recommendations</h2>\n";
    echo "<ul>\n";
    
    if ($successRate >= 95) {
        echo "<li style='color: green;'>✅ UrlHandler successfully handles most legacy URL patterns</li>\n";
    } else {
        echo "<li style='color: red;'>❌ UrlHandler needs improvement to handle more legacy patterns</li>\n";
    }
    
    if ($roundTripRate >= 90) {
        echo "<li style='color: green;'>✅ Round-trip URL conversion works well</li>\n";
    } else {
        echo "<li style='color: orange;'>⚠️ Round-trip conversion needs improvement for consistency</li>\n";
    }
    
    if ($parseTime < 0.1) {
        echo "<li style='color: green;'>✅ Parse performance is excellent</li>\n";
    } else {
        echo "<li style='color: orange;'>⚠️ Consider optimizing parse performance</li>\n";
    }
    
    if ($buildTime < 0.1) {
        echo "<li style='color: green;'>✅ Build performance is excellent</li>\n";
    } else {
        echo "<li style='color: orange;'>⚠️ Consider optimizing build performance</li>\n";
    }
    
    echo "<li>📝 Update all SearchController methods to use UrlHandler</li>\n";
    echo "<li>📝 Replace complex if-else logic in index.php with UrlHandler calls</li>\n";
    echo "<li>📝 Add comprehensive error handling and logging</li>\n";
    echo "<li>📝 Create unit tests for all URL patterns</li>\n";
    echo "<li>📝 Monitor production logs for any unhandled URL patterns</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<h2>Next Steps</h2>\n";
echo "<ol>\n";
echo "<li>Review any failed URL patterns above and update UrlHandler if needed</li>\n";
echo "<li>Deploy UrlHandler to staging environment for testing</li>\n";
echo "<li>Monitor application logs for any URL parsing issues</li>\n";
echo "<li>Gradually replace old URL handling code with UrlHandler calls</li>\n";
echo "<li>Add comprehensive unit tests</li>\n";
echo "<li>Deploy to production with careful monitoring</li>\n";
echo "</ol>\n";

?>
