<?php
require_once __DIR__ . '/../libraries/Database.php';

class Ward {
    private $db;

    public function __construct() {
        $this->db = new Database;
    }

    // L<PERSON>y tất cả phường/xã
    public function getAllWards() {
        $this->db->query('SELECT * FROM wards ORDER BY name ASC');
        return $this->db->resultSet();
    }

    // Lấy phường/xã theo ID
    public function getWardById($id) {
        $this->db->query('SELECT * FROM wards WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }

    // Lấy phường/xã theo slug
    public function getWardBySlug($slug) {
        $this->db->query('SELECT * FROM wards WHERE slug = :slug');
        $this->db->bind(':slug', $slug);
        return $this->db->single();
    }

    // L<PERSON>y tất cả phường/xã theo trạng thái
    public function getWardsByStatus($status = 1) {
        $this->db->query('SELECT * FROM wards WHERE status = :status ORDER BY name ASC');
        $this->db->bind(':status', $status);
        return $this->db->resultSet();
    }

    // Tìm kiếm phường/xã
    public function searchWards($searchTerm) {
        $this->db->query('SELECT id, name FROM wards
                         WHERE name LIKE :search_name AND status = 1
                         ORDER BY name ASC
                         LIMIT 20');
        $this->db->bind(':search_name', '%' . $searchTerm . '%');
        return $this->db->resultSet();
    }

    // Thêm phường/xã mới
    public function create($data) {
        $this->db->query('INSERT INTO wards (name, slug, status) VALUES (:name, :slug, :status)');

        // Bind values
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':slug', $data['slug']);
        $this->db->bind(':status', $data['status'] ?? 1);

        // Execute
        return $this->db->execute();
    }

    // Cập nhật phường/xã
    public function update($data) {
        $this->db->query('UPDATE wards SET name = :name, slug = :slug, status = :status WHERE id = :id');

        // Bind values
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':slug', $data['slug']);
        $this->db->bind(':status', $data['status']);

        // Execute
        return $this->db->execute();
    }

    // Xóa phường/xã
    public function delete($id) {
        $this->db->query('DELETE FROM wards WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->execute();
    }

    // Kiểm tra xem slug đã tồn tại chưa (trừ ID hiện tại)
    public function isSlugExists($slug, $id = 0) {
        $this->db->query('SELECT COUNT(*) as count FROM wards WHERE slug = :slug AND id != :id');
        $this->db->bind(':slug', $slug);
        $this->db->bind(':id', $id);
        $row = $this->db->single();
        return $row->count > 0;
    }

    // Tạo slug từ tên
    public function createSlug($name) {
        // Chuyển đổi tiếng Việt sang không dấu
        $slug = $this->convertToSlug($name);

        // Kiểm tra xem slug đã tồn tại chưa
        $originalSlug = $slug;
        $count = 1;

        while ($this->isSlugExists($slug)) {
            $slug = $originalSlug . '-' . $count;
            $count++;
        }

        return $slug;
    }

    // Hàm chuyển đổi tiếng Việt sang không dấu
    private function convertToSlug($string) {
        $string = trim($string);
        $string = preg_replace('/[^a-zA-Z0-9ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễếệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ\s]/', '', $string);
        $string = preg_replace('/[\s]+/', ' ', $string);
        $string = str_replace(' ', '-', $string);

        $string = strtolower($string);

        // Chuyển đổi tiếng Việt sang không dấu
        $vietnamese = array(
            'à', 'á', 'ạ', 'ả', 'ã', 'â', 'ầ', 'ấ', 'ậ', 'ẩ', 'ẫ', 'ă', 'ằ', 'ắ', 'ặ', 'ẳ', 'ẵ',
            'è', 'é', 'ẹ', 'ẻ', 'ẽ', 'ê', 'ề', 'ế', 'ệ', 'ể', 'ễ',
            'ì', 'í', 'ị', 'ỉ', 'ĩ',
            'ò', 'ó', 'ọ', 'ỏ', 'õ', 'ô', 'ồ', 'ố', 'ộ', 'ổ', 'ỗ', 'ơ', 'ờ', 'ớ', 'ợ', 'ở', 'ỡ',
            'ù', 'ú', 'ụ', 'ủ', 'ũ', 'ư', 'ừ', 'ứ', 'ự', 'ử', 'ữ',
            'ỳ', 'ý', 'ỵ', 'ỷ', 'ỹ',
            'đ',
            'À', 'Á', 'Ạ', 'Ả', 'Ã', 'Â', 'Ầ', 'Ấ', 'Ậ', 'Ẩ', 'Ẫ', 'Ă', 'Ằ', 'Ắ', 'Ặ', 'Ẳ', 'Ẵ',
            'È', 'É', 'Ẹ', 'Ẻ', 'Ẽ', 'Ê', 'Ề', 'Ế', 'Ệ', 'Ể', 'Ễ',
            'Ì', 'Í', 'Ị', 'Ỉ', 'Ĩ',
            'Ò', 'Ó', 'Ọ', 'Ỏ', 'Õ', 'Ô', 'Ồ', 'Ố', 'Ộ', 'Ổ', 'Ỗ', 'Ơ', 'Ờ', 'Ớ', 'Ợ', 'Ở', 'Ỡ',
            'Ù', 'Ú', 'Ụ', 'Ủ', 'Ũ', 'Ư', 'Ừ', 'Ứ', 'Ự', 'Ử', 'Ữ',
            'Ỳ', 'Ý', 'Ỵ', 'Ỷ', 'Ỹ',
            'Đ'
        );

        $latin = array(
            'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
            'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
            'i', 'i', 'i', 'i', 'i',
            'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
            'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
            'y', 'y', 'y', 'y', 'y',
            'd',
            'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
            'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
            'i', 'i', 'i', 'i', 'i',
            'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
            'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
            'y', 'y', 'y', 'y', 'y',
            'd'
        );

        $string = str_replace($vietnamese, $latin, $string);

        return $string;
    }
}
