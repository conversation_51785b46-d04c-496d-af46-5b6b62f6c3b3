<?php
/**
 * API endpoint for AJAX search functionality
 * Handles search requests and returns JSON responses
 */

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define paths (only if not already defined)
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__));
}
if (!defined('APP_PATH')) {
    define('APP_PATH', BASE_PATH . '/app');
}
if (!defined('VIEW_PATH')) {
    define('VIEW_PATH', APP_PATH . '/views');
}
if (!defined('LAYOUT_PATH')) {
    define('LAYOUT_PATH', VIEW_PATH . '/layout');
}

// Include required files
require_once BASE_PATH . '/app/controllers/BaseController.php';
require_once BASE_PATH . '/app/controllers/SearchApiController.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Only GET requests are allowed');
    }

    // Check if this is an AJAX request
    $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
              strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

    if (!$isAjax) {
        // Allow direct access for testing
        error_log('API accessed directly (not AJAX)');
    }

    // Create controller instance
    $controller = new SearchApiController();

    // Route to appropriate method based on action parameter
    $action = isset($_GET['action']) ? $_GET['action'] : 'search';

    switch ($action) {
        case 'search':
            $controller->ajaxSearch();
            break;

        case 'filters':
            $controller->getFilterOptions();
            break;

        default:
            throw new Exception('Invalid action: ' . $action);
    }

} catch (Exception $e) {
    // Log error
    error_log('API Error: ' . $e->getMessage());
    error_log('Stack trace: ' . $e->getTraceAsString());

    // Return error response
    http_response_code(500);

    $response = [
        'success' => false,
        'error' => [
            'message' => 'Có lỗi xảy ra khi xử lý yêu cầu',
            'details' => $e->getMessage(),
            'code' => $e->getCode()
        ],
        'timestamp' => time()
    ];

    echo json_encode($response);
}
?>
