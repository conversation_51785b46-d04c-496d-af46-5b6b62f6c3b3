/**
 * Mobile Search System
 * Handles mobile-specific search functionality with modal filters
 */

class MobileSearch {
    constructor() {
        this.isLoading = false;
        this.currentRequest = null;
        this.modal = null;
        this.debounceTimer = null;

        this.init();
    }

    /**
     * Initialize mobile search system
     */
    init() {
        console.log('🔍 Initializing Mobile Search System...');

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
        } else {
            this.setupEventListeners();
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Mobile search form submission
        const mobileForm = document.getElementById('mobileSearchForm');
        if (mobileForm) {
            mobileForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // Mobile filter button
        const filterBtn = document.getElementById('mobileFilterBtn');
        if (filterBtn) {
            filterBtn.addEventListener('click', () => this.openFiltersModal());
        }

        // Mobile search button
        const searchBtn = document.getElementById('mobileSearchBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', (e) => this.handleSearchClick(e));
        }

        // Keyword input with debounce
        const keywordInput = document.getElementById('mobileSearchKeyword');
        if (keywordInput) {
            keywordInput.addEventListener('input', (e) => this.handleKeywordInput(e));
        }

        // Initialize modal
        this.initModal();

        console.log('✅ Mobile Search event listeners setup complete');
    }

    /**
     * Initialize Bootstrap modal
     */
    initModal() {
        const modalElement = document.getElementById('mobileFiltersModal');
        if (modalElement && window.bootstrap) {
            this.modal = new bootstrap.Modal(modalElement, {
                backdrop: true,
                keyboard: true,
                focus: true
            });

            // Modal event listeners
            this.setupModalEventListeners();
        }
    }

    /**
     * Setup modal event listeners
     */
    setupModalEventListeners() {
        // Apply filters button
        const applyBtn = document.getElementById('mobileFiltersApply');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => this.applyFilters());
        }

        // Clear filters button
        const clearBtn = document.getElementById('mobileFiltersClear');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearFilters());
        }

        // Update badge when modal is shown
        const modalElement = document.getElementById('mobileFiltersModal');
        if (modalElement) {
            modalElement.addEventListener('shown.bs.modal', () => this.syncModalWithForm());
        }
    }

    /**
     * Handle form submission
     */
    handleFormSubmit(e) {
        e.preventDefault();
        console.log('📱 Mobile form submitted');

        // Always use mobile AJAX search (with built-in fallback to SEO URL)
        this.performAjaxSearch();
    }

    /**
     * Handle search button click
     */
    handleSearchClick(e) {
        e.preventDefault();
        console.log('📱 Mobile search button clicked');

        // Always use mobile AJAX search (with built-in fallback to SEO URL)
        this.performAjaxSearch();
    }

    /**
     * Handle keyword input with debounce
     */
    handleKeywordInput(e) {
        // Clear previous timer
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        // Set new timer for debounced search
        this.debounceTimer = setTimeout(() => {
            const keyword = e.target.value.trim();
            if (keyword.length >= 2) {
                console.log('📱 Mobile keyword search:', keyword);
                // Optionally trigger search on keyword input
                // this.performAjaxSearch();
            }
        }, 800);
    }

    /**
     * Open filters modal
     */
    openFiltersModal() {
        console.log('📱 Opening mobile filters modal');

        if (this.modal) {
            this.syncModalWithForm();
            this.modal.show();
        }
    }

    /**
     * Sync modal fields with form values
     */
    syncModalWithForm() {
        console.log('📱 Syncing modal with form values');

        // Get current form values
        const formData = this.getFormData();

        // Update modal fields
        Object.keys(formData).forEach(key => {
            const modalField = document.getElementById(`mobileFilter${key.charAt(0).toUpperCase() + key.slice(1)}`);
            if (modalField) {
                modalField.value = formData[key] || '';
            }
        });
    }

    /**
     * Apply filters from modal
     */
    applyFilters() {
        console.log('📱 Applying mobile filters');

        // Get values from modal
        const modalData = this.getModalData();
        console.log('📱 Modal data:', modalData);

        // Update hidden form fields
        this.updateFormFields(modalData);
        console.log('📱 Updated form fields');

        // Update filter badge
        this.updateFilterBadge(modalData);
        console.log('📱 Updated filter badge');

        // Close modal
        if (this.modal) {
            this.modal.hide();
            console.log('📱 Modal closed');
        }

        // Perform search
        console.log('📱 Starting AJAX search...');
        this.performAjaxSearch();
    }

    /**
     * Clear all filters
     */
    clearFilters() {
        console.log('📱 Clearing mobile filters');

        // Clear modal fields
        const modalForm = document.getElementById('mobileFiltersForm');
        if (modalForm) {
            const selects = modalForm.querySelectorAll('select');
            selects.forEach(select => {
                select.selectedIndex = 0;
            });
        }

        // Clear hidden form fields
        const emptyData = {
            type: '',
            ward: '',
            price: '',
            bedrooms: '',
            bathrooms: '',
            area: '',
            direction: ''
        };

        this.updateFormFields(emptyData);
        this.updateFilterBadge(emptyData);
    }

    /**
     * Get form data
     */
    getFormData() {
        return {
            keyword: document.getElementById('mobileSearchKeyword')?.value || '',
            type: document.getElementById('mobileSearchType')?.value || '',
            ward: document.getElementById('mobileSearchWard')?.value || '',
            price: document.getElementById('mobileSearchPrice')?.value || '',
            bedrooms: document.getElementById('mobileSearchBedrooms')?.value || '',
            bathrooms: document.getElementById('mobileSearchBathrooms')?.value || '',
            area: document.getElementById('mobileSearchArea')?.value || '',
            direction: document.getElementById('mobileSearchDirection')?.value || ''
        };
    }

    /**
     * Get modal data
     */
    getModalData() {
        return {
            type: document.getElementById('mobileFilterType')?.value || '',
            ward: document.getElementById('mobileFilterWard')?.value || '',
            price: document.getElementById('mobileFilterPrice')?.value || '',
            bedrooms: document.getElementById('mobileFilterBedrooms')?.value || '',
            bathrooms: document.getElementById('mobileFilterBathrooms')?.value || '',
            area: document.getElementById('mobileFilterArea')?.value || '',
            direction: document.getElementById('mobileFilterDirection')?.value || ''
        };
    }

    /**
     * Update form hidden fields
     */
    updateFormFields(data) {
        Object.keys(data).forEach(key => {
            const field = document.getElementById(`mobileSearch${key.charAt(0).toUpperCase() + key.slice(1)}`);
            if (field) {
                field.value = data[key] || '';
            }
        });
    }

    /**
     * Update filter badge
     */
    updateFilterBadge(data) {
        const badge = document.getElementById('mobileFiltersBadge');
        const activeFilters = Object.values(data).filter(value => value && value.trim() !== '').length;

        if (badge) {
            if (activeFilters > 0) {
                badge.textContent = activeFilters;
                badge.style.display = 'inline-block';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    /**
     * Perform AJAX search using mobile API
     */
    async performAjaxSearch() {
        if (this.isLoading) return;

        console.log('📱 Performing mobile AJAX search');

        this.showLoading();

        try {
            const params = this.buildSearchParams();

            // Build mobile API URL - use debug API first
            const searchParams = new URLSearchParams(params);
            searchParams.append('_t', Date.now()); // Cache busting

            // Use simple API with database integration
            const debugApiUrl = '/thuenhadanang/api/mobile-search-debug.php?' + searchParams.toString();
            const simpleApiUrl = '/thuenhadanang/api/mobile-search-simple.php?' + searchParams.toString();
            const mainApiUrl = '/thuenhadanang/api/mobile-search.php?' + searchParams.toString();
            const apiUrl = simpleApiUrl; // Use simple API with database

            console.log('📱 Mobile API URL:', apiUrl);

            // Create AbortController for request cancellation
            const controller = new AbortController();
            this.currentRequest = controller;

            // Make AJAX request to mobile API
            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-Mobile-Search': 'true'
                },
                signal: controller.signal
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('📱 HTTP Error Response:', errorText);
                throw new Error(`HTTP error! status: ${response.status} - ${errorText.substring(0, 100)}`);
            }

            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                const text = await response.text();
                console.error('📱 Non-JSON Response:', text.substring(0, 500));
                throw new Error('Server returned non-JSON response: ' + text.substring(0, 100));
            }

            const data = await response.json();

            if (data.success) {
                console.log('📱 Mobile search completed:', data.data.count, 'properties found');

                // Try to use desktop AJAX system, with retry mechanism
                this.updateSearchResults(data.data, params);

            } else {
                throw new Error(data.error.message || 'Mobile search failed');
            }

        } catch (error) {
            console.error('📱 Mobile search error:', error);
            console.error('📱 Error name:', error.name);
            console.error('📱 Error message:', error.message);
            console.error('📱 Error stack:', error.stack);

            // Check if it's an AbortError (user cancelled)
            if (error.name === 'AbortError') {
                console.log('📱 Search was aborted by user');
                return;
            }

            // Fallback: manual URL redirect instead of form submission
            console.log('📱 Falling back to manual URL redirect due to error');
            const params = this.buildSearchParams();
            console.log('📱 Building manual URL with params:', params);
            this.reloadWithParams(params);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Build search parameters
     */
    buildSearchParams() {
        const formData = this.getFormData();

        // Convert to format expected by desktop AJAX search, filter out empty values
        const params = {};

        // Only add non-empty parameters
        if (formData.keyword && formData.keyword.trim()) params.keyword = formData.keyword.trim();
        if (formData.type && formData.type.trim()) params.type = formData.type.trim();
        if (formData.ward && formData.ward.trim()) params.ward = formData.ward.trim();
        if (formData.price && formData.price.trim()) params.price = formData.price.trim();
        if (formData.bedrooms && formData.bedrooms.trim()) params.bedrooms = formData.bedrooms.trim();
        if (formData.bathrooms && formData.bathrooms.trim()) params.bathrooms = formData.bathrooms.trim();
        if (formData.area && formData.area.trim()) params.area = formData.area.trim();
        if (formData.direction && formData.direction.trim()) params.direction = formData.direction.trim();

        // Only add sort if it's not default
        if (formData.sort && formData.sort !== 'default') params.sort = formData.sort;

        console.log('📱 Built clean search params:', params);
        return params;
    }

    /**
     * Convert direction display value to slug
     */
    convertDirectionToSlug(direction) {
        if (!direction) return '';

        const directionMap = {
            'Đông': 'dong',
            'Tây': 'tay',
            'Nam': 'nam',
            'Bắc': 'bac',
            'Đông Bắc': 'dong-bac',
            'Đông Nam': 'dong-nam',
            'Tây Bắc': 'tay-bac',
            'Tây Nam': 'tay-nam'
        };

        return directionMap[direction] || direction.toLowerCase();
    }

    /**
     * Update search results with retry mechanism
     */
    async updateSearchResults(data, params, retryCount = 0) {
        console.log(`📱 updateSearchResults called (attempt ${retryCount + 1})`);
        console.log('📱 Data received:', data);
        console.log('📱 Params:', params);

        const maxRetries = 3;
        const retryDelay = 500; // 500ms

        // Check if desktop AJAX is available
        console.log('📱 Checking desktop AJAX availability...');
        console.log('📱 window.ajaxSearch exists:', !!window.ajaxSearch);
        console.log('📱 updateResults function exists:', !!(window.ajaxSearch && typeof window.ajaxSearch.updateResults === 'function'));

        if (window.ajaxSearch && typeof window.ajaxSearch.updateResults === 'function') {
            console.log('📱 Using desktop AJAX system for results update');

            try {
                // Desktop updateResults handles skeleton timing and DOM updates automatically
                console.log('📱 Calling window.ajaxSearch.updateResults...');
                window.ajaxSearch.updateResults(data);
                console.log('📱 Desktop AJAX updateResults completed');

                // Update URL without page reload (desktop handles this too, but ensure it's done)
                if (data.metadata && data.metadata.url) {
                    console.log('📱 Updating URL to:', data.metadata.url);
                    this.updateUrlWithoutReload(data.metadata.url, params);
                    console.log('📱 URL update completed');
                } else {
                    console.warn('📱 No metadata.url found in data');
                }

                console.log('📱 Mobile search completed successfully with desktop AJAX - NO RELOAD');
                return; // Success, exit - NO RELOAD

            } catch (error) {
                console.error('📱 Error using desktop AJAX system:', error);
                console.error('📱 Desktop AJAX error stack:', error.stack);
            }
        } else {
            console.log('📱 Desktop AJAX not available, using mobile fallback');

            // Mobile fallback with DOM update (NO RELOAD)
            this.updateResultsMobile(data, params);
            return; // Success with mobile fallback - NO RELOAD
        }

        // If desktop AJAX not available or failed, retry or fallback
        if (retryCount < maxRetries) {
            console.log(`📱 Desktop AJAX not ready, retrying in ${retryDelay}ms (attempt ${retryCount + 1}/${maxRetries})`);

            setTimeout(() => {
                this.updateSearchResults(data, params, retryCount + 1);
            }, retryDelay);

        } else {
            console.log('📱 Desktop AJAX not available after retries, using final mobile fallback');
            console.log('📱 Using mobile DOM update instead of reload...');
            // Final fallback: use mobile DOM update (NO RELOAD)
            this.updateResultsMobile(data, params);
        }
    }

    /**
     * Update results with mobile fallback (DOM update like PC, no reload)
     */
    updateResultsMobile(data, params) {
        console.log('📱 Using mobile fallback for results update');

        // Prepare content first (like PC)
        this.prepareMobileContent(data);

        // Add minimum delay to ensure skeleton was visible (like PC)
        const minSkeletonTime = 300; // Same as PC
        setTimeout(() => {
            // Update URL first
            if (data.metadata && data.metadata.url) {
                this.updateUrlWithoutReload(data.metadata.url, params);
            }

            // Show prepared content (like PC)
            this.showMobileContent();

            console.log('📱 Mobile fallback completed - DOM updated, no reload');

        }, minSkeletonTime);
    }

    /**
     * Prepare mobile content (like PC prepareNewContent)
     */
    prepareMobileContent(data) {
        console.log('📱 Preparing mobile content');

        // Store data for later display
        this.pendingMobileData = data;

        // Pre-render content like PC
        this.updateMobileResultsHeader(data);
        this.updateMobilePropertiesGrid(data.properties);
    }

    /**
     * Show mobile content (like PC showNewContent)
     */
    showMobileContent() {
        console.log('📱 Showing mobile content');

        // Hide skeleton and show real content (like PC)
        this.hideMobileSkeleton();
    }

    /**
     * Update mobile results header (like PC updateResultsHeader)
     */
    updateMobileResultsHeader(data) {
        // Find and update title (same selectors as PC)
        const resultsTitle = document.querySelector('.results-title');
        if (resultsTitle) {
            resultsTitle.textContent = data.metadata.title;
            console.log('📱 Updated results title:', data.metadata.title);
        }

        // Find and update count (same selectors as PC)
        const resultsCount = document.querySelector('.results-count');
        if (resultsCount) {
            resultsCount.textContent = `Tìm thấy ${data.count} bất động sản phù hợp`;
            console.log('📱 Updated results count:', data.count);
        }

        // Update page title (same as PC)
        document.title = data.metadata.title + ' - Thuê Nhà Đà Nẵng';
        console.log('📱 Updated page title');
    }

    /**
     * Update mobile properties grid (like PC updatePropertiesGrid)
     */
    updateMobilePropertiesGrid(properties) {
        // Find results container (same selectors as PC)
        const resultsContainer = document.querySelector('.properties-grid');
        if (!resultsContainer) {
            console.warn('📱 Properties grid container not found');
            return;
        }

        if (properties.length === 0) {
            this.showMobileNoResults(resultsContainer);
            return;
        }

        // Hide any existing no-results container when we have results (same as PC)
        const existingNoResults = document.querySelector('.no-results-container');
        if (existingNoResults) {
            existingNoResults.style.display = 'none';
            console.log('📱 Mobile Search: Hid existing no-results container');
        }

        // Show properties grid container (same as PC)
        if (resultsContainer) {
            resultsContainer.style.display = '';
            console.log('📱 Mobile Search: Showed properties-grid container');
        }

        // Generate HTML like PC (reuse PC's renderPropertyCard if available)
        let html = '';
        properties.forEach(property => {
            if (window.ajaxSearch && typeof window.ajaxSearch.renderPropertyCard === 'function') {
                // Adapt mobile API response to PC format
                const adaptedProperty = this.adaptPropertyForPC(property);
                html += window.ajaxSearch.renderPropertyCard(adaptedProperty);
            } else {
                // Mobile fallback rendering
                html += this.renderMobilePropertyCard(property);
            }
        });

        // Update container (same as PC)
        resultsContainer.innerHTML = html;
        console.log('📱 Updated properties grid with', properties.length, 'properties');
    }

    /**
     * Adapt mobile API property to PC format
     */
    adaptPropertyForPC(property) {
        return {
            ...property,
            // Map mobile API fields to PC expected fields
            imagePath: property.image,
            wardName: property.ward_name,
            priceDisplay: property.formatted_price,
            isNew: property.is_new
        };
    }

    /**
     * Show no results with suggestions (like PC showNoResults)
     */
    showMobileNoResults(container) {
        console.log('🚫 Mobile Search: Showing no results, clearing any existing content');

        // CRITICAL: Clear any existing content in properties grid (same as PC fix)
        if (container) {
            container.innerHTML = ''; // Clear all property cards
            container.style.display = 'none'; // Hide properties grid
            console.log('✅ Mobile Search: Cleared properties-grid content and hid container');
        }

        // Get current search parameters to generate suggestions
        const urlParams = new URLSearchParams(window.location.search);
        const currentPath = window.location.pathname;

        // Extract type and ward from URL path
        const typeMatch = currentPath.match(/cho-thue-([^-]+)(?:-tai-|$)/);
        const wardMatch = currentPath.match(/cho-thue-[^-]+-tai-([^\/]+)/);

        const selectedType = typeMatch ? typeMatch[1] : urlParams.get('type');
        const selectedWard = wardMatch ? wardMatch[1] : urlParams.get('ward');

        // Generate suggestions HTML (same as PC)
        let suggestionsHTML = '';
        if (selectedType && selectedWard) {
            suggestionsHTML = `
                <div class="suggestions">
                    <p>Bạn có thể thử:</p>
                    <ul>
                        <li><a href="/thuenhadanang/cho-thue-${selectedType}">Xem tất cả ${selectedType} cho thuê</a></li>
                        <li><a href="/thuenhadanang/cho-thue-nha-dat-tai-${selectedWard}">Xem tất cả bất động sản tại ${selectedWard}</a></li>
                        <li><a href="/thuenhadanang/cho-thue-nha-dat">Xem tất cả bất động sản cho thuê</a></li>
                    </ul>
                </div>
            `;
        } else if (selectedWard) {
            suggestionsHTML = `
                <div class="suggestions">
                    <p>Hiện tại chưa có bất động sản nào tại khu vực này. Vui lòng thử tìm kiếm ở khu vực khác hoặc
                    <a href="/thuenhadanang/cho-thue-nha-dat">xem tất cả bất động sản</a>.</p>
                </div>
            `;
        }

        // Find or create no-results container (same as PC)
        let noResultsContainer = document.querySelector('.no-results-container');
        if (!noResultsContainer) {
            // Create new no-results container
            noResultsContainer = document.createElement('div');
            noResultsContainer.className = 'no-results-container';

            // Insert after properties grid
            if (container && container.parentNode) {
                container.parentNode.insertBefore(noResultsContainer, container.nextSibling);
            }
            console.log('🔧 Mobile Search: Created new no-results container');
        }

        // Update no-results content (same as PC)
        noResultsContainer.innerHTML = `
            <div class="no-results-content">
                <div class="no-results-icon">
                    <i class="bi bi-house-x"></i>
                </div>
                <h3 class="no-results-title">Không tìm thấy kết quả phù hợp</h3>
                <p class="no-results-text">Không tìm thấy bất động sản phù hợp với tiêu chí tìm kiếm của bạn.</p>
                ${suggestionsHTML}
            </div>
        `;

        // Show no-results container
        noResultsContainer.style.display = '';
        console.log('✅ Mobile Search: No-results message displayed with suggestions');
    }

    /**
     * Mobile property card renderer (matching PC structure exactly)
     */
    renderMobilePropertyCard(property) {
        const featuredBadge = property.featured ? '<span class="property-badge featured-badge">Nổi bật</span>' : '';

        return `
            <div class="property-card" data-featured="${property.featured}" data-new="${property.is_new}">
                <div class="property-image-container">
                    <a href="${property.url}">
                        <img src="${property.image || '/thuenhadanang/public/images/no-image.jpg'}" class="property-image" alt="${property.title}">
                    </a>
                    <div class="property-badges">
                        ${featuredBadge}
                    </div>
                    <button class="wishlist-btn" data-property-id="${property.id}">
                        <i class="bi bi-heart"></i>
                    </button>
                </div>
                <div class="property-content">
                    <div class="property-header">
                        <h3 class="property-title">
                            <a href="${property.url}">${property.title}</a>
                        </h3>
                        <div class="property-location">
                            <i class="bi bi-geo-alt"></i>
                            <span>${property.ward_name}</span>
                        </div>
                    </div>
                    <div class="property-features">
                        ${property.area ? `<span class="feature-item"><i class="bi bi-rulers"></i>${property.area}m²</span>` : ''}
                        ${property.bedrooms ? `<span class="feature-item"><i class="bi bi-door-closed"></i>${property.bedrooms} PN</span>` : ''}
                        ${property.bathrooms ? `<span class="feature-item"><i class="bi bi-droplet"></i>${property.bathrooms} WC</span>` : ''}
                    </div>
                    <div class="property-price">
                        ${property.formatted_price}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Update URL without page reload (mobile implementation)
     */
    updateUrlWithoutReload(newUrl, params) {
        try {
            // Use desktop AJAX updateUrl if available
            if (window.ajaxSearch && typeof window.ajaxSearch.updateUrl === 'function') {
                window.ajaxSearch.updateUrl(newUrl, params);
            } else {
                // Manual URL update
                const state = {
                    mobileSearch: true,
                    params: params,
                    url: newUrl
                };

                history.pushState(state, '', newUrl);
                console.log('📱 URL updated manually to:', newUrl);
            }
        } catch (error) {
            console.error('📱 Error updating URL:', error);
        }
    }

    /**
     * Reload page with search parameters (fallback)
     */
    reloadWithParams(params) {
        console.log('📱 reloadWithParams called with:', params);

        // Always try to build SEO-friendly URL first
        try {
            const seoUrl = this.buildSeoUrl(params);
            console.log('📱 Built SEO URL:', seoUrl);

            if (seoUrl) {
                console.log('📱 Using SEO URL:', seoUrl);
                window.location.href = seoUrl;
                return;
            }
        } catch (error) {
            console.error('📱 Error building SEO URL:', error);
        }

        // Only fallback to query parameters if SEO URL building completely fails
        console.log('📱 SEO URL building failed, using query params fallback');
        const url = new URL('/thuenhadanang/search', window.location.origin);

        // Only add non-empty parameters
        Object.keys(params).forEach(key => {
            if (params[key] && params[key].trim() !== '') {
                url.searchParams.set(key, params[key]);
            }
        });

        console.log('📱 Reloading with query params:', url.toString());
        window.location.href = url.toString();
    }

    /**
     * Build SEO-friendly URL (following PC UrlHandler logic exactly)
     */
    buildSeoUrl(params) {
        console.log('📱 Building SEO URL with params:', params);

        // Use same logic as SearchApiController.buildSearchUrl()
        // Only include non-empty parameters
        const urlParams = {};

        if (params.type && params.type.trim()) urlParams.type = params.type.trim();
        if (params.ward && params.ward.trim()) urlParams.ward = params.ward.trim();
        if (params.price && params.price.trim()) urlParams.price = params.price.trim();
        if (params.area && params.area.trim()) urlParams.area = params.area.trim();
        if (params.bedrooms && params.bedrooms.trim()) urlParams.bedrooms = params.bedrooms.trim();
        if (params.bathrooms && params.bathrooms.trim()) urlParams.bathrooms = params.bathrooms.trim();
        if (params.direction && params.direction.trim()) urlParams.direction = params.direction.trim();

        // Add additional params (keyword, sort) - same as PC
        const additionalParams = {};
        if (params.keyword && params.keyword.trim()) {
            additionalParams.keyword = params.keyword.trim();
        }
        if (params.sort && params.sort !== 'default' && params.sort.trim()) {
            additionalParams.sort = params.sort.trim();
        }

        if (Object.keys(additionalParams).length > 0) {
            urlParams.additional_params = additionalParams;
        }

        console.log('📱 Clean URL params for PC builder:', urlParams);

        // Use mobile version of UrlHandler.buildUrl()
        return this.buildUrlLikePC(urlParams);
    }

    /**
     * Build URL like PC UrlHandler.buildUrl() method
     */
    buildUrlLikePC(params) {
        console.log('📱 Building URL like PC with params:', params);

        let url = '/thuenhadanang';
        const { type, ward, price, area, bedrooms, bathrooms, direction, additional_params } = params;

        // Build base segment: /cho-thue-{type}-tai-{ward} (same as PC)
        const baseSegment = this.buildBaseSegment(type, ward);
        if (baseSegment) {
            url += '/' + baseSegment;
        } else {
            // If no base segment, use /search (same as PC fallback)
            url += '/search';
        }

        // Build filter segment: gia-X-trieu-dt-Y-m2-Zpn (same as PC)
        const filterSegment = this.buildFilterSegment(price, area, bedrooms);
        if (filterSegment) {
            url += '/' + filterSegment;
        }

        // Add query parameters (same as PC UrlHandler.buildQueryParams())
        const queryParams = [];

        // Only add non-empty query parameters
        if (bathrooms && bathrooms.trim()) {
            queryParams.push(`bathrooms=${encodeURIComponent(bathrooms.trim())}`);
        }
        if (direction && direction.trim()) {
            queryParams.push(`direction=${encodeURIComponent(direction.trim())}`);
        }

        // Add additional_params (keyword, sort) - same as PC
        if (additional_params) {
            Object.keys(additional_params).forEach(key => {
                const value = additional_params[key];
                if (value && value.toString().trim()) {
                    queryParams.push(`${key}=${encodeURIComponent(value.toString().trim())}`);
                }
            });
        }

        if (queryParams.length > 0) {
            url += '?' + queryParams.join('&');
        }

        console.log('📱 Built URL like PC:', url);
        return url;
    }

    /**
     * Build base segment (cho-thue-type-tai-ward)
     */
    buildBaseSegment(type, ward) {
        // Handle empty type -> use "nha-dat"
        if (!type) {
            type = 'nha-dat';
        }

        if (ward) {
            return 'cho-thue-' + type + '-tai-' + ward;
        } else if (type) {
            return 'cho-thue-' + type;
        }

        return '';
    }

    /**
     * Build filter segment (gia-X-trieu-dt-Y-m2-Zpn)
     */
    buildFilterSegment(price, area, bedrooms) {
        const filterParts = [];

        // Add price
        if (price) {
            filterParts.push('gia-' + this.formatPriceForUrl(price));
        }

        // Add area
        if (area) {
            filterParts.push('dt-' + this.formatAreaForUrl(area));
        }

        // Add bedrooms
        if (bedrooms) {
            if (bedrooms === '4+') {
                filterParts.push('4pn-tro-len');
            } else {
                filterParts.push(bedrooms + 'pn');
            }
        }

        return filterParts.join('-');
    }

    /**
     * Format price for URL (following UrlHandler logic)
     */
    formatPriceForUrl(price) {
        if (price.includes('+')) {
            return 'tren-' + price.replace('+', '') + '-trieu';
        } else if (price.includes('-')) {
            return price + '-trieu';
        }
        return price + '-trieu';
    }

    /**
     * Format area for URL (following UrlHandler logic)
     */
    formatAreaForUrl(area) {
        if (area.includes('+')) {
            return 'tren-' + area.replace('+', '') + '-m2';
        } else if (area.includes('-')) {
            // Format: dt-50-70m2 (no dash before m2)
            return area + 'm2';
        }
        return area + 'm2';
    }

    /**
     * Show loading state with skeleton (like PC)
     */
    showLoading() {
        this.isLoading = true;

        // Disable search button
        const searchBtn = document.getElementById('mobileSearchBtn');
        if (searchBtn) {
            searchBtn.disabled = true;
            // Add loading spinner to button
            const originalText = searchBtn.innerHTML;
            searchBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin me-1"></i>Đang tìm...';
            searchBtn.dataset.originalText = originalText;
        }

        // Use desktop AJAX skeleton if available
        if (window.ajaxSearch && typeof window.ajaxSearch.showLoading === 'function') {
            console.log('📱 Using desktop skeleton loading');
            window.ajaxSearch.showLoading();
        } else {
            console.log('📱 Desktop skeleton not available, using mobile fallback');
            this.showMobileSkeleton();
        }
    }

    /**
     * Hide loading state
     */
    hideLoading() {
        this.isLoading = false;

        // Restore search button
        const searchBtn = document.getElementById('mobileSearchBtn');
        if (searchBtn) {
            searchBtn.disabled = false;
            const originalText = searchBtn.dataset.originalText;
            if (originalText) {
                searchBtn.innerHTML = originalText;
                delete searchBtn.dataset.originalText;
            }
        }

        // Use desktop AJAX hide loading if available
        if (window.ajaxSearch && typeof window.ajaxSearch.hideLoading === 'function') {
            console.log('📱 Using desktop hide loading');
            window.ajaxSearch.hideLoading();
        } else {
            console.log('📱 Desktop hide loading not available, using mobile fallback');
            this.hideMobileSkeleton();
        }
    }

    /**
     * Show mobile skeleton loading (fallback)
     */
    showMobileSkeleton() {
        // Find results container
        const resultsContainer = document.querySelector('.properties-grid, .results-container, #search-results');
        if (!resultsContainer) return;

        // Hide real content
        resultsContainer.style.display = 'none';

        // Remove existing skeleton
        const existingSkeleton = document.querySelector('.mobile-skeleton');
        if (existingSkeleton) {
            existingSkeleton.remove();
        }

        // Create mobile skeleton
        const skeleton = document.createElement('div');
        skeleton.className = 'mobile-skeleton';
        skeleton.innerHTML = this.generateMobileSkeletonHTML();

        // Insert skeleton after results container
        resultsContainer.parentNode.insertBefore(skeleton, resultsContainer.nextSibling);

        // Add skeleton styles if not exists
        this.addMobileSkeletonStyles();
    }

    /**
     * Hide mobile skeleton loading (fallback)
     */
    hideMobileSkeleton() {
        // Remove skeleton
        const skeleton = document.querySelector('.mobile-skeleton');
        if (skeleton) {
            skeleton.remove();
        }

        // Show real content
        const resultsContainer = document.querySelector('.properties-grid, .results-container, #search-results');
        if (resultsContainer) {
            resultsContainer.style.display = '';
        }
    }

    /**
     * Generate mobile skeleton HTML
     */
    generateMobileSkeletonHTML() {
        return `
            <div class="mobile-skeleton-grid">
                ${this.generateMobileSkeletonCards(6)}
            </div>
        `;
    }

    /**
     * Generate mobile skeleton cards
     */
    generateMobileSkeletonCards(count) {
        let cards = '';
        for (let i = 0; i < count; i++) {
            cards += `
                <div class="mobile-skeleton-card">
                    <div class="mobile-skeleton-image"></div>
                    <div class="mobile-skeleton-content">
                        <div class="mobile-skeleton-title"></div>
                        <div class="mobile-skeleton-location"></div>
                        <div class="mobile-skeleton-features">
                            <div class="mobile-skeleton-feature"></div>
                            <div class="mobile-skeleton-feature"></div>
                        </div>
                        <div class="mobile-skeleton-price"></div>
                    </div>
                </div>
            `;
        }
        return cards;
    }

    /**
     * Add mobile skeleton styles
     */
    addMobileSkeletonStyles() {
        if (document.querySelector('#mobile-skeleton-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'mobile-skeleton-styles';
        styles.textContent = `
            .spin {
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .mobile-skeleton {
                margin: 2rem 0;
            }

            .mobile-skeleton-grid {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 1.5rem;
            }

            .mobile-skeleton-card {
                background: white;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .mobile-skeleton-image {
                height: 200px;
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: skeleton-shimmer 1.5s ease-in-out infinite;
            }

            .mobile-skeleton-content {
                padding: 1rem;
            }

            .mobile-skeleton-title {
                height: 20px;
                width: 85%;
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: skeleton-shimmer 1.5s ease-in-out infinite;
                border-radius: 4px;
                margin-bottom: 0.5rem;
            }

            .mobile-skeleton-location {
                height: 16px;
                width: 70%;
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: skeleton-shimmer 1.5s ease-in-out infinite;
                border-radius: 4px;
                margin-bottom: 0.75rem;
            }

            .mobile-skeleton-features {
                display: flex;
                gap: 0.5rem;
                margin-bottom: 0.75rem;
            }

            .mobile-skeleton-feature {
                height: 14px;
                width: 60px;
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: skeleton-shimmer 1.5s ease-in-out infinite;
                border-radius: 4px;
            }

            .mobile-skeleton-price {
                height: 18px;
                width: 50%;
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: skeleton-shimmer 1.5s ease-in-out infinite;
                border-radius: 4px;
            }

            @keyframes skeleton-shimmer {
                0% {
                    background-position: 200% 0;
                }
                100% {
                    background-position: -200% 0;
                }
            }

            /* Responsive mobile skeleton */
            @media (max-width: 1200px) {
                .mobile-skeleton-grid {
                    grid-template-columns: repeat(3, 1fr);
                }
            }

            @media (max-width: 768px) {
                .mobile-skeleton-grid {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 1rem;
                }

                .mobile-skeleton-image {
                    height: 150px;
                }
            }

            @media (max-width: 480px) {
                .mobile-skeleton-grid {
                    grid-template-columns: 1fr;
                }
            }
        `;
        document.head.appendChild(styles);
    }
}

// Initialize mobile search when script loads
let mobileSearch;

// Wait for DOM and other scripts to load
document.addEventListener('DOMContentLoaded', function() {
    // Wait for desktop AJAX to be available or timeout
    const initMobileSearch = () => {
        mobileSearch = new MobileSearch();

        // Make it globally accessible for debugging
        window.mobileSearch = mobileSearch;

        console.log('📱 Mobile Search System initialized');

        // Log desktop AJAX availability
        if (window.ajaxSearch) {
            console.log('📱 Desktop AJAX system detected and available');
        } else {
            console.log('📱 Desktop AJAX system not available, will use fallback');
        }
    };

    // Check if desktop AJAX is already available
    if (window.ajaxSearch) {
        console.log('📱 Desktop AJAX already available, initializing mobile search');
        initMobileSearch();
    } else {
        // Wait a bit for desktop AJAX to load
        let checkCount = 0;
        const maxChecks = 10; // Max 2 seconds
        const checkInterval = 200; // Check every 200ms

        const checkDesktopAjax = () => {
            checkCount++;

            if (window.ajaxSearch) {
                console.log('📱 Desktop AJAX detected after', checkCount * checkInterval, 'ms');
                initMobileSearch();
            } else if (checkCount >= maxChecks) {
                console.log('📱 Desktop AJAX not detected after timeout, initializing mobile search anyway');
                initMobileSearch();
            } else {
                setTimeout(checkDesktopAjax, checkInterval);
            }
        };

        setTimeout(checkDesktopAjax, checkInterval);
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileSearch;
}
