<!-- Include custom CSS for property listing -->
<link rel="stylesheet" href="/thuenhadanang/public/css/property-listing.css">
<!-- Include Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="container mt-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/thuenhadanang" class="text-decoration-none">Trang chủ</a></li>
        <li class="breadcrumb-item"><a href="/thuenhadanang/dashboard" class="text-decoration-none">Bảng đi<PERSON><PERSON> khiển</a></li>
        <li class="breadcrumb-item active" aria-current="page">Đăng tin bất động sản</li>
    </ol>
</nav>

<div class="container py-4">
    <div class="row">
        <!-- Include Sidebar -->
        <?php require_once 'app/views/partials/dashboard-sidebar.php'; ?>

        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card border-0 shadow-sm property-listing-form">
                <div class="card-header bg-white py-3 border-0">
                    <h5 class="mb-0">Đăng tin bất động sản</h5>
                </div>
                <div class="card-body p-4">
                    <!-- Progress Bar -->
                    <div class="step-progress">
                        <div class="progress-bar" role="progressbar" style="width: 50%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="step-indicators">
                        <span class="step-indicator active">Bước 1: Thông tin bất động sản</span>
                        <span class="step-indicator">Bước 2: Hình ảnh</span>
                    </div>

                    <?php if (!empty($data['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $data['error']; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($data['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $data['success']; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form action="/thuenhadanang/property-listing/step1" method="POST" id="propertyForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="type_id" class="form-label">Loại bất động sản <span class="required">*</span></label>
                                    <select class="form-select" id="type_id" name="type_id" required>
                                        <option value="">-- Chọn loại bất động sản --</option>
                                        <?php foreach ($data['propertyTypes'] as $type): ?>
                                            <option value="<?php echo $type->id; ?>" <?php echo (isset($data['formData']['type_id']) && $data['formData']['type_id'] == $type->id) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($type->name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="ward_id" class="form-label">Phường/Xã <span class="required">*</span></label>
                                    <select class="form-select" id="ward_id" name="ward_id" required>
                                        <option value="">-- Chọn phường/xã --</option>
                                        <?php foreach ($data['wards'] as $ward): ?>
                                            <option value="<?php echo $ward->id; ?>" <?php echo (isset($data['formData']['ward_id']) && $data['formData']['ward_id'] == $ward->id) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($ward->name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="street" class="form-label">Đường <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="street" name="street" placeholder="Chỉ nhập tên đường. Ví dụ: Ngô Quyền" required value="<?php echo isset($data['formData']['street']) ? htmlspecialchars($data['formData']['street']) : ''; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="address" class="form-label">Địa chỉ đầy đủ <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="address" name="address" required value="<?php echo isset($data['formData']['address']) ? htmlspecialchars($data['formData']['address']) : ''; ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="price" class="form-label">Giá thuê (VND) <span class="required">*</span></label>
                                    <input type="number" class="form-control" id="price" name="price" min="0" required value="<?php echo isset($data['formData']['price']) ? htmlspecialchars($data['formData']['price']) : ''; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="price_period" class="form-label">Thời hạn thuê <span class="required">*</span></label>
                                    <select class="form-select" id="price_period" name="price_period" required>
                                        <option value="day" <?php echo (isset($data['formData']['price_period']) && $data['formData']['price_period'] == 'day') ? 'selected' : ''; ?>>Ngày</option>
                                        <option value="week" <?php echo (isset($data['formData']['price_period']) && $data['formData']['price_period'] == 'week') ? 'selected' : ''; ?>>Tuần</option>
                                        <option value="month" <?php echo (!isset($data['formData']['price_period']) || $data['formData']['price_period'] == 'month') ? 'selected' : ''; ?>>Tháng</option>
                                        <option value="quarter" <?php echo (isset($data['formData']['price_period']) && $data['formData']['price_period'] == 'quarter') ? 'selected' : ''; ?>>Quý</option>
                                        <option value="year" <?php echo (isset($data['formData']['price_period']) && $data['formData']['price_period'] == 'year') ? 'selected' : ''; ?>>Năm</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="area" class="form-label">Diện tích (m²) <span class="required">*</span></label>
                                    <input type="number" class="form-control" id="area" name="area" min="0" step="0.1" required value="<?php echo isset($data['formData']['area']) ? htmlspecialchars($data['formData']['area']) : ''; ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="direction" class="form-label">Hướng chính</label>
                                    <select class="form-select" id="direction" name="direction">
                                        <option value="">-- Chọn hướng --</option>
                                        <?php foreach ($data['directions'] as $direction): ?>
                                            <option value="<?php echo $direction->slug; ?>" <?php echo (isset($data['formData']['direction']) && $data['formData']['direction'] == $direction->slug) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($direction->name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="bedrooms" class="form-label">Số phòng ngủ</label>
                                    <select class="form-select" id="bedrooms" name="bedrooms">
                                        <option value="">-- Chọn số phòng ngủ --</option>
                                        <?php for ($i = 0; $i <= 10; $i++): ?>
                                            <option value="<?php echo $i; ?>" <?php echo (isset($data['formData']['bedrooms']) && $data['formData']['bedrooms'] == $i) ? 'selected' : ''; ?>>
                                                <?php echo $i; ?>
                                            </option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="bathrooms" class="form-label">Số phòng tắm</label>
                                    <select class="form-select" id="bathrooms" name="bathrooms">
                                        <option value="">-- Chọn số phòng tắm --</option>
                                        <?php for ($i = 0; $i <= 10; $i++): ?>
                                            <option value="<?php echo $i; ?>" <?php echo (isset($data['formData']['bathrooms']) && $data['formData']['bathrooms'] == $i) ? 'selected' : ''; ?>>
                                                <?php echo $i; ?>
                                            </option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="video_url" class="form-label">URL video YouTube</label>
                            <input type="url" class="form-control" id="video_url" name="video_url" placeholder="https://www.youtube.com/watch?v=..." value="<?php echo isset($data['formData']['video_url']) ? htmlspecialchars($data['formData']['video_url']) : ''; ?>">
                            <div class="form-text">Nhập URL video YouTube (nếu có)</div>
                        </div>

                        <div class="form-group">
                            <label for="title" class="form-label">Tiêu đề <span class="required">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required value="<?php echo isset($data['formData']['title']) ? htmlspecialchars($data['formData']['title']) : ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="description" class="form-label">Mô tả <span class="required">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="5" required><?php echo isset($data['formData']['description']) ? htmlspecialchars($data['formData']['description']) : ''; ?></textarea>
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <button type="submit" class="btn btn-primary">
                                Tiếp theo <i class="bi bi-arrow-right ms-1"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.getElementById('propertyForm');

    // Add validation styles on input/change
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(function(field) {
        field.addEventListener('blur', function() {
            validateField(field);
        });

        field.addEventListener('change', function() {
            validateField(field);
        });
    });

    // Form submission validation
    form.addEventListener('submit', function(event) {
        let isValid = true;

        requiredFields.forEach(function(field) {
            // Xử lý đặc biệt cho trường Select2
            if (field.id === 'ward_id' && typeof $ !== 'undefined') {
                // Sử dụng hàm validateField của Select2
                if (!window.validateSelect2Field(field)) {
                    isValid = false;
                }
            } else {
                // Sử dụng hàm validateField thông thường
                if (!validateField(field)) {
                    isValid = false;
                }
            }
        });

        if (!isValid) {
            event.preventDefault();

            // Scroll to the first invalid field
            const firstInvalidField = form.querySelector('.is-invalid');
            if (firstInvalidField) {
                firstInvalidField.focus();
                firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            // Show validation message
            const validationAlert = document.createElement('div');
            validationAlert.className = 'alert alert-danger alert-dismissible fade show mt-3';
            validationAlert.innerHTML = `
                <strong>Lỗi!</strong> Vui lòng điền đầy đủ thông tin bắt buộc.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            // Insert alert before the form
            form.parentNode.insertBefore(validationAlert, form);

            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                validationAlert.classList.remove('show');
                setTimeout(function() {
                    validationAlert.remove();
                }, 150);
            }, 5000);
        }
    });

    // Field validation function
    function validateField(field) {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');

            // Add validation feedback if not exists
            let feedback = field.nextElementSibling;
            if (!feedback || !feedback.classList.contains('invalid-feedback')) {
                feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = 'Trường này là bắt buộc';
                field.parentNode.insertBefore(feedback, field.nextElementSibling);
            }

            return false;
        } else {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');

            // Remove validation feedback if exists
            const feedback = field.nextElementSibling;
            if (feedback && feedback.classList.contains('invalid-feedback')) {
                feedback.remove();
            }

            return true;
        }
    }
});
</script>

<!-- Include jQuery and Select2 JS -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<!-- Initialize Select2 for ward_id -->
<script>
$(document).ready(function() {
    // Khởi tạo Select2 cho trường Phường/Xã
    $('#ward_id').select2({
        theme: 'bootstrap-5',
        width: '100%',
        placeholder: '-- Chọn phường/xã --',
        allowClear: true,
        language: {
            noResults: function() {
                return "Không tìm thấy kết quả";
            },
            searching: function() {
                return "Đang tìm kiếm...";
            }
        },
        // Tùy chỉnh dropdown để hiển thị tốt hơn trên thiết bị di động
        dropdownCssClass: 'select2-dropdown-ward',
        // Tùy chỉnh container để phù hợp với thiết kế form
        containerCssClass: 'select2-container-ward'
    });

    // Xử lý sự kiện khi Select2 thay đổi để kích hoạt validation
    $('#ward_id').on('change', function() {
        validateSelect2Field(this);
    });

    // Xử lý sự kiện khi Select2 mở để đảm bảo hiển thị đúng trên thiết bị di động
    $('#ward_id').on('select2:open', function() {
        // Đảm bảo input tìm kiếm nhận focus ngay lập tức
        setTimeout(function() {
            $('.select2-search__field').focus();
        }, 100);
    });

    // Hàm validation cho Select2 - Đặt vào window để có thể gọi từ bên ngoài
    window.validateSelect2Field = function(field) {
        if (!field.value) {
            $(field).next('.select2-container').addClass('is-invalid-select2');

            // Thêm thông báo lỗi nếu chưa có
            let feedback = $(field).nextAll('.invalid-feedback');
            if (feedback.length === 0) {
                feedback = $('<div class="invalid-feedback">Trường này là bắt buộc</div>');
                $(field).next('.select2-container').after(feedback);
            }

            return false;
        } else {
            $(field).next('.select2-container').removeClass('is-invalid-select2');
            $(field).next('.select2-container').addClass('is-valid-select2');

            // Xóa thông báo lỗi nếu có
            $(field).nextAll('.invalid-feedback').remove();

            return true;
        }
    }

    // Kiểm tra trạng thái ban đầu của trường Select2
    if ($('#ward_id').val()) {
        $('#ward_id').next('.select2-container').addClass('is-valid-select2');
    }
});
</script>
