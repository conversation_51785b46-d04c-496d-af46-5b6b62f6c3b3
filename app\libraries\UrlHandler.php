<?php

class UrlHandler {
    private $propertyTypeModel;
    private $wardModel;

    public function __construct() {
        require_once BASE_PATH . '/app/models/PropertyType.php';
        require_once BASE_PATH . '/app/models/Ward.php';
        $this->propertyTypeModel = new PropertyType();
        $this->wardModel = new Ward();
    }

    /**
     * Xử lý URL SEO-friendly và trả về các tham số đã phân tích
     * @param string $url URL cần xử lý
     * @return array Mảng chứa các tham số đã phân tích
     */
    public function parseUrl($url) {
        // Khởi tạo mảng params mặc định
        $params = [
            'type' => '',
            'ward' => '',
            'price' => '',
            'area' => '',
            'bedrooms' => '',
            'bathrooms' => '',
            'direction' => '',
            'matched' => false,
            'controller' => '',
            'action' => '',
            'additional_params' => []
        ];

        // // error_log('UrlHandler: Parsing URL: ' . $url);

        // Tách URL thành các phần
        $urlParts = explode('/', $url);

        // Xử lý phần đầu tiên (cho-thue-xxx hoặc cho-thue-xxx-tai-yyy)
        if (isset($urlParts[0])) {
            $basePart = $urlParts[0];

            // Trường hợp: cho-thue-{type}-tai-{ward}
            if (preg_match('/^cho-thue-([a-z0-9-]+)-tai-([a-z0-9-]+)$/', $basePart, $matches)) {
                $typeSlug = $matches[1];
                $wardSlug = $matches[2];

                // Kiểm tra nếu type là "nha-dat", đặt type thành rỗng
                $params['type'] = ($typeSlug === 'nha-dat') ? '' : $typeSlug;
                $params['ward'] = $wardSlug;
                $params['matched'] = true;
                $params['controller'] = 'SearchController';
                $params['action'] = 'filterByTypeAndWard';

                // // error_log('UrlHandler: Matched type-ward pattern: type=' . $params['type'] . ', ward=' . $params['ward']);
            }
            // Trường hợp: cho-thue-{type}
            else if (preg_match('/^cho-thue-([a-z0-9-]+)$/', $basePart, $matches)) {
                $typeSlug = $matches[1];

                // Kiểm tra nếu type là "nha-dat", đặt type thành rỗng
                $params['type'] = ($typeSlug === 'nha-dat') ? '' : $typeSlug;
                $params['matched'] = true;
                $params['controller'] = 'SearchController';
                $params['action'] = 'filterByTypeAndWard';

                // // error_log('UrlHandler: Matched type-only pattern: type=' . $params['type']);
            }
            // Trường hợp: cho-thue-nha-dat-tai-{ward}
            else if (preg_match('/^cho-thue-nha-dat-tai-([a-z0-9-]+)$/', $basePart, $matches)) {
                $params['type'] = ''; // Đặt type thành rỗng cho "nha-dat"
                $params['ward'] = $matches[1];
                $params['matched'] = true;
                $params['controller'] = 'SearchController';
                $params['action'] = 'filterByTypeAndWard';

                // error_log('UrlHandler: Matched nha-dat-ward pattern: ward=' . $params['ward']);
            }
        }

        // Xử lý các phần filter (nếu có)
        if (isset($urlParts[1]) && $params['matched']) {
            $filterPart = $urlParts[1];
            $this->parseFilters($filterPart, $params);
        }

        // Xử lý các tham số từ query string
        $this->parseQueryParams($params);

        // // error_log('UrlHandler: Final parsed params: ' . json_encode($params));

        return $params;
    }

    /**
     * Phân tích phần filter của URL
     * @param string $filterPart Phần filter cần phân tích
     * @param array &$params Mảng tham số để cập nhật
     */
    private function parseFilters($filterPart, &$params) {
        $filterSegments = explode('-', $filterPart);

        // error_log('UrlHandler: Parsing filter segments: ' . json_encode($filterSegments));

        // Xử lý từng segment
        for ($i = 0; $i < count($filterSegments); $i++) {
            $segment = $filterSegments[$i];

            // Xử lý giá: gia-tu-X-Y-trieu hoặc gia-tren-X-trieu hoặc gia-X-Y-trieu
            if ($segment == 'gia') {
                $priceResult = $this->parsePrice($filterSegments, $i);
                if ($priceResult['price']) {
                    $params['price'] = $priceResult['price'];
                    $i = $priceResult['next_index'];
                    // error_log('UrlHandler: Parsed price: ' . $params['price']);
                }
            }

            // Xử lý diện tích: dt-X-Y-m2 hoặc dt-tren-X-m2
            else if ($segment == 'dt') {
                $areaResult = $this->parseArea($filterSegments, $i);
                if ($areaResult['area']) {
                    $params['area'] = $areaResult['area'];
                    $i = $areaResult['next_index'];
                    // error_log('UrlHandler: Parsed area: ' . $params['area']);
                }
            }

            // Xử lý trường hợp đặc biệt: 4pn-tro-len (phải đặt trước logic Xpn đơn giản)
            else if ($segment == '4pn' && isset($filterSegments[$i+1]) && $filterSegments[$i+1] == 'tro' &&
                     isset($filterSegments[$i+2]) && $filterSegments[$i+2] == 'len') {
                $params['bedrooms'] = '4+';
                $i += 2;
                // error_log('UrlHandler: Parsed bedrooms 4+');
            }

            // Xử lý phòng ngủ: Xpn (ví dụ: 1pn, 2pn, 3pn)
            else if (preg_match('/^([0-9]+)pn$/', $segment, $bedroomMatches)) {
                $params['bedrooms'] = $bedroomMatches[1];
                // error_log('UrlHandler: Parsed bedrooms: ' . $params['bedrooms']);
            }

            // Xử lý phòng ngủ dạng dài: X-phong-ngu
            else if (preg_match('/^([0-9]+)$/', $segment) && isset($filterSegments[$i+1]) && $filterSegments[$i+1] == 'phong' &&
                     isset($filterSegments[$i+2]) && $filterSegments[$i+2] == 'ngu') {
                $params['bedrooms'] = $segment;
                $i += 2;
                // error_log('UrlHandler: Parsed bedrooms (long form): ' . $params['bedrooms']);
            }
        }
    }

    /**
     * Phân tích giá từ filter segments
     * @param array $segments Mảng segments
     * @param int $startIndex Index bắt đầu
     * @return array Kết quả phân tích
     */
    private function parsePrice($segments, $startIndex) {
        $result = ['price' => '', 'next_index' => $startIndex];

        // gia-tu-X-Y-trieu
        if (isset($segments[$startIndex+1]) && $segments[$startIndex+1] == 'tu' &&
            isset($segments[$startIndex+2]) && isset($segments[$startIndex+3]) &&
            isset($segments[$startIndex+4]) && $segments[$startIndex+4] == 'trieu') {
            $result['price'] = $segments[$startIndex+2] . '-' . $segments[$startIndex+3];
            $result['next_index'] = $startIndex + 4;
        }
        // gia-tren-X-trieu
        else if (isset($segments[$startIndex+1]) && $segments[$startIndex+1] == 'tren' &&
                 isset($segments[$startIndex+2]) && isset($segments[$startIndex+3]) &&
                 $segments[$startIndex+3] == 'trieu') {
            $result['price'] = $segments[$startIndex+2] . '+';
            $result['next_index'] = $startIndex + 3;
        }
        // gia-X-Y-trieu
        else if (isset($segments[$startIndex+1]) && isset($segments[$startIndex+2]) &&
                 isset($segments[$startIndex+3]) && $segments[$startIndex+3] == 'trieu') {
            $result['price'] = $segments[$startIndex+1] . '-' . $segments[$startIndex+2];
            $result['next_index'] = $startIndex + 3;
        }

        return $result;
    }

    /**
     * Phân tích diện tích từ filter segments
     * @param array $segments Mảng segments
     * @param int $startIndex Index bắt đầu
     * @return array Kết quả phân tích
     */
    private function parseArea($segments, $startIndex) {
        $result = ['area' => '', 'next_index' => $startIndex];

        // dt-tren-Xm2 (ví dụ: dt-tren-90m2)
        if (isset($segments[$startIndex+1]) && $segments[$startIndex+1] == 'tren' &&
            isset($segments[$startIndex+2]) &&
            preg_match('/^([0-9]+)m2$/', $segments[$startIndex+2], $matches)) {
            $result['area'] = $matches[1] . '+';
            $result['next_index'] = $startIndex + 2;
        }
        // dt-tren-X-m2 (ví dụ: dt-tren-90-m2)
        else if (isset($segments[$startIndex+1]) && $segments[$startIndex+1] == 'tren' &&
                 isset($segments[$startIndex+2]) && isset($segments[$startIndex+3]) &&
                 $segments[$startIndex+3] == 'm2') {
            $result['area'] = $segments[$startIndex+2] . '+';
            $result['next_index'] = $startIndex + 3;
        }
        // dt-X-Y-m2 (ví dụ: dt-50-70-m2)
        else if (isset($segments[$startIndex+1]) && isset($segments[$startIndex+2]) &&
                 isset($segments[$startIndex+3]) && $segments[$startIndex+3] == 'm2') {
            $result['area'] = $segments[$startIndex+1] . '-' . $segments[$startIndex+2];
            $result['next_index'] = $startIndex + 3;
        }
        // dt-X-Ym2 (ví dụ: dt-50-70m2 - không có dấu gạch ngang trước m2)
        else if (isset($segments[$startIndex+1]) && isset($segments[$startIndex+2]) &&
                 preg_match('/^([0-9]+)m2$/', $segments[$startIndex+2], $matches)) {
            $result['area'] = $segments[$startIndex+1] . '-' . $matches[1];
            $result['next_index'] = $startIndex + 2;
        }

        return $result;
    }

    /**
     * Xử lý các tham số từ query string
     * @param array &$params Mảng tham số để cập nhật
     */
    private function parseQueryParams(&$params) {
        // Lấy các tham số từ query string nếu chưa có trong URL path
        if (empty($params['bathrooms']) && isset($_GET['bathrooms'])) {
            $params['bathrooms'] = $_GET['bathrooms'];
        }

        if (empty($params['direction']) && isset($_GET['direction'])) {
            $params['direction'] = $_GET['direction'];
        }

        // Các tham số khác
        $additionalParams = ['keyword', 'sort'];
        foreach ($additionalParams as $param) {
            if (isset($_GET[$param])) {
                $params['additional_params'][$param] = $_GET[$param];
            }
        }
    }

    /**
     * Tạo URL SEO-friendly từ các tham số
     * @param array $params Mảng tham số
     * @return string URL được tạo
     */
    public function buildUrl($params) {
        $url = '/thuenhadanang';

        // Xây dựng phần cơ bản của URL
        $baseSegment = $this->buildBaseSegment($params);
        if ($baseSegment) {
            $url .= '/' . $baseSegment;
        }

        // Xây dựng phần filter
        $filterSegment = $this->buildFilterSegment($params);
        if ($filterSegment) {
            $url .= '/' . $filterSegment;
        }

        // Thêm query parameters nếu cần
        $queryParams = $this->buildQueryParams($params);
        if (!empty($queryParams)) {
            $url .= '?' . http_build_query($queryParams);
        }

        return $url;
    }

    /**
     * Xây dựng phần cơ bản của URL (cho-thue-xxx-tai-yyy)
     * @param array $params Mảng tham số
     * @return string Phần cơ bản của URL
     */
    private function buildBaseSegment($params) {
        $type = isset($params['type']) ? $params['type'] : '';
        $ward = isset($params['ward']) ? $params['ward'] : '';

        // Xử lý trường hợp type rỗng -> sử dụng "nha-dat"
        if (empty($type)) {
            $type = 'nha-dat';
        }

        if (!empty($ward)) {
            return 'cho-thue-' . $type . '-tai-' . $ward;
        } else if (!empty($type)) {
            return 'cho-thue-' . $type;
        }

        return '';
    }

    /**
     * Xây dựng phần filter của URL
     * @param array $params Mảng tham số
     * @return string Phần filter của URL
     */
    private function buildFilterSegment($params) {
        $filterParts = [];

        // Thêm price
        if (!empty($params['price'])) {
            $filterParts[] = 'gia-' . $this->formatPriceForUrl($params['price']);
        }

        // Thêm area
        if (!empty($params['area'])) {
            $filterParts[] = 'dt-' . $this->formatAreaForUrl($params['area']);
        }

        // Thêm bedrooms
        if (!empty($params['bedrooms'])) {
            if ($params['bedrooms'] == '4+') {
                $filterParts[] = '4pn-tro-len';
            } else {
                $filterParts[] = $params['bedrooms'] . 'pn';
            }
        }

        return implode('-', $filterParts);
    }

    /**
     * Xây dựng query parameters
     * @param array $params Mảng tham số
     * @return array Query parameters
     */
    private function buildQueryParams($params) {
        $queryParams = [];

        // Thêm bathrooms
        if (!empty($params['bathrooms'])) {
            $queryParams['bathrooms'] = $params['bathrooms'];
        }

        // Thêm direction
        if (!empty($params['direction'])) {
            $queryParams['direction'] = $params['direction'];
        }

        // Thêm các tham số bổ sung
        if (isset($params['additional_params'])) {
            $queryParams = array_merge($queryParams, $params['additional_params']);
        }

        return $queryParams;
    }

    /**
     * Format giá cho URL
     * @param string $price Giá cần format
     * @return string Giá đã format
     */
    private function formatPriceForUrl($price) {
        if (strpos($price, '+') !== false) {
            return 'tren-' . str_replace('+', '', $price) . '-trieu';
        } else if (strpos($price, '-') !== false) {
            return str_replace('-', '-', $price) . '-trieu';
        }
        return $price . '-trieu';
    }

    /**
     * Format diện tích cho URL
     * @param string $area Diện tích cần format
     * @return string Diện tích đã format
     */
    private function formatAreaForUrl($area) {
        if (strpos($area, '+') !== false) {
            return 'tren-' . str_replace('+', '', $area) . '-m2';
        } else if (strpos($area, '-') !== false) {
            // Để khớp với format gốc: dt-50-70m2 (không có dấu gạch ngang trước m2)
            return str_replace('-', '-', $area) . 'm2';
        }
        return $area . 'm2';
    }
}
