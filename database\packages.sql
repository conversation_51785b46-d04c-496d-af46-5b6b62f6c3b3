-- <PERSON><PERSON><PERSON> bảng cũ nếu tồn tại
DROP TABLE IF EXISTS `packages`;

-- Tạo bảng packages (gói dịch vụ)
CREATE TABLE `packages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT 'Tên gói dịch vụ',
  `description` text DEFAULT NULL COMMENT '<PERSON>ô tả gói dịch vụ',
  `post_limit` int(11) NOT NULL DEFAULT 0 COMMENT 'Số lượng tin đăng tối đa',
  `price` decimal(15,0) NOT NULL DEFAULT 0 COMMENT '<PERSON><PERSON>á gói (VNĐ)',
  `duration_days` int(11) NOT NULL DEFAULT 30 COMMENT 'Thời hạn gói (ngày)',
  `features` text DEFAULT NULL COMMENT 'Tính năng của gói (JSON)',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Gói mặc định cho user mới',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Trạng thái hoạt động',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Thêm dữ liệu mẫu cho 2 gói dịch vụ
INSERT INTO `packages` (`name`, `description`, `post_limit`, `price`, `duration_days`, `features`, `is_default`, `status`) VALUES
('Gói Cơ bản', 'Gói dành cho người dùng cá nhân với nhu cầu đăng tin cơ bản', 5, 0, 30, '["Đăng tối đa 5 tin", "Hiển thị thông tin liên hệ", "Hỗ trợ qua email"]', 1, 1),
('Gói Chuyên nghiệp', 'Gói dành cho môi giới và doanh nghiệp với nhiều tính năng nâng cao', 20, 500000, 30, '["Đăng tối đa 20 tin", "Tin được ưu tiên hiển thị", "Hiển thị thông tin liên hệ", "Hỗ trợ qua Zalo/Phone", "Thống kê chi tiết"]', 0, 1);
