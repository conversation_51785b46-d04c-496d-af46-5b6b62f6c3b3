<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Layout Changes - 4 Properties per Row</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/thuenhadanang/public/css/style.css" rel="stylesheet">
    <style>
        body {
            background: var(--modern-gray-50);
            padding: 2rem 0;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .test-header {
            background: white;
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }
        .breakpoint-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="breakpoint-info" id="breakpointInfo">
        Screen: <span id="screenWidth"></span>px
    </div>

    <div class="test-container">
        <div class="test-header">
            <h1>Test Layout: 1 dòng 4 bản tin bất động sản</h1>
            <p class="mb-2">
                <strong>Desktop (≥1200px):</strong> 4 cột |
                <strong>Tablet (1024px):</strong> 3 cột |
                <strong>Mobile (768px):</strong> 2 cột |
                <strong>Small Mobile (480px):</strong> 1 cột
            </p>
            <p class="mb-0 text-success">
                ✅ <strong>Property title:</strong> 1 dòng với ellipsis |
                ✅ <strong>Hover effects:</strong> Đã tắt tất cả
            </p>
        </div>

        <div class="properties-grid">
            <!-- Property Card 1 -->
            <div class="property-card">
                <div class="property-image-container">
                    <img src="https://via.placeholder.com/400x240/ff6b00/ffffff?text=Property+1" class="property-image" alt="Property 1">
                    <div class="property-badges">
                        <span class="property-badge featured-badge">Nổi bật</span>
                    </div>
                    <button class="wishlist-btn">
                        <i class="bi bi-heart"></i>
                    </button>
                </div>
                <div class="property-content">
                    <div class="property-header">
                        <h3 class="property-title">
                            <a href="#">Căn hộ cao cấp view biển Đà Nẵng với nội thất sang trọng và tiện nghi hiện đại</a>
                        </h3>
                        <div class="property-location">
                            <i class="bi bi-geo-alt"></i>
                            <span>Hải Châu</span>
                        </div>
                    </div>
                    <div class="property-features">
                        <span class="feature-item">
                            <i class="bi bi-rulers"></i>
                            80m²
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-door-closed"></i>
                            2 PN
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-droplet"></i>
                            2 WC
                        </span>
                    </div>
                    <div class="property-footer">
                        <div class="property-price">
                            <span class="current-price">15 triệu/tháng</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Property Card 2 -->
            <div class="property-card">
                <div class="property-image-container">
                    <img src="https://via.placeholder.com/400x240/2563eb/ffffff?text=Property+2" class="property-image" alt="Property 2">
                    <button class="wishlist-btn">
                        <i class="bi bi-heart"></i>
                    </button>
                </div>
                <div class="property-content">
                    <div class="property-header">
                        <h3 class="property-title">
                            <a href="#">Nhà riêng 3 tầng gần biển</a>
                        </h3>
                        <div class="property-location">
                            <i class="bi bi-geo-alt"></i>
                            <span>Sơn Trà</span>
                        </div>
                    </div>
                    <div class="property-features">
                        <span class="feature-item">
                            <i class="bi bi-rulers"></i>
                            120m²
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-door-closed"></i>
                            3 PN
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-droplet"></i>
                            3 WC
                        </span>
                    </div>
                    <div class="property-footer">
                        <div class="property-price">
                            <span class="current-price">25 triệu/tháng</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Property Card 3 -->
            <div class="property-card">
                <div class="property-image-container">
                    <img src="https://via.placeholder.com/400x240/10b981/ffffff?text=Property+3" class="property-image" alt="Property 3">
                    <button class="wishlist-btn">
                        <i class="bi bi-heart"></i>
                    </button>
                </div>
                <div class="property-content">
                    <div class="property-header">
                        <h3 class="property-title">
                            <a href="#">Phòng trọ cao cấp full nội thất</a>
                        </h3>
                        <div class="property-location">
                            <i class="bi bi-geo-alt"></i>
                            <span>Thanh Khê</span>
                        </div>
                    </div>
                    <div class="property-features">
                        <span class="feature-item">
                            <i class="bi bi-rulers"></i>
                            35m²
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-door-closed"></i>
                            1 PN
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-droplet"></i>
                            1 WC
                        </span>
                    </div>
                    <div class="property-footer">
                        <div class="property-price">
                            <span class="current-price">8 triệu/tháng</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Property Card 4 -->
            <div class="property-card">
                <div class="property-image-container">
                    <img src="https://via.placeholder.com/400x240/f59e0b/ffffff?text=Property+4" class="property-image" alt="Property 4">
                    <div class="property-badges">
                        <span class="property-badge featured-badge">Nổi bật</span>
                    </div>
                    <button class="wishlist-btn">
                        <i class="bi bi-heart"></i>
                    </button>
                </div>
                <div class="property-content">
                    <div class="property-header">
                        <h3 class="property-title">
                            <a href="#">Villa sang trọng view sông Hàn</a>
                        </h3>
                        <div class="property-location">
                            <i class="bi bi-geo-alt"></i>
                            <span>Ngũ Hành Sơn</span>
                        </div>
                    </div>
                    <div class="property-features">
                        <span class="feature-item">
                            <i class="bi bi-rulers"></i>
                            200m²
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-door-closed"></i>
                            4 PN
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-droplet"></i>
                            4 WC
                        </span>
                    </div>
                    <div class="property-footer">
                        <div class="property-price">
                            <span class="current-price">50 triệu/tháng</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Property Card 5 -->
            <div class="property-card">
                <div class="property-image-container">
                    <img src="https://via.placeholder.com/400x240/ef4444/ffffff?text=Property+5" class="property-image" alt="Property 5">
                    <button class="wishlist-btn">
                        <i class="bi bi-heart"></i>
                    </button>
                </div>
                <div class="property-content">
                    <div class="property-header">
                        <h3 class="property-title">
                            <a href="#">Căn hộ studio hiện đại</a>
                        </h3>
                        <div class="property-location">
                            <i class="bi bi-geo-alt"></i>
                            <span>Cẩm Lệ</span>
                        </div>
                    </div>
                    <div class="property-features">
                        <span class="feature-item">
                            <i class="bi bi-rulers"></i>
                            45m²
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-door-closed"></i>
                            1 PN
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-droplet"></i>
                            1 WC
                        </span>
                    </div>
                    <div class="property-footer">
                        <div class="property-price">
                            <span class="current-price">12 triệu/tháng</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Property Card 6 -->
            <div class="property-card">
                <div class="property-image-container">
                    <img src="https://via.placeholder.com/400x240/8b5cf6/ffffff?text=Property+6" class="property-image" alt="Property 6">
                    <button class="wishlist-btn">
                        <i class="bi bi-heart"></i>
                    </button>
                </div>
                <div class="property-content">
                    <div class="property-header">
                        <h3 class="property-title">
                            <a href="#">Nhà mặt tiền kinh doanh</a>
                        </h3>
                        <div class="property-location">
                            <i class="bi bi-geo-alt"></i>
                            <span>Liên Chiểu</span>
                        </div>
                    </div>
                    <div class="property-features">
                        <span class="feature-item">
                            <i class="bi bi-rulers"></i>
                            150m²
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-door-closed"></i>
                            4 PN
                        </span>
                        <span class="feature-item">
                            <i class="bi bi-droplet"></i>
                            3 WC
                        </span>
                    </div>
                    <div class="property-footer">
                        <div class="property-price">
                            <span class="current-price">35 triệu/tháng</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Display current screen width
        function updateScreenInfo() {
            const width = window.innerWidth;
            document.getElementById('screenWidth').textContent = width;

            let breakpoint = '';
            if (width >= 1200) breakpoint = 'Desktop (4 cols)';
            else if (width >= 1024) breakpoint = 'Large Tablet (3 cols)';
            else if (width >= 768) breakpoint = 'Tablet (2 cols)';
            else if (width >= 480) breakpoint = 'Mobile (2 cols)';
            else breakpoint = 'Small Mobile (1 col)';

            document.getElementById('breakpointInfo').innerHTML =
                `Screen: ${width}px<br>${breakpoint}`;
        }

        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
    </script>
</body>
</html>
