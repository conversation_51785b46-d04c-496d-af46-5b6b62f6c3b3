<?php
/**
 * Test Hybrid Search Implementation
 * <PERSON>ểm tra việc triển khai phương án Hybrid cho trang tìm kiếm
 */

// Prevent direct access
if (!defined('TESTING_MODE')) {
    define('TESTING_MODE', true);
}

// Include necessary files
require_once __DIR__ . '/config/config.php';

class HybridSearchTester {
    private $results = [];
    private $errors = [];

    public function __construct() {
        $this->log("🔬 Hybrid Search Implementation Tester initialized");
    }

    /**
     * Run all tests
     */
    public function runAllTests() {
        $this->log("🚀 Starting Hybrid Search Implementation Tests...");

        // Test 1: Check AjaxSearch class modifications
        $this->testAjaxSearchModifications();

        // Test 2: Check template modifications
        $this->testTemplateModifications();

        // Test 3: Check file structure
        $this->testFileStructure();

        // Test 4: Check JavaScript functionality
        $this->testJavaScriptFunctionality();

        // Test 5: Check mobile compatibility
        $this->testMobileCompatibility();

        $this->generateReport();
    }

    /**
     * Test AjaxSearch class modifications
     */
    private function testAjaxSearchModifications() {
        $this->log("📝 Testing AjaxSearch class modifications...");

        $ajaxSearchFile = __DIR__ . '/public/js/ajax-search.js';

        if (!file_exists($ajaxSearchFile)) {
            $this->addError("AjaxSearch file not found: $ajaxSearchFile");
            return;
        }

        $content = file_get_contents($ajaxSearchFile);

        // Check for hybrid properties
        $hybridProperties = [
            'isInitialPageLoad',
            'hasServerData',
            'initialUrl'
        ];

        foreach ($hybridProperties as $property) {
            if (strpos($content, $property) !== false) {
                $this->addResult("✅ Found hybrid property: $property");
            } else {
                $this->addError("❌ Missing hybrid property: $property");
            }
        }

        // Check for hybrid methods
        $hybridMethods = [
            'checkServerData',
            'checkReferrerSource',
            'initializeHybridPageLoad'
        ];

        foreach ($hybridMethods as $method) {
            if (strpos($content, $method) !== false) {
                $this->addResult("✅ Found hybrid method: $method");
            } else {
                $this->addError("❌ Missing hybrid method: $method");
            }
        }

        // Check for hybrid logic in showLoading
        if (strpos($content, 'isInitialPageLoad && this.hasServerData') !== false) {
            $this->addResult("✅ Found hybrid logic in showLoading method");
        } else {
            $this->addError("❌ Missing hybrid logic in showLoading method");
        }

        $this->log("📝 AjaxSearch modifications test completed");
    }

    /**
     * Test template modifications
     */
    private function testTemplateModifications() {
        $this->log("🎨 Testing template modifications...");

        $searchTemplate = __DIR__ . '/app/views/search.php';

        if (!file_exists($searchTemplate)) {
            $this->addError("Search template not found: $searchTemplate");
            return;
        }

        $content = file_get_contents($searchTemplate);

        // Check for data-server-rendered attribute
        if (strpos($content, 'data-server-rendered="true"') !== false) {
            $this->addResult("✅ Found data-server-rendered attribute in template");
        } else {
            $this->addError("❌ Missing data-server-rendered attribute in template");
        }

        // Check for main-content-section class
        if (strpos($content, 'main-content-section') !== false) {
            $this->addResult("✅ Found main-content-section class");
        } else {
            $this->addError("❌ Missing main-content-section class");
        }

        $this->log("🎨 Template modifications test completed");
    }

    /**
     * Test file structure
     */
    private function testFileStructure() {
        $this->log("📁 Testing file structure...");

        $requiredFiles = [
            'public/js/ajax-search.js' => 'AJAX Search main file',
            'app/views/search.php' => 'Search template',
            'test-hybrid-search.html' => 'Hybrid test page',
            'HYBRID_SEARCH_GUIDE.md' => 'Hybrid documentation'
        ];

        foreach ($requiredFiles as $file => $description) {
            $fullPath = __DIR__ . '/' . $file;
            if (file_exists($fullPath)) {
                $this->addResult("✅ Found $description: $file");
            } else {
                $this->addError("❌ Missing $description: $file");
            }
        }

        $this->log("📁 File structure test completed");
    }

    /**
     * Test JavaScript functionality
     */
    private function testJavaScriptFunctionality() {
        $this->log("⚙️ Testing JavaScript functionality...");

        $ajaxSearchFile = __DIR__ . '/public/js/ajax-search.js';

        if (!file_exists($ajaxSearchFile)) {
            $this->addError("Cannot test JavaScript - file not found");
            return;
        }

        $content = file_get_contents($ajaxSearchFile);

        // Check for console.log statements with hybrid messages
        $hybridLogs = [
            'Hybrid: Server-rendered data detected',
            'Hybrid: Using server-rendered data',
            'Hybrid: User came from homepage',
            'Hybrid: Skipping skeleton'
        ];

        foreach ($hybridLogs as $log) {
            if (strpos($content, $log) !== false) {
                $this->addResult("✅ Found hybrid log: $log");
            } else {
                $this->addError("❌ Missing hybrid log: $log");
            }
        }

        // Check for proper error handling
        if (strpos($content, 'try {') !== false && strpos($content, 'catch') !== false) {
            $this->addResult("✅ Found error handling in JavaScript");
        } else {
            $this->addError("❌ Missing error handling in JavaScript");
        }

        $this->log("⚙️ JavaScript functionality test completed");
    }

    /**
     * Test mobile compatibility
     */
    private function testMobileCompatibility() {
        $this->log("📱 Testing mobile compatibility...");

        $mobileSearchFile = __DIR__ . '/public/js/mobile-search.js';

        if (!file_exists($mobileSearchFile)) {
            $this->addError("Mobile search file not found");
            return;
        }

        $content = file_get_contents($mobileSearchFile);

        // Check if mobile search uses desktop AJAX
        if (strpos($content, 'window.ajaxSearch') !== false) {
            $this->addResult("✅ Mobile search integrates with desktop AJAX");
        } else {
            $this->addError("❌ Mobile search doesn't integrate with desktop AJAX");
        }

        // Check for mobile-specific hybrid handling
        if (strpos($content, 'updateResults') !== false) {
            $this->addResult("✅ Mobile search can use desktop updateResults");
        } else {
            $this->addError("❌ Mobile search missing updateResults integration");
        }

        $this->log("📱 Mobile compatibility test completed");
    }

    /**
     * Add test result
     */
    private function addResult($message) {
        $this->results[] = $message;
        $this->log($message);
    }

    /**
     * Add error
     */
    private function addError($message) {
        $this->errors[] = $message;
        $this->log($message, 'ERROR');
    }

    /**
     * Log message
     */
    private function log($message, $level = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        echo "[$timestamp] [$level] $message\n";
    }

    /**
     * Generate test report
     */
    private function generateReport() {
        $this->log("📊 Generating test report...");

        $totalTests = count($this->results) + count($this->errors);
        $passedTests = count($this->results);
        $failedTests = count($this->errors);
        $successRate = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 2) : 0;

        echo "\n" . str_repeat("=", 60) . "\n";
        echo "🔬 HYBRID SEARCH IMPLEMENTATION TEST REPORT\n";
        echo str_repeat("=", 60) . "\n";

        echo "📈 SUMMARY:\n";
        echo "  Total Tests: $totalTests\n";
        echo "  Passed: $passedTests\n";
        echo "  Failed: $failedTests\n";
        echo "  Success Rate: $successRate%\n\n";

        if (!empty($this->results)) {
            echo "✅ PASSED TESTS:\n";
            foreach ($this->results as $result) {
                echo "  $result\n";
            }
            echo "\n";
        }

        if (!empty($this->errors)) {
            echo "❌ FAILED TESTS:\n";
            foreach ($this->errors as $error) {
                echo "  $error\n";
            }
            echo "\n";
        }

        echo "🎯 RECOMMENDATIONS:\n";
        if ($failedTests == 0) {
            echo "  🎉 All tests passed! Hybrid implementation is ready.\n";
            echo "  📝 Next steps:\n";
            echo "    1. Test on live environment\n";
            echo "    2. Monitor performance metrics\n";
            echo "    3. Gather user feedback\n";
        } else {
            echo "  🔧 Fix the failed tests before deployment\n";
            echo "  📋 Review the implementation guide: HYBRID_SEARCH_GUIDE.md\n";
            echo "  🧪 Run browser tests: test-hybrid-search.html\n";
        }

        echo "\n" . str_repeat("=", 60) . "\n";

        // Save report to file (simplified to avoid memory issues)
        $reportFile = __DIR__ . '/hybrid_test_report_' . date('Y-m-d_H-i-s') . '.txt';
        $reportContent = "Hybrid Search Implementation Test Report\n";
        $reportContent .= "Total Tests: " . ($totalTests) . "\n";
        $reportContent .= "Passed: " . $passedTests . "\n";
        $reportContent .= "Failed: " . $failedTests . "\n";
        $reportContent .= "Success Rate: " . $successRate . "%\n";
        file_put_contents($reportFile, $reportContent);

        $this->log("📄 Report saved to: $reportFile");
    }
}

// Run tests if called directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    echo "🔬 Hybrid Search Implementation Tester\n";
    echo "=====================================\n\n";

    $tester = new HybridSearchTester();
    $tester->runAllTests();

    echo "\n✨ Testing completed!\n";
    echo "📖 For detailed documentation, see: HYBRID_SEARCH_GUIDE.md\n";
    echo "🧪 For browser testing, open: test-hybrid-search.html\n";
}
?>
