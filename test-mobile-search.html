<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mobile Search - Thu<PERSON> Nẵng</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Mobile Search CSS -->
    <link rel="stylesheet" href="/thuenhadanang/public/css/mobile-search.css">

    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .test-container {
            max-width: 500px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .test-section {
            margin-bottom: 30px;
        }

        .test-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .test-btn {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background: white;
            color: #495057;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .test-btn:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }

        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .status-success {
            color: #28a745;
        }

        .status-error {
            color: #dc3545;
        }

        .status-info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h2>🔍 Mobile Search Test</h2>
            <p class="text-muted">Test mobile search functionality</p>
        </div>

        <!-- Mobile Search Interface -->
        <div class="test-section">
            <div class="test-title">Mobile Search Interface</div>

            <!-- Mobile Search Section -->
            <section class="mobile-search-section bg-white py-2">
                <div class="container">
                    <!-- Mobile Search Form -->
                    <form class="mobile-search-form" action="/thuenhadanang/search" method="get" id="mobileSearchForm">
                        <div class="mobile-search-container">
                            <!-- Single Row Layout: Keyword + Filter Button + Search Button -->
                            <div class="mobile-search-row">
                                <!-- 1. Keyword Field -->
                                <div class="mobile-search-field keyword-field">
                                    <input type="text" class="form-control mobile-search-input" name="keyword"
                                           placeholder="Tìm kiếm địa điểm, tên đường..."
                                           value="" id="mobileSearchKeyword">
                                </div>

                                <!-- 2. Filter Button -->
                                <button type="button" class="mobile-filter-btn" id="mobileFilterBtn">
                                    <i class="bi bi-sliders"></i>
                                    <span class="filter-text">Lọc</span>
                                    <span class="badge bg-primary ms-1" id="mobileFiltersBadge" style="display: none;">0</span>
                                </button>

                                <!-- 3. Search Button -->
                                <button type="submit" class="mobile-search-btn" id="mobileSearchBtn">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>

                            <!-- Hidden Fields for Mobile Form Submission -->
                            <input type="hidden" name="type" id="mobileSearchType" value="">
                            <input type="hidden" name="ward" id="mobileSearchWard" value="">
                            <input type="hidden" name="price" id="mobileSearchPrice" value="">
                            <input type="hidden" name="bedrooms" id="mobileSearchBedrooms" value="">
                            <input type="hidden" name="bathrooms" id="mobileSearchBathrooms" value="">
                            <input type="hidden" name="area" id="mobileSearchArea" value="">
                            <input type="hidden" name="direction" id="mobileSearchDirection" value="">
                        </div>
                    </form>
                </div>
            </section>
        </div>

        <!-- Test Controls -->
        <div class="test-section">
            <div class="test-title">Test Controls</div>
            <div class="test-info">
                <strong>Instructions:</strong><br>
                1. Enter a keyword and click search<br>
                2. Click filter button to open modal<br>
                3. Test API endpoints manually<br>
                4. Check console for logs
            </div>

            <div class="test-buttons">
                <button class="test-btn" onclick="testMobileAPI()">Test Mobile API</button>
                <button class="test-btn" onclick="testFilterModal()">Test Filter Modal</button>
                <button class="test-btn" onclick="testSuggestions()">Test Suggestions</button>
                <button class="test-btn" onclick="clearResults()">Clear Results</button>
            </div>

            <div id="testResults" class="test-results" style="display: none;"></div>
        </div>
    </div>

    <!-- Mobile Filters Modal -->
    <div class="modal fade" id="mobileFiltersModal" tabindex="-1" aria-labelledby="mobileFiltersModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mobileFiltersModalLabel">
                        <i class="bi bi-sliders me-2"></i>Bộ lọc tìm kiếm
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="mobileFiltersForm">
                        <div class="row g-3">
                            <!-- Property Type -->
                            <div class="col-12">
                                <label for="mobileFilterType" class="form-label">
                                    <i class="bi bi-house me-1"></i>Loại hình bất động sản
                                </label>
                                <select class="form-select" id="mobileFilterType" name="type">
                                    <option value="">Tất cả loại hình</option>
                                    <option value="can-ho">Căn hộ</option>
                                    <option value="nha-rieng">Nhà riêng</option>
                                    <option value="phong-tro">Phòng trọ</option>
                                </select>
                            </div>

                            <!-- Ward/Commune -->
                            <div class="col-12">
                                <label for="mobileFilterWard" class="form-label">
                                    <i class="bi bi-geo-alt me-1"></i>Phường/Xã
                                </label>
                                <select class="form-select mobile-ward-select" id="mobileFilterWard" name="ward">
                                    <option value="">Tất cả phường/xã</option>
                                    <option value="an-hai-bac">An Hải Bắc</option>
                                    <option value="an-hai-dong">An Hải Đông</option>
                                    <option value="my-an">Mỹ An</option>
                                </select>
                            </div>

                            <!-- Price Range -->
                            <div class="col-12">
                                <label for="mobileFilterPrice" class="form-label">
                                    <i class="bi bi-currency-dollar me-1"></i>Mức giá
                                </label>
                                <select class="form-select" id="mobileFilterPrice" name="price">
                                    <option value="">Tất cả mức giá</option>
                                    <option value="1-3">1-3 triệu</option>
                                    <option value="3-5">3-5 triệu</option>
                                    <option value="5-7">5-7 triệu</option>
                                    <option value="7-10">7-10 triệu</option>
                                    <option value="10-15">10-15 triệu</option>
                                    <option value="15+">Trên 15 triệu</option>
                                </select>
                            </div>

                            <!-- Advanced Filters Row -->
                            <div class="col-6">
                                <label for="mobileFilterBedrooms" class="form-label">
                                    <i class="bi bi-door-closed me-1"></i>Phòng ngủ
                                </label>
                                <select class="form-select" id="mobileFilterBedrooms" name="bedrooms">
                                    <option value="">Tất cả</option>
                                    <option value="1">1 phòng</option>
                                    <option value="2">2 phòng</option>
                                    <option value="3">3 phòng</option>
                                    <option value="4+">4+ phòng</option>
                                </select>
                            </div>

                            <div class="col-6">
                                <label for="mobileFilterBathrooms" class="form-label">
                                    <i class="bi bi-droplet me-1"></i>Phòng tắm
                                </label>
                                <select class="form-select" id="mobileFilterBathrooms" name="bathrooms">
                                    <option value="">Tất cả</option>
                                    <option value="1">1 phòng</option>
                                    <option value="2">2 phòng</option>
                                    <option value="3">3 phòng</option>
                                    <option value="4+">4+ phòng</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" id="mobileFiltersClear">
                        <i class="bi bi-arrow-clockwise me-1"></i>Xóa bộ lọc
                    </button>
                    <button type="button" class="btn btn-primary" id="mobileFiltersApply">
                        <i class="bi bi-check-lg me-1"></i>Áp dụng
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Mobile Search JS -->
    <script src="/thuenhadanang/public/js/mobile-search.js"></script>

    <script>
        // Test functions
        function logResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            results.style.display = 'block';

            const timestamp = new Date().toLocaleTimeString();
            const className = `status-${type}`;
            results.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }

        async function testMobileAPI() {
            logResult('Testing Mobile APIs...', 'info');

            const apis = [
                { name: 'Simple API', url: '/thuenhadanang/api/mobile-search-simple.php?keyword=test&_t=' + Date.now() },
                { name: 'Debug API', url: '/thuenhadanang/api/mobile-search-debug.php?keyword=test&_t=' + Date.now() },
                { name: 'Main API', url: '/thuenhadanang/api/mobile-search.php?keyword=test&_t=' + Date.now() }
            ];

            for (const api of apis) {
                try {
                    logResult(`Testing ${api.name}...`, 'info');
                    const response = await fetch(api.url);

                    if (!response.ok) {
                        logResult(`❌ ${api.name} HTTP Error: ${response.status}`, 'error');
                        continue;
                    }

                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        const text = await response.text();
                        logResult(`❌ ${api.name} Non-JSON: ${text.substring(0, 100)}`, 'error');
                        continue;
                    }

                    const data = await response.json();

                    if (data.success) {
                        const count = data.data ? data.data.count : 0;
                        logResult(`✅ ${api.name} Success: Found ${count} properties`, 'success');
                    } else {
                        logResult(`❌ ${api.name} Error: ${data.error.message}`, 'error');
                    }
                } catch (error) {
                    logResult(`❌ ${api.name} Network Error: ${error.message}`, 'error');
                }
            }
        }

        function testFilterModal() {
            logResult('Opening filter modal...', 'info');

            if (window.mobileSearch) {
                window.mobileSearch.openFiltersModal();
                logResult('✅ Filter modal opened', 'success');
            } else {
                logResult('❌ Mobile search not initialized', 'error');
            }
        }

        async function testSuggestions() {
            logResult('Testing suggestions API...', 'info');

            const apis = [
                { name: 'Simple API Suggestions', url: '/thuenhadanang/api/mobile-search-simple.php?action=suggestions&q=an&_t=' + Date.now() },
                { name: 'Main API Suggestions', url: '/thuenhadanang/api/mobile-search.php?action=suggestions&q=an&_t=' + Date.now() }
            ];

            for (const api of apis) {
                try {
                    logResult(`Testing ${api.name}...`, 'info');
                    const response = await fetch(api.url);

                    if (!response.ok) {
                        logResult(`❌ ${api.name} HTTP Error: ${response.status}`, 'error');
                        continue;
                    }

                    const data = await response.json();

                    if (data.success) {
                        logResult(`✅ ${api.name}: Found ${data.suggestions.length} items`, 'success');
                    } else {
                        logResult(`❌ ${api.name} Error: ${data.error.message}`, 'error');
                    }
                } catch (error) {
                    logResult(`❌ ${api.name} Network Error: ${error.message}`, 'error');
                }
            }
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('testResults').style.display = 'none';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logResult('🚀 Mobile Search Test Page Loaded', 'success');

            // Check if mobile search is available
            setTimeout(() => {
                if (window.mobileSearch) {
                    logResult('✅ Mobile Search System Initialized', 'success');
                } else {
                    logResult('❌ Mobile Search System Not Found', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
