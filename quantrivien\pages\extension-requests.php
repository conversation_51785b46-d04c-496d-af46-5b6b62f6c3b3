<?php
// <PERSON><PERSON><PERSON> tra quyền admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Include model
require_once __DIR__ . '/../../app/models/ExtensionRequest.php';
$extensionRequestModel = new ExtensionRequest();

// Lấy filter status từ query string
$status = $_GET['status'] ?? null;
if ($status && !in_array($status, ['pending', 'approved', 'rejected'])) {
    $status = null;
}

// Lấy danh sách yêu cầu gia hạn
$extensionRequests = $extensionRequestModel->getAllRequests($status);

// L<PERSON>y thống kê (chỉ cần khi không phải AJAX)
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] !== 'XMLHttpRequest') {
    $stats = $extensionRequestModel->getStats();
}

// AJAX requests được xử lý bởi endpoint riêng trong ajax/extension-requests-table.php
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Quản lý yêu cầu gia hạn</h1>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Chờ duyệt</h5>
                        <h3 class="mb-0"><?php echo $stats->pending; ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock-history fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Đã duyệt</h5>
                        <h3 class="mb-0"><?php echo $stats->approved; ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-bg-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Đã từ chối</h5>
                        <h3 class="mb-0"><?php echo $stats->rejected; ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-x-circle fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Tổng cộng</h5>
                        <h3 class="mb-0"><?php echo $stats->total; ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-list-ul fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter Tabs -->
<ul class="nav nav-tabs mb-3" id="statusTabs">
    <li class="nav-item">
        <a class="nav-link <?php echo !$status ? 'active' : ''; ?>" 
           href="#" data-status="">
            Tất cả (<?php echo $stats->total; ?>)
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link <?php echo $status === 'pending' ? 'active' : ''; ?>" 
           href="#" data-status="pending">
            Chờ duyệt (<?php echo $stats->pending; ?>)
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link <?php echo $status === 'approved' ? 'active' : ''; ?>" 
           href="#" data-status="approved">
            Đã duyệt (<?php echo $stats->approved; ?>)
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link <?php echo $status === 'rejected' ? 'active' : ''; ?>" 
           href="#" data-status="rejected">
            Đã từ chối (<?php echo $stats->rejected; ?>)
        </a>
    </li>
</ul>

<!-- Table Container -->
<div id="tableContainer">
    <?php include __DIR__ . '/extension-requests-table.php'; ?>
</div>

<!-- Modal for Admin Note -->
<div class="modal fade" id="adminNoteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Ghi chú</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="adminNoteForm">
                    <div class="mb-3">
                        <label for="adminNote" class="form-label">Ghi chú của admin:</label>
                        <textarea class="form-control" id="adminNote" name="admin_note" rows="3" 
                                  placeholder="Nhập ghi chú (tùy chọn)"></textarea>
                    </div>
                    <input type="hidden" id="requestId" name="request_id">
                    <input type="hidden" id="actionType" name="action_type">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn" id="confirmActionBtn">Xác nhận</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle tab switching
    const tabLinks = document.querySelectorAll('#statusTabs .nav-link');
    const tableContainer = document.getElementById('tableContainer');

    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active tab
            tabLinks.forEach(tab => tab.classList.remove('active'));
            this.classList.add('active');
            
            // Get status
            const status = this.getAttribute('data-status');
            
            // Load data
            loadData(status);
        });
    });

    function loadData(status) {
        const timestamp = new Date().getTime();
        const url = status ?
            `ajax/extension-requests-table.php?status=${status}&_t=${timestamp}` :
            `ajax/extension-requests-table.php?_t=${timestamp}`;

        fetch(url, {
            cache: 'no-cache'
        })
        .then(response => response.text())
        .then(html => {
            tableContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Error:', error);
            tableContainer.innerHTML = '<div class="alert alert-danger">Có lỗi xảy ra khi tải dữ liệu</div>';
        });
    }

    // Handle approve/reject actions
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('approve-btn') || e.target.classList.contains('reject-btn')) {
            e.preventDefault();
            
            const button = e.target;
            const requestId = button.getAttribute('data-request-id');
            const action = button.classList.contains('approve-btn') ? 'approve' : 'reject';
            
            // Set modal data
            document.getElementById('requestId').value = requestId;
            document.getElementById('actionType').value = action;
            document.getElementById('modalTitle').textContent = action === 'approve' ? 'Phê duyệt yêu cầu' : 'Từ chối yêu cầu';
            
            const confirmBtn = document.getElementById('confirmActionBtn');
            confirmBtn.className = `btn btn-${action === 'approve' ? 'success' : 'danger'}`;
            confirmBtn.textContent = action === 'approve' ? 'Phê duyệt' : 'Từ chối';
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('adminNoteModal'));
            modal.show();
        }
    });

    // Handle confirm action
    document.getElementById('confirmActionBtn').addEventListener('click', function() {
        const requestId = document.getElementById('requestId').value;
        const actionType = document.getElementById('actionType').value;
        const adminNote = document.getElementById('adminNote').value;
        
        const url = `/thuenhadanang/quantrivien/extension-requests/${actionType}/${requestId}`;
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `admin_note=${encodeURIComponent(adminNote)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                bootstrap.Modal.getInstance(document.getElementById('adminNoteModal')).hide();
                
                // Reload current tab
                const activeTab = document.querySelector('#statusTabs .nav-link.active');
                const status = activeTab.getAttribute('data-status');
                loadData(status);
                
                // Show success message
                showToast('success', data.message);
            } else {
                showToast('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'Có lỗi xảy ra khi xử lý yêu cầu');
        });
    });

    function showToast(type, message) {
        // Simple toast implementation
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
});
</script>
