<?php
// File xử lý AJAX upload hình ảnh
session_start();

// Kiểm tra quyền truy cập
if (!isset($_SESSION['admin_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// Đ<PERSON>nh nghĩa các thư mục
$tempUploadDir = __DIR__ . '/../public/uploads/temp/';
$uploadDir = __DIR__ . '/../public/uploads/properties/';

// Tạo thư mục nếu chưa tồn tại
if (!file_exists($tempUploadDir)) {
    if (!mkdir($tempUploadDir, 0777, true)) {
        error_log('Failed to create temp directory: ' . $tempUploadDir);
    } else {
        // Đảm bảo quyền ghi
        chmod($tempUploadDir, 0777);
    }
}

if (!file_exists($uploadDir)) {
    if (!mkdir($uploadDir, 0777, true)) {
        error_log('Failed to create upload directory: ' . $uploadDir);
    } else {
        // Đ<PERSON><PERSON> bảo quyền ghi
        chmod($uploadDir, 0777);
    }
}

// Xử lý action
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Xử lý upload file tạm thời
if ($action == 'upload_temp') {
    // Đặt header JSON
    header('Content-Type: application/json');

    try {
        // Kiểm tra file upload
        if (!isset($_FILES['file']) || $_FILES['file']['error'] != 0) {
            $error = isset($_FILES['file']) ? 'Error code: ' . $_FILES['file']['error'] : 'No file uploaded';
            echo json_encode(['success' => false, 'error' => 'No file uploaded or upload error: ' . $error]);
            exit;
        }

        $file = $_FILES['file'];
        $fileName = $file['name'];
        $fileTmpName = $file['tmp_name'];
        $fileSize = $file['size'];
        $fileType = $file['type'];

        // Kiểm tra loại file - chấp nhận cả file đã nén từ Compressor.js
        if (!in_array($fileType, ['image/jpeg', 'image/jpg', 'image/png'])) {
            echo json_encode(['success' => false, 'error' => 'Invalid file type: ' . $fileType]);
            exit;
        }

        // Kiểm tra kích thước file (2MB = 2097152 bytes) - tăng giới hạn cho file đã nén
        if ($fileSize > 2097152) {
            echo json_encode(['success' => false, 'error' => 'File too large: ' . ($fileSize / 1048576) . 'MB']);
            exit;
        }

        // Kiểm tra kích thước hình ảnh
        $imageInfo = getimagesize($fileTmpName);
        if ($imageInfo[0] < 300 || $imageInfo[1] < 300) {
            echo json_encode(['success' => false, 'error' => 'Image dimensions too small: ' . $imageInfo[0] . 'x' . $imageInfo[1]]);
            exit;
        }

        // Ghi log thông tin file để debug
        error_log('Processing file: ' . $fileName . ', Size: ' . $fileSize . ' bytes, Type: ' . $fileType);

        // Kiểm tra quyền ghi vào thư mục
        if (!is_writable($tempUploadDir)) {
            echo json_encode(['success' => false, 'error' => 'Temp directory is not writable: ' . $tempUploadDir]);
            exit;
        }

        // Tạo tên file mới
        $newFileName = 'temp_' . time() . '_' . mt_rand(1000, 9999) . '.jpg';
        $targetFile = $tempUploadDir . $newFileName;

        // Upload file
        if (move_uploaded_file($fileTmpName, $targetFile)) {
            echo json_encode(['success' => true, 'filename' => $newFileName]);
        } else {
            $lastError = error_get_last();
            $errorMsg = $lastError ? $lastError['message'] : 'Unknown error';
            echo json_encode(['success' => false, 'error' => 'Failed to upload file: ' . $errorMsg]);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Exception: ' . $e->getMessage()]);
    }
    exit;
}

// Xử lý xóa file tạm thời
if ($action == 'remove_temp') {
    // Đặt header JSON
    header('Content-Type: application/json');

    try {
        // Kiểm tra tên file
        if (!isset($_POST['filename'])) {
            echo json_encode(['success' => false, 'error' => 'No filename provided']);
            exit;
        }

        $filename = $_POST['filename'];

        // Kiểm tra tên file hợp lệ (chỉ cho phép xóa file trong thư mục temp)
        if (!preg_match('/^temp_\d+_\d+\.jpg$/', $filename)) {
            echo json_encode(['success' => false, 'error' => 'Invalid filename: ' . $filename]);
            exit;
        }

        $filePath = $tempUploadDir . $filename;

        // Kiểm tra xem file có tồn tại không
        if (!file_exists($filePath)) {
            echo json_encode(['success' => true, 'message' => 'File not found, already deleted']);
            exit;
        }

        // Xóa file
        if (unlink($filePath)) {
            echo json_encode(['success' => true]);
        } else {
            $lastError = error_get_last();
            $errorMsg = $lastError ? $lastError['message'] : 'Unknown error';
            echo json_encode(['success' => false, 'error' => 'Failed to delete file: ' . $errorMsg]);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Exception: ' . $e->getMessage()]);
    }
    exit;
}

// Nếu không có action hợp lệ
header('Content-Type: application/json');
echo json_encode(['success' => false, 'error' => 'Invalid action']);
exit;
