<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Contact Protection - <PERSON><PERSON><PERSON> Nẵng</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/thuenhadanang/public/css/style.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            padding: 2rem 0;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
        }
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        .test-info {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
        .test-results {
            margin-top: 2rem;
            padding: 1rem;
            background: #f5f5f5;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="test-card">
                <h2 class="test-title">Test Contact Protection</h2>
                
                <div class="test-info">
                    <strong>Hướng dẫn test:</strong><br>
                    1. Click vào nút "Nhấn để hiện số" để xem số điện thoại<br>
                    2. Click lần 2 để gọi điện<br>
                    3. Click vào nút "Chat Zalo" để mở Zalo<br>
                    4. Kiểm tra source code - số điện thoại được mã hóa base64
                </div>

                <!-- Test Contact Buttons -->
                <div class="d-grid gap-2">
                    <!-- Phone Button với số được mã hóa -->
                    <div class="btn btn-primary phone-btn" data-phone="MDkwNTEyMzQ1Ng==">
                        <i class="bi bi-telephone"></i> <span class="phone-placeholder">Nhấn để hiện số</span>
                    </div>
                    
                    <!-- Zalo Button với số được mã hóa -->
                    <div class="btn btn-outline-primary zalo-btn" data-zalo="MDkwNTEyMzQ1Ng==">
                        <i class="bi bi-chat"></i> Chat Zalo
                    </div>
                    
                    <!-- Email Button (không mã hóa) -->
                    <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                        <i class="bi bi-envelope"></i> Gửi email
                    </a>
                </div>

                <div class="test-results">
                    <h6>Thông tin test:</h6>
                    <ul class="mb-0">
                        <li><strong>Số điện thoại gốc:</strong> 0905123456</li>
                        <li><strong>Mã hóa base64:</strong> MDkwNTEyMzQ1Ng==</li>
                        <li><strong>Bảo vệ:</strong> Số chỉ hiển thị khi user click</li>
                        <li><strong>Crawl protection:</strong> Bot không thể lấy số từ HTML</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="/thuenhadanang/public/js/contact-protection.js"></script>
</body>
</html>
