<?php
/**
 * Test Mobile API Response
 * Check what mobile-search-simple.php returns
 */

echo "<h2>🔍 Mobile API Response Test</h2>";

// Test parameters
$testParams = [
    'type' => 'can-ho',
    'ward' => 'an-hai-bac', 
    'price' => '15+',
    'bedrooms' => '2',
    'sort' => 'default'
];

$queryString = http_build_query($testParams);
$apiUrl = "http://localhost/thuenhadanang/api/mobile-search-simple.php?$queryString";

echo "<h3>Testing URL:</h3>";
echo "<p><code>$apiUrl</code></p>";

echo "<h3>Parameters:</h3>";
echo "<pre>" . print_r($testParams, true) . "</pre>";

// Test API call
echo "<h3>API Response:</h3>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'X-Requested-With: XMLHttpRequest',
    'X-Mobile-Search: true'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
$error = curl_error($ch);
curl_close($ch);

echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
echo "<p><strong>Content Type:</strong> $contentType</p>";

if ($error) {
    echo "<p style='color: red;'><strong>cURL Error:</strong> $error</p>";
}

if ($response) {
    echo "<p><strong>Response Length:</strong> " . strlen($response) . " bytes</p>";
    
    // Check if it's JSON
    $json = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<p style='color: green;'><strong>✅ Valid JSON Response</strong></p>";
        
        // Check response structure
        if (isset($json['success'])) {
            echo "<p><strong>Success:</strong> " . ($json['success'] ? 'true' : 'false') . "</p>";
            
            if ($json['success'] && isset($json['data'])) {
                echo "<p><strong>Properties Count:</strong> " . (isset($json['data']['count']) ? $json['data']['count'] : 'N/A') . "</p>";
                
                if (isset($json['data']['metadata']['url'])) {
                    echo "<p><strong>Generated URL:</strong> <code>" . htmlspecialchars($json['data']['metadata']['url']) . "</code></p>";
                } else {
                    echo "<p style='color: orange;'><strong>⚠️ No metadata.url found!</strong></p>";
                }
                
                if (isset($json['data']['metadata']['title'])) {
                    echo "<p><strong>Generated Title:</strong> " . htmlspecialchars($json['data']['metadata']['title']) . "</p>";
                }
            } else if (!$json['success'] && isset($json['error'])) {
                echo "<p style='color: red;'><strong>API Error:</strong> " . htmlspecialchars($json['error']['message']) . "</p>";
            }
        }
        
        echo "<h4>Full Response:</h4>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 400px; overflow-y: auto;'>";
        echo htmlspecialchars(json_encode($json, JSON_PRETTY_PRINT));
        echo "</pre>";
    } else {
        echo "<p style='color: red;'><strong>❌ Invalid JSON Response</strong></p>";
        echo "<p><strong>JSON Error:</strong> " . json_last_error_msg() . "</p>";
        echo "<pre style='background: #ffe6e6; padding: 10px; border-radius: 5px; max-height: 400px; overflow-y: auto;'>";
        echo htmlspecialchars($response);
        echo "</pre>";
    }
} else {
    echo "<p style='color: red;'><strong>❌ No Response</strong></p>";
}

// Test direct include
echo "<hr>";
echo "<h3>Direct Include Test:</h3>";

try {
    // Simulate the API call directly
    $_GET = $testParams;
    $_GET['_t'] = time();
    
    ob_start();
    include __DIR__ . '/api/mobile-search-simple.php';
    $directResponse = ob_get_clean();
    
    echo "<p><strong>Direct Response Length:</strong> " . strlen($directResponse) . " bytes</p>";
    
    $directJson = json_decode($directResponse, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<p style='color: green;'><strong>✅ Direct include works</strong></p>";
        
        if (isset($directJson['data']['metadata']['url'])) {
            echo "<p><strong>Direct URL:</strong> <code>" . htmlspecialchars($directJson['data']['metadata']['url']) . "</code></p>";
        }
    } else {
        echo "<p style='color: red;'><strong>❌ Direct include failed</strong></p>";
        echo "<p><strong>JSON Error:</strong> " . json_last_error_msg() . "</p>";
        echo "<pre style='background: #ffe6e6; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars($directResponse);
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Direct include error:</strong> " . $e->getMessage() . "</p>";
}

// Test UrlHandler directly
echo "<hr>";
echo "<h3>UrlHandler Test:</h3>";

try {
    if (!defined('BASE_PATH')) {
        define('BASE_PATH', __DIR__);
    }
    
    require_once __DIR__ . '/app/libraries/UrlHandler.php';
    
    $urlHandler = new UrlHandler();
    
    $urlParams = [
        'type' => 'can-ho',
        'ward' => 'an-hai-bac',
        'price' => '15+',
        'bedrooms' => '2'
    ];
    
    $generatedUrl = $urlHandler->buildUrl($urlParams);
    echo "<p><strong>UrlHandler Generated URL:</strong> <code>" . htmlspecialchars($generatedUrl) . "</code></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ UrlHandler error:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
